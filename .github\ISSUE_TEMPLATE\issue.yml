name: Question or bug report
description: Submit a question or bug report to help us improve aider
labels: []
body:
  - type: textarea
    attributes:
      label: Issue
      description: Please describe your problem or question.
    validations:
      required: true      
  - type: textarea
    attributes:
      label: Version and model info
      description: Please include aider version, model being used (`gpt-4-xxx`, etc) and any other switches or config settings that are active.
      placeholder: |          
        Aider v0.XX.Y
        Model: gpt-N-... using ???? edit format
        Git repo: .git with ### files
        Repo-map: using #### tokens
    validations:
      required: false