"""
记忆分析器 - 使用AI分析和分类记忆内容
"""

import logging
import re
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class MemoryAnalyzer:
    """
    记忆分析器
    
    使用AI分析用户的行为、代码、决策等，自动分类并存储到合适的记忆系统中
    """
    
    def __init__(self):
        """初始化记忆分析器"""
        logger.info("MemoryAnalyzer initialized")
    
    def analyze_task_completion(self, task: Dict[str, Any], result: Dict[str, Any]) -> Tuple[List[Dict], List[Dict]]:
        """
        分析任务完成情况，提取可学习的模式
        
        Args:
            task: 任务信息
            result: 执行结果
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (全局记忆项, 项目记忆项)
        """
        global_memories = []
        project_memories = []
        
        # 分析任务类型和解决方案
        task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")
        title = task.get("title", "")
        description = task.get("description", "")
        
        # 提取工作习惯
        habits = self._extract_work_habits(task, result)
        global_memories.extend(habits)
        
        # 提取编码模式
        coding_patterns = self._extract_coding_patterns(task, result)
        if self._is_project_specific(coding_patterns):
            project_memories.extend(coding_patterns)
        else:
            global_memories.extend(coding_patterns)
        
        # 提取技术偏好
        tech_preferences = self._extract_tech_preferences(task, result)
        global_memories.extend(tech_preferences)
        
        # 提取项目特定信息
        project_info = self._extract_project_info(task, result)
        project_memories.extend(project_info)
        
        logger.info(f"Analyzed task completion: {len(global_memories)} global, {len(project_memories)} project memories")
        return global_memories, project_memories
    
    def analyze_user_feedback(self, feedback: str, context: Dict[str, Any]) -> Tuple[List[Dict], List[Dict]]:
        """
        分析用户反馈，提取偏好和改进点
        
        Args:
            feedback: 用户反馈内容
            context: 反馈上下文
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (全局记忆项, 项目记忆项)
        """
        global_memories = []
        project_memories = []
        
        # 分析反馈情感和内容
        sentiment = self._analyze_sentiment(feedback)
        preferences = self._extract_preferences_from_feedback(feedback)
        
        # 根据反馈类型分类
        if self._is_global_feedback(feedback, context):
            global_memories.extend(preferences)
        else:
            project_memories.extend(preferences)
        
        # 记录反馈模式
        feedback_pattern = {
            "type": "user_feedback",
            "sentiment": sentiment,
            "content": feedback,
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
        
        global_memories.append({
            "category": "feedback_patterns",
            "data": feedback_pattern
        })
        
        logger.info(f"Analyzed user feedback: {len(global_memories)} global, {len(project_memories)} project memories")
        return global_memories, project_memories
    
    def analyze_code_changes(self, changes: List[Dict[str, Any]], project_context: Dict[str, Any]) -> Tuple[List[Dict], List[Dict]]:
        """
        分析代码变更，提取编码习惯和模式
        
        Args:
            changes: 代码变更列表
            project_context: 项目上下文
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (全局记忆项, 项目记忆项)
        """
        global_memories = []
        project_memories = []
        
        for change in changes:
            # 分析命名习惯
            naming_habits = self._analyze_naming_patterns(change)
            global_memories.extend(naming_habits)
            
            # 分析代码结构
            structure_patterns = self._analyze_code_structure(change)
            project_memories.extend(structure_patterns)
            
            # 分析注释习惯
            comment_habits = self._analyze_comment_patterns(change)
            global_memories.extend(comment_habits)
        
        logger.info(f"Analyzed code changes: {len(global_memories)} global, {len(project_memories)} project memories")
        return global_memories, project_memories
    
    def _extract_work_habits(self, task: Dict, result: Dict) -> List[Dict]:
        """提取工作习惯"""
        habits = []
        
        # 分析任务处理偏好
        if result.get("status") == "success":
            task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")
            
            habit = {
                "category": "work_habits",
                "type": "task_preference",
                "data": {
                    "description": f"成功处理 {task_type} 类型任务",
                    "task_type": task_type,
                    "success_pattern": self._extract_success_pattern(result),
                    "frequency": 1
                }
            }
            habits.append(habit)
        
        # 分析工具使用偏好
        if "aider" in str(result).lower():
            habit = {
                "category": "work_habits", 
                "type": "tool_preference",
                "data": {
                    "description": "偏好使用Aider进行代码处理",
                    "tool": "aider",
                    "context": task.get("analysis", {}).get("task_type", "UNKNOWN"),
                    "frequency": 1
                }
            }
            habits.append(habit)
        
        return habits
    
    def _extract_coding_patterns(self, task: Dict, result: Dict) -> List[Dict]:
        """提取编码模式"""
        patterns = []
        
        # 分析文件创建模式
        if "创建" in str(result) or "create" in str(result).lower():
            pattern = {
                "category": "coding_patterns",
                "type": "file_creation",
                "data": {
                    "description": "文件创建模式",
                    "pattern": self._extract_file_pattern(result),
                    "context": task.get("title", "")
                }
            }
            patterns.append(pattern)
        
        # 分析代码组织模式
        if "目录" in str(result) or "directory" in str(result).lower():
            pattern = {
                "category": "coding_patterns",
                "type": "directory_structure", 
                "data": {
                    "description": "目录结构组织模式",
                    "pattern": self._extract_directory_pattern(result),
                    "context": task.get("title", "")
                }
            }
            patterns.append(pattern)
        
        return patterns
    
    def _extract_tech_preferences(self, task: Dict, result: Dict) -> List[Dict]:
        """提取技术偏好"""
        preferences = []
        
        # 分析语言偏好
        languages = self._detect_languages(task, result)
        for lang in languages:
            pref = {
                "category": "preferences",
                "type": "programming_languages",
                "data": {
                    "language": lang,
                    "usage_context": task.get("analysis", {}).get("task_type", "UNKNOWN"),
                    "frequency": 1
                }
            }
            preferences.append(pref)
        
        # 分析框架偏好
        frameworks = self._detect_frameworks(task, result)
        for framework in frameworks:
            pref = {
                "category": "preferences",
                "type": "frameworks",
                "data": {
                    "framework": framework,
                    "usage_context": task.get("title", ""),
                    "frequency": 1
                }
            }
            preferences.append(pref)
        
        return preferences
    
    def _extract_project_info(self, task: Dict, result: Dict) -> List[Dict]:
        """提取项目特定信息"""
        project_info = []
        
        # 提取架构决策
        if "架构" in str(task) or "architecture" in str(task).lower():
            decision = {
                "category": "architecture",
                "data": {
                    "title": task.get("title", ""),
                    "description": task.get("description", ""),
                    "decision": self._extract_decision(result),
                    "rationale": self._extract_rationale(result)
                }
            }
            project_info.append(decision)
        
        # 提取编码规范
        standards = self._extract_coding_standards(task, result)
        project_info.extend(standards)
        
        return project_info
    
    def _is_project_specific(self, items: List[Dict]) -> bool:
        """判断是否为项目特定内容"""
        # 简单的判断逻辑，可以根据需要改进
        for item in items:
            data = item.get("data", {})
            if any(keyword in str(data).lower() for keyword in ["项目", "project", "架构", "architecture"]):
                return True
        return False
    
    def _is_global_feedback(self, feedback: str, context: Dict) -> bool:
        """判断是否为全局反馈"""
        global_keywords = ["总是", "通常", "偏好", "习惯", "一般", "always", "usually", "prefer", "habit"]
        return any(keyword in feedback.lower() for keyword in global_keywords)
    
    def _analyze_sentiment(self, text: str) -> str:
        """分析情感倾向"""
        positive_words = ["好", "棒", "优秀", "满意", "喜欢", "good", "great", "excellent", "satisfied", "like"]
        negative_words = ["差", "糟糕", "不好", "不满意", "讨厌", "bad", "terrible", "poor", "unsatisfied", "hate"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"
    
    def _extract_preferences_from_feedback(self, feedback: str) -> List[Dict]:
        """从反馈中提取偏好"""
        preferences = []
        
        # 简单的偏好提取逻辑
        if "偏好" in feedback or "prefer" in feedback.lower():
            pref = {
                "category": "preferences",
                "type": "user_preference",
                "data": {
                    "description": feedback,
                    "extracted_at": datetime.now().isoformat()
                }
            }
            preferences.append(pref)
        
        return preferences
    
    def _analyze_naming_patterns(self, change: Dict) -> List[Dict]:
        """分析命名模式"""
        patterns = []
        
        # 这里可以添加更复杂的命名模式分析
        # 目前返回空列表
        return patterns
    
    def _analyze_code_structure(self, change: Dict) -> List[Dict]:
        """分析代码结构"""
        patterns = []
        
        # 这里可以添加代码结构分析
        # 目前返回空列表
        return patterns
    
    def _analyze_comment_patterns(self, change: Dict) -> List[Dict]:
        """分析注释模式"""
        patterns = []
        
        # 这里可以添加注释模式分析
        # 目前返回空列表
        return patterns
    
    def _extract_success_pattern(self, result: Dict) -> str:
        """提取成功模式"""
        return result.get("report", "")[:100]  # 取前100个字符作为模式
    
    def _extract_file_pattern(self, result: Dict) -> str:
        """提取文件模式"""
        return "文件创建模式"  # 简化实现
    
    def _extract_directory_pattern(self, result: Dict) -> str:
        """提取目录模式"""
        return "目录结构模式"  # 简化实现
    
    def _detect_languages(self, task: Dict, result: Dict) -> List[str]:
        """检测编程语言"""
        languages = []
        text = f"{task} {result}".lower()
        
        lang_patterns = {
            "python": ["python", ".py", "pip", "django", "flask"],
            "javascript": ["javascript", "js", ".js", "node", "npm"],
            "java": ["java", ".java", "maven", "gradle"],
            "go": ["golang", "go", ".go"],
            "rust": ["rust", ".rs", "cargo"],
            "typescript": ["typescript", ".ts", "tsc"]
        }
        
        for lang, patterns in lang_patterns.items():
            if any(pattern in text for pattern in patterns):
                languages.append(lang)
        
        return languages
    
    def _detect_frameworks(self, task: Dict, result: Dict) -> List[str]:
        """检测框架"""
        frameworks = []
        text = f"{task} {result}".lower()
        
        framework_patterns = ["django", "flask", "fastapi", "react", "vue", "angular", "spring", "express"]
        
        for framework in framework_patterns:
            if framework in text:
                frameworks.append(framework)
        
        return frameworks
    
    def _extract_decision(self, result: Dict) -> str:
        """提取决策内容"""
        return result.get("report", "")[:200]  # 简化实现
    
    def _extract_rationale(self, result: Dict) -> str:
        """提取决策理由"""
        return "基于任务执行结果的决策"  # 简化实现
    
    def _extract_coding_standards(self, task: Dict, result: Dict) -> List[Dict]:
        """提取编码规范"""
        standards = []
        
        # 简化实现，可以根据需要扩展
        if "规范" in str(task) or "standard" in str(task).lower():
            standard = {
                "category": "coding_standards",
                "type": "general",
                "data": {
                    "description": task.get("title", ""),
                    "details": task.get("description", "")
                }
            }
            standards.append(standard)
        
        return standards
