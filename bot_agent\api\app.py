"""
FastAPI 应用 - API 网关的主要入口点
"""

import logging
import os
from typing import Dict

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from bot_agent.service_registry.consul_client import ConsulClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="Bot Agent API",
    description="Bot Agent API 网关，提供统一的接口管理对外调用",
    version="0.1.0",
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 获取环境变量
CONSUL_HOST = os.getenv("CONSUL_HOST", "localhost")
CONSUL_PORT = int(os.getenv("CONSUL_PORT", "8500"))
SERVICE_NAME = os.getenv("SERVICE_NAME", "bot-agent")
SERVICE_PORT = int(os.getenv("SERVICE_PORT", "8000"))

# 创建 Consul 客户端
consul_client = ConsulClient(
    host=CONSUL_HOST,
    port=CONSUL_PORT,
    service_name=SERVICE_NAME,
    service_port=SERVICE_PORT,
)


@app.on_event("startup")
async def startup_event():
    """应用启动时执行的操作"""
    # 注册服务到 Consul
    if consul_client.register_service():
        logger.info("Service registered with Consul ")
    else:
        logger.warning("Failed to register service with Consul")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行的操作"""
    # 从 Consul 注销服务
    if consul_client.deregister_service():
        logger.info("Service deregistered from Consul")
    else:
        logger.warning("Failed to deregister service from Consul")


@app.get("/health")
async def health_check() -> Dict[str, str]:
    """健康检查端点"""
    return {"status": "healthy"}


@app.get("/")
async def root() -> Dict[str, str]:
    """API 根端点"""
    return {
        "message": "Welcome to Bot Agent API",
        "version": "0.1.0",
        "service": SERVICE_NAME,
    }


@app.get("/debug")
async def debug() -> Dict:
    """调试端点，显示应用信息"""
    # 获取所有路由
    routes = []
    for route in app.routes:
        routes.append({
            "path": route.path,
            "name": getattr(route, "name", None),
            "methods": list(getattr(route, "methods", ["GET"])),
        })

    # 获取所有中间件
    middlewares = []
    for middleware in app.user_middleware:
        middlewares.append(str(middleware.__class__.__name__))

    return {
        "app_info": {
            "title": app.title,
            "version": app.version,
            "description": app.description,
        },
        "routes": routes,
        "middlewares": middlewares,
        "environment": {
            "consul_host": CONSUL_HOST,
            "consul_port": CONSUL_PORT,
            "service_name": SERVICE_NAME,
            "service_port": SERVICE_PORT,
        }
    }


# 直接添加 GitLab webhook 路由
@app.post("/webhook/gitlab/")
async def gitlab_webhook_direct(request: Request) -> Dict:
    """
    直接处理 GitLab Webhook 事件，然后转发到处理函数

    Args:
        request: FastAPI 请求对象

    Returns:
        Dict: 处理结果
    """
    import logging
    logger = logging.getLogger(__name__)

    logger.info("=== 直接的 GitLab webhook 处理函数被访问 ===")
    logger.info(f"请求方法: {request.method}")
    logger.info(f"请求路径: {request.url.path}")
    logger.info(f"请求头: {dict(request.headers)}")

    try:
        # 导入处理函数
        try:
            from bot_agent.webhook.gitlab import process_gitlab_webhook

            # 调用处理函数
            logger.info("调用 GitLab webhook 处理函数")
            result = await process_gitlab_webhook(request)
            logger.info(f"处理函数返回结果: {result}")
            return result
        except Exception as e:
            logger.error(f"调用处理函数时出错: {e}", exc_info=True)

            # 如果导入或调用失败，尝试直接处理请求
            payload = await request.json()
            event_type = request.headers.get("X-Gitlab-Event")
            object_kind = payload.get("object_kind", "unknown")
            action = payload.get("object_attributes", {}).get("action", "unknown")

            logger.info(f"直接处理 GitLab webhook 事件: {event_type}, {object_kind}, {action}")

            # 返回基本响应
            return {
                "status": "partial_success",
                "message": "GitLab webhook 请求已接收，但处理函数调用失败",
                "error": str(e),
                "event_type": event_type,
                "object_kind": object_kind,
                "action": action
            }
    except Exception as e:
        logger.error(f"处理 GitLab webhook 请求时出错: {e}", exc_info=True)
        return {
            "status": "error",
            "message": f"处理请求时出错: {str(e)}"
        }


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP 异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error"},
    )
