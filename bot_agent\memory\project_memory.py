"""
项目记忆管理器 - 管理特定项目的特点、规范和历史
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ProjectMemoryManager:
    """
    项目记忆管理器
    
    负责管理特定项目的代码规范、架构模式、历史决策等
    存储在项目的docs/memory目录下
    """
    
    def __init__(self, project_path: str):
        """
        初始化项目记忆管理器
        
        Args:
            project_path: 项目根目录路径
        """
        self.project_path = Path(project_path)
        self.memory_dir = self.project_path / "docs" / "memory"
        self.memory_dir.mkdir(parents=True, exist_ok=True)
        
        # 记忆文件路径
        self.architecture_file = self.memory_dir / "architecture.json"
        self.coding_standards_file = self.memory_dir / "coding_standards.json"
        self.decisions_file = self.memory_dir / "decisions.json"
        self.patterns_file = self.memory_dir / "project_patterns.json"
        self.dependencies_file = self.memory_dir / "dependencies.json"
        self.team_preferences_file = self.memory_dir / "team_preferences.json"
        
        logger.info(f"ProjectMemoryManager initialized for project: {self.project_path}")
    
    def load_memory(self) -> Dict[str, Any]:
        """
        加载项目记忆
        
        Returns:
            Dict: 包含所有项目记忆的字典
        """
        memory = {
            "architecture": self._load_json_file(self.architecture_file),
            "coding_standards": self._load_json_file(self.coding_standards_file),
            "decisions": self._load_json_file(self.decisions_file),
            "project_patterns": self._load_json_file(self.patterns_file),
            "dependencies": self._load_json_file(self.dependencies_file),
            "team_preferences": self._load_json_file(self.team_preferences_file)
        }
        
        logger.info("Project memory loaded")
        return memory
    
    def save_architecture_info(self, arch_data: Dict[str, Any]) -> None:
        """
        保存架构信息
        
        Args:
            arch_data: 架构数据
        """
        architecture = self._load_json_file(self.architecture_file)
        architecture.update(arch_data)
        architecture["updated_at"] = datetime.now().isoformat()
        
        self._save_json_file(self.architecture_file, architecture)
        logger.info("Architecture info saved")
    
    def save_coding_standard(self, standard_type: str, standard_data: Dict[str, Any]) -> None:
        """
        保存编码规范
        
        Args:
            standard_type: 规范类型 (naming, formatting, structure, etc.)
            standard_data: 规范数据
        """
        standards = self._load_json_file(self.coding_standards_file)
        
        if standard_type not in standards:
            standards[standard_type] = []
        
        standard_data["recorded_at"] = datetime.now().isoformat()
        standards[standard_type].append(standard_data)
        
        self._save_json_file(self.coding_standards_file, standards)
        logger.info(f"Coding standard saved: {standard_type}")
    
    def save_decision(self, decision_data: Dict[str, Any]) -> None:
        """
        保存项目决策
        
        Args:
            decision_data: 决策数据
        """
        decisions = self._load_json_file(self.decisions_file)
        
        if "decisions" not in decisions:
            decisions["decisions"] = []
        
        decision_data["id"] = len(decisions["decisions"]) + 1
        decision_data["recorded_at"] = datetime.now().isoformat()
        decisions["decisions"].append(decision_data)
        
        self._save_json_file(self.decisions_file, decisions)
        logger.info("Project decision saved")
    
    def save_project_pattern(self, pattern_type: str, pattern_data: Dict[str, Any]) -> None:
        """
        保存项目特定模式
        
        Args:
            pattern_type: 模式类型
            pattern_data: 模式数据
        """
        patterns = self._load_json_file(self.patterns_file)
        
        if pattern_type not in patterns:
            patterns[pattern_type] = []
        
        pattern_data["recorded_at"] = datetime.now().isoformat()
        patterns[pattern_type].append(pattern_data)
        
        self._save_json_file(self.patterns_file, patterns)
        logger.info(f"Project pattern saved: {pattern_type}")
    
    def save_dependency_info(self, dep_data: Dict[str, Any]) -> None:
        """
        保存依赖信息
        
        Args:
            dep_data: 依赖数据
        """
        dependencies = self._load_json_file(self.dependencies_file)
        dependencies.update(dep_data)
        dependencies["updated_at"] = datetime.now().isoformat()
        
        self._save_json_file(self.dependencies_file, dependencies)
        logger.info("Dependency info saved")
    
    def save_team_preference(self, pref_type: str, pref_data: Dict[str, Any]) -> None:
        """
        保存团队偏好
        
        Args:
            pref_type: 偏好类型
            pref_data: 偏好数据
        """
        preferences = self._load_json_file(self.team_preferences_file)
        
        if pref_type not in preferences:
            preferences[pref_type] = {}
        
        preferences[pref_type].update(pref_data)
        preferences[pref_type]["updated_at"] = datetime.now().isoformat()
        
        self._save_json_file(self.team_preferences_file, preferences)
        logger.info(f"Team preference saved: {pref_type}")
    
    def get_relevant_standards(self, file_type: str) -> List[Dict[str, Any]]:
        """
        获取相关的编码规范
        
        Args:
            file_type: 文件类型 (python, javascript, etc.)
            
        Returns:
            List: 相关的编码规范列表
        """
        standards = self._load_json_file(self.coding_standards_file)
        relevant_standards = []
        
        for standard_type, standard_list in standards.items():
            for standard in standard_list:
                if self._is_standard_relevant(standard, file_type):
                    relevant_standards.append({
                        "type": standard_type,
                        "data": standard
                    })
        
        return relevant_standards
    
    def get_architecture_summary(self) -> str:
        """
        获取架构摘要
        
        Returns:
            str: 架构摘要
        """
        architecture = self._load_json_file(self.architecture_file)
        
        if not architecture:
            return "No architecture information recorded"
        
        summary_parts = []
        
        if "overview" in architecture:
            summary_parts.append(f"Overview: {architecture['overview']}")
        
        if "patterns" in architecture:
            summary_parts.append(f"Patterns: {', '.join(architecture['patterns'])}")
        
        if "technologies" in architecture:
            summary_parts.append(f"Technologies: {', '.join(architecture['technologies'])}")
        
        return "; ".join(summary_parts)
    
    def export_memory_summary(self) -> str:
        """
        导出项目记忆摘要，用于AI参考
        
        Returns:
            str: 格式化的项目记忆摘要
        """
        memory = self.load_memory()
        
        summary = f"# 项目记忆摘要 - {self.project_path.name}\n\n"
        
        # 架构信息
        if memory["architecture"]:
            summary += "## 项目架构\n"
            arch = memory["architecture"]
            for key, value in arch.items():
                if key != "updated_at" and value:
                    summary += f"- {key}: {value}\n"
            summary += "\n"
        
        # 编码规范
        if memory["coding_standards"]:
            summary += "## 编码规范\n"
            for standard_type, standards in memory["coding_standards"].items():
                if standards:
                    summary += f"### {standard_type}\n"
                    for standard in standards[-3:]:  # 最近的3个规范
                        summary += f"- {standard.get('description', 'N/A')}\n"
            summary += "\n"
        
        # 重要决策
        if memory["decisions"] and "decisions" in memory["decisions"]:
            summary += "## 重要决策\n"
            decisions = memory["decisions"]["decisions"]
            for decision in decisions[-5:]:  # 最近的5个决策
                summary += f"- {decision.get('title', 'N/A')}: {decision.get('description', 'N/A')}\n"
            summary += "\n"
        
        # 项目模式
        if memory["project_patterns"]:
            summary += "## 项目模式\n"
            for pattern_type, patterns in memory["project_patterns"].items():
                if patterns:
                    summary += f"### {pattern_type}\n"
                    for pattern in patterns[-3:]:  # 最近的3个模式
                        summary += f"- {pattern.get('name', 'N/A')}: {pattern.get('description', 'N/A')}\n"
            summary += "\n"
        
        # 依赖信息
        if memory["dependencies"]:
            summary += "## 主要依赖\n"
            deps = memory["dependencies"]
            for key, value in deps.items():
                if key != "updated_at" and value:
                    summary += f"- {key}: {value}\n"
            summary += "\n"
        
        # 团队偏好
        if memory["team_preferences"]:
            summary += "## 团队偏好\n"
            for pref_type, prefs in memory["team_preferences"].items():
                if prefs and isinstance(prefs, dict):
                    summary += f"### {pref_type}\n"
                    for key, value in prefs.items():
                        if key != "updated_at" and value:
                            summary += f"- {key}: {value}\n"
            summary += "\n"
        
        return summary
    
    def _load_json_file(self, file_path: Path) -> Dict[str, Any]:
        """加载JSON文件"""
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"Failed to load {file_path}: {e}")
        return {}
    
    def _save_json_file(self, file_path: Path, data: Dict[str, Any]) -> None:
        """保存JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except IOError as e:
            logger.error(f"Failed to save {file_path}: {e}")
    
    def _is_standard_relevant(self, standard: Dict, file_type: str) -> bool:
        """判断编码规范是否与文件类型相关"""
        if "applies_to" in standard:
            return file_type in standard["applies_to"]
        return True  # 默认认为相关
