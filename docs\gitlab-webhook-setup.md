# GitLab Webhook 配置指南

本文档介绍如何在 GitLab 中配置 Webhook，以便将 GitLab 事件发送到 Aider-Plus 服务。

## 前提条件

- 已部署 Aider-Plus 服务
- 具有 GitLab 项目的管理员权限
- 已配置 Aider-Plus 服务的 GitLab Webhook 令牌

## 配置步骤

### 1. 获取 Webhook URL

Aider-Plus 服务的 GitLab Webhook URL 格式如下：

```
http://<服务器地址>:<端口>/webhook/gitlab/
```

例如：
```
http://192.168.123.137:8008/webhook/gitlab/
```

### 2. 在 GitLab 中配置 Webhook

1. 登录 GitLab，进入您的项目
2. 点击左侧菜单的 **设置 > Webhooks**
3. 填写以下信息：
   - **URL**：填写上一步获取的 Webhook URL
   - **Secret Token**：填写与 Aider-Plus 服务配置中的 `GITLAB_WEBHOOK_TOKEN` 相同的令牌
   - **Trigger**：选择需要触发的事件（建议至少选择以下事件）：
     - Issues events
     - Comments
     - Merge request events
     - Push events
     - Tag push events
     - Pipeline events
   - **SSL verification**：如果使用 HTTPS，请启用；如果使用 HTTP，请禁用

4. 点击 **添加 Webhook** 按钮保存配置

### 3. 测试 Webhook

1. 在 GitLab 的 Webhook 列表中，找到刚刚添加的 Webhook
2. 点击 **Test** 按钮，选择一个事件类型进行测试
3. 检查 Aider-Plus 服务的日志，确认是否收到了事件

## 支持的事件类型

Aider-Plus 服务支持以下 GitLab 事件类型：

| 事件类型 | 说明 | 处理方式 |
|---------|------|---------|
| Issue Hook | Issue 创建、更新、关闭等事件 | 分析 Issue 内容，如果包含 AI 相关标签或关键词，则路由到相应组件处理 |
| Note Hook | 评论事件，包括 Issue、MR 等评论 | 检查是否提及了 Bot（如 @aider-bot），如果是则路由到相应组件处理 |
| Merge Request Hook | MR 创建、更新、合并等事件 | 分析 MR 内容，如果包含 AI 代码审查相关标签或关键词，则路由到相应组件处理 |
| Push Hook | 代码推送事件 | 分析提交信息，如果包含 AI 相关关键词或推送到主要分支，则可能触发代码审查 |
| Tag Push Hook | 标签推送事件 | 记录版本标签推送事件 |
| Pipeline Hook | CI/CD 流水线事件 | 记录流水线状态变更事件 |

## 触发 AI 处理的方式

有多种方式可以触发 Aider-Plus 服务的 AI 处理：

1. **Issue 标签**：为 Issue 添加以下标签之一：`ai`、`bot`、`aider`
2. **Issue 标题或描述**：在 Issue 标题或描述中包含关键词：`ai`、`bot`、`aider`
3. **评论中提及 Bot**：在评论中使用 `@aider-bot` 提及 Bot
4. **MR 标签**：为 MR 添加以下标签之一：`ai-review`、`bot-review`、`aider-review`
5. **MR 标题或描述**：在 MR 标题或描述中包含关键词：`ai review`、`bot review`、`aider review`
6. **提交信息**：在提交信息中包含关键词：`ai review`、`bot review`、`aider`

## 环境变量配置

在 Aider-Plus 服务的环境变量中，可以配置以下与 GitLab Webhook 相关的参数：

| 环境变量 | 说明 | 默认值 |
|---------|------|-------|
| GITLAB_WEBHOOK_TOKEN | GitLab Webhook 令牌，用于验证请求的合法性 | ********************token |
| GITLAB_WEBHOOK_ALLOWED_IPS | 允许的 IP 地址列表，用逗号分隔，可以是单个 IP 或 CIDR 格式 | 空（不限制 IP） |
| BOT_NAMES | Bot 名称列表，用逗号分隔，用于检测评论中的提及 | aider-bot |

## 故障排除

如果 Webhook 不工作，请检查以下几点：

1. **检查 Webhook 配置**：确保 URL 和令牌正确
2. **检查网络连接**：确保 GitLab 服务器可以访问 Aider-Plus 服务
3. **检查 Aider-Plus 服务日志**：查看是否有接收到事件的日志记录
4. **检查 GitLab Webhook 历史**：在 GitLab 的 Webhook 页面查看请求历史和响应状态码
5. **检查 IP 限制**：如果配置了 `GITLAB_WEBHOOK_ALLOWED_IPS`，确保 GitLab 服务器的 IP 在允许列表中

如果仍然有问题，请联系系统管理员或查看 Aider-Plus 服务的详细日志。
