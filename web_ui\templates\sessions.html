{% extends "base.html" %}

{% block title %}会话列表 - 对话分析系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-list text-primary"></i>
        会话列表
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshSessions()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData()">
                <i class="fas fa-download"></i> 导出
            </button>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="search-filters">
    <div class="row">
        <div class="col-md-3">
            <label for="taskTypeFilter" class="form-label">任务类型</label>
            <select class="form-select" id="taskTypeFilter" onchange="applyFilters()">
                <option value="">全部类型</option>
                <option value="intelligent_execution">智能执行</option>
                <option value="code_generation">代码生成</option>
                <option value="bug_fix">错误修复</option>
                <option value="analysis">分析任务</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="statusFilter" class="form-label">状态</label>
            <select class="form-select" id="statusFilter" onchange="applyFilters()">
                <option value="">全部状态</option>
                <option value="success">成功</option>
                <option value="failed">失败</option>
                <option value="in_progress">进行中</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="dateFromFilter" class="form-label">开始日期</label>
            <input type="date" class="form-control" id="dateFromFilter" onchange="applyFilters()">
        </div>
        <div class="col-md-3">
            <label for="dateToFilter" class="form-label">结束日期</label>
            <input type="date" class="form-control" id="dateToFilter" onchange="applyFilters()">
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-md-6">
            <label for="searchInput" class="form-label">搜索</label>
            <input type="text" class="form-control" id="searchInput" placeholder="搜索会话标题..." onkeyup="applyFilters()">
        </div>
        <div class="col-md-3">
            <label for="limitFilter" class="form-label">显示数量</label>
            <select class="form-select" id="limitFilter" onchange="applyFilters()">
                <option value="20">20条</option>
                <option value="50" selected>50条</option>
                <option value="100">100条</option>
                <option value="200">200条</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times"></i> 清除过滤
            </button>
        </div>
    </div>
</div>

<!-- 会话列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-comments"></i>
            会话记录
        </h5>
        <span class="badge bg-secondary" id="sessionCount">加载中...</span>
    </div>
    <div class="card-body p-0">
        <div id="sessionsList">
            <div class="loading">
                <div class="spinner"></div>
                <div>加载会话列表...</div>
            </div>
        </div>
    </div>
</div>

<!-- 分页 -->
<nav aria-label="会话分页" class="mt-3">
    <ul class="pagination justify-content-center" id="pagination">
        <!-- 分页按钮将通过JavaScript生成 -->
    </ul>
</nav>
{% endblock %}

{% block extra_js %}
<script>
let currentSessions = [];
let filteredSessions = [];
let currentPage = 1;
const itemsPerPage = 20;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSessions();
    
    // 设置默认日期范围（最近7天）
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('dateToFilter').value = today.toISOString().split('T')[0];
    document.getElementById('dateFromFilter').value = weekAgo.toISOString().split('T')[0];
});

// 加载会话列表
async function loadSessions() {
    try {
        showLoading('sessionsList');
        
        const response = await fetch('/api/sessions?limit=200');
        const data = await response.json();
        
        if (data.success) {
            currentSessions = data.data;
            applyFilters();
        } else {
            showError('sessionsList', data.error || '加载会话失败');
        }
    } catch (error) {
        console.error('加载会话失败:', error);
        showError('sessionsList', '网络错误，请稍后重试');
    }
}

// 应用过滤器
function applyFilters() {
    const taskType = document.getElementById('taskTypeFilter').value;
    const status = document.getElementById('statusFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;
    const searchText = document.getElementById('searchInput').value.toLowerCase();
    
    filteredSessions = currentSessions.filter(session => {
        // 任务类型过滤
        if (taskType && session.task_type !== taskType) return false;
        
        // 状态过滤
        if (status && session.status !== status) return false;
        
        // 日期过滤
        if (dateFrom && session.started_at.split('T')[0] < dateFrom) return false;
        if (dateTo && session.started_at.split('T')[0] > dateTo) return false;
        
        // 搜索过滤
        if (searchText && !session.task_title.toLowerCase().includes(searchText)) return false;
        
        return true;
    });
    
    currentPage = 1;
    updateSessionsList();
    updatePagination();
    updateSessionCount();
}

// 更新会话列表显示
function updateSessionsList() {
    const container = document.getElementById('sessionsList');
    
    if (filteredSessions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-search fa-3x mb-3"></i>
                <div>没有找到匹配的会话</div>
                <small>尝试调整过滤条件</small>
            </div>
        `;
        return;
    }
    
    // 分页
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageSessions = filteredSessions.slice(startIndex, endIndex);
    
    const html = pageSessions.map(session => `
        <div class="session-item p-3 border-bottom" onclick="viewSession('${session.session_id}')">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-1">${session.task_title}</h6>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-light text-dark me-2">${session.task_type}</span>
                        ${getStatusBadge(session.status)}
                    </div>
                </div>
                <div class="col-md-3">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        ${formatTime(session.started_at)}
                    </small>
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-comments"></i>
                        ${session.rounds_count} 轮对话
                    </small>
                </div>
                <div class="col-md-3 text-end">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="event.stopPropagation(); viewSession('${session.session_id}')">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="event.stopPropagation(); copySessionId('${session.session_id}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// 更新分页
function updatePagination() {
    const totalPages = Math.ceil(filteredSessions.length / itemsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = html;
}

// 更新会话数量显示
function updateSessionCount() {
    document.getElementById('sessionCount').textContent = `${filteredSessions.length} 个会话`;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredSessions.length / itemsPerPage);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    updateSessionsList();
    updatePagination();
}

// 查看会话详情
function viewSession(sessionId) {
    window.location.href = `/sessions/${sessionId}`;
}

// 复制会话ID
function copySessionId(sessionId) {
    navigator.clipboard.writeText(sessionId).then(() => {
        // 显示复制成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    会话ID已复制到剪贴板
                </div>
            </div>
        `;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}

// 清除过滤器
function clearFilters() {
    document.getElementById('taskTypeFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';
    document.getElementById('searchInput').value = '';
    applyFilters();
}

// 刷新会话列表
function refreshSessions() {
    loadSessions();
}

// 导出数据
function exportData() {
    const csvContent = "data:text/csv;charset=utf-8," 
        + "会话ID,任务标题,任务类型,状态,开始时间,轮次数\n"
        + filteredSessions.map(session => 
            `"${session.session_id}","${session.task_title}","${session.task_type}","${session.status}","${session.started_at}",${session.rounds_count}`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `conversations_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
{% endblock %}
