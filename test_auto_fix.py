#!/usr/bin/env python3
"""
测试自动修复功能
验证JobFailureAnalyzer是否能够自动执行修复和验证
"""

import os
import sys
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bot_agent.handlers.job_failure_analyzer import global_job_failure_analyzer


async def test_auto_fix_functionality():
    """测试自动修复功能"""
    print("🧪 测试自动修复功能")
    print("=" * 60)
    
    # 模拟一个lint作业失败的场景
    job_id = 704  # 使用之前的Job ID
    project_id = 3
    
    print(f"📋 测试作业: Job {job_id}")
    print(f"🏗️ 项目ID: {project_id}")
    
    try:
        # 执行作业失败分析（包含自动修复）
        print("\n🔍 开始作业失败分析（包含自动修复）...")
        start_time = datetime.now()
        
        analysis_result = await global_job_failure_analyzer.analyze_job_failure(job_id, project_id)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⏱️ 分析耗时: {duration:.2f} 秒")
        
        # 检查分析结果
        print(f"\n📊 分析结果:")
        print(f"  ✅ 成功: {analysis_result.success}")
        print(f"  📝 消息: {analysis_result.message}")
        print(f"  🏷️ 作业名称: {analysis_result.job_name}")
        print(f"  ❌ 失败原因: {analysis_result.failure_reason}")
        
        # 显示错误详情
        print(f"\n🔍 错误详情 ({len(analysis_result.error_details)} 个):")
        for i, error in enumerate(analysis_result.error_details[:5], 1):
            print(f"  {i}. {error}")
        if len(analysis_result.error_details) > 5:
            print(f"  ... 还有 {len(analysis_result.error_details) - 5} 个错误")
        
        # 显示修复命令
        print(f"\n🔧 修复命令 ({len(analysis_result.fix_commands)} 个):")
        for i, command in enumerate(analysis_result.fix_commands[:5], 1):
            print(f"  {i}. {command}")
        if len(analysis_result.fix_commands) > 5:
            print(f"  ... 还有 {len(analysis_result.fix_commands) - 5} 个命令")
        
        # 显示验证步骤
        print(f"\n✅ 验证步骤 ({len(analysis_result.verification_steps)} 个):")
        for i, step in enumerate(analysis_result.verification_steps, 1):
            print(f"  {i}. {step}")
        
        # 检查是否包含执行结果信息
        if "修复执行:" in analysis_result.message:
            print(f"\n🎉 检测到自动修复执行结果!")
            print(f"📋 详细信息: {analysis_result.message}")
            return True
        else:
            print(f"\n⚠️ 未检测到自动修复执行")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_fix_execution_logic():
    """测试修复执行逻辑"""
    print("\n🔧 测试修复执行逻辑")
    print("=" * 60)
    
    try:
        # 模拟修复方案
        fix_plan = {
            'commands': [
                'black /path/to/file1.py',
                'black /path/to/file2.py',
                'black /path/to/file3.py',
                'black /path/to/file4.py',
                'black /path/to/file5.py',
                'black /path/to/file6.py'  # 超过5个，应该触发批量修复
            ],
            'description': '针对 lint 作业的修复方案'
        }
        
        job_info = {
            'name': 'lint',
            'status': 'failed'
        }
        
        # 测试修复命令执行逻辑
        analyzer = global_job_failure_analyzer
        result = await analyzer._execute_fix_commands(fix_plan, job_info, 3)
        
        print(f"📊 修复执行结果:")
        print(f"  状态: {result['status']}")
        print(f"  消息: {result['message']}")
        print(f"  执行的命令数: {len(result['executed_commands'])}")
        
        # 检查是否使用了批量命令
        if 'black .' in result['executed_commands']:
            print(f"  ✅ 正确使用了批量修复命令")
            return True
        else:
            print(f"  ⚠️ 未使用批量修复命令")
            print(f"  执行的命令: {result['executed_commands']}")
            return False
            
    except Exception as e:
        print(f"❌ 修复执行逻辑测试失败: {e}")
        return False


async def test_verification_logic():
    """测试验证逻辑"""
    print("\n✅ 测试验证逻辑")
    print("=" * 60)
    
    try:
        verification_steps = [
            "运行 black --check . 验证格式化",
            "运行 flake8 . 验证代码风格",
            "运行 mypy . 验证类型注解"
        ]
        
        job_info = {
            'name': 'lint',
            'status': 'failed'
        }
        
        # 测试验证执行逻辑
        analyzer = global_job_failure_analyzer
        result = await analyzer._execute_verification(verification_steps, job_info, 3)
        
        print(f"📊 验证执行结果:")
        print(f"  状态: {result['status']}")
        print(f"  消息: {result['message']}")
        print(f"  验证结果数: {len(result['results'])}")
        
        for i, res in enumerate(result['results'], 1):
            print(f"  {i}. {res['command']}: {'✅' if res['success'] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证逻辑测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 自动修复功能测试套件")
    print("=" * 80)
    
    test_results = []
    
    # 运行测试
    tests = [
        ("修复执行逻辑", test_fix_execution_logic),
        ("验证逻辑", test_verification_logic),
        ("完整自动修复功能", test_auto_fix_functionality)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            test_results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            test_results.append((test_name, False))
            print(f"❌ {test_name}: 异常 - {e}")
    
    # 总结测试结果
    print(f"\n{'='*20} 测试总结 {'='*20}")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    success_rate = passed_tests / total_tests * 100
    print(f"\n📊 总体通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 66:
        print("\n🎉 自动修复功能测试成功！")
        print("\n✅ 新增功能:")
        print("  🔧 自动执行修复命令")
        print("  ✅ 自动验证修复效果")
        print("  📊 智能批量处理（超过5个文件时使用 black .）")
        print("  🛡️ 安全限制（最多执行10个命令）")
        print("  📋 详细的执行结果报告")
        
        print("\n🔄 改进的流程:")
        print("  ❌ 之前: 分析 → 建议 → 结束")
        print("  ✅ 现在: 分析 → 建议 → 执行 → 验证 → 反馈")
        
        print("\n💡 解决的问题:")
        print("  ✅ AI不再只是分析问题，而是真正修复问题")
        print("  ✅ 自动验证修复效果，确保问题解决")
        print("  ✅ 智能优化修复策略，提高效率")
        print("  ✅ 提供详细的执行反馈，便于跟踪")
        
        print("\n🚀 使用效果:")
        print("  - 对于lint作业：自动运行 black . 修复格式化问题")
        print("  - 对于test作业：自动安装缺失依赖，修复测试")
        print("  - 对于build作业：自动安装依赖，修复构建问题")
        print("  - 自动验证修复效果，确保问题真正解决")
        
    else:
        print("\n⚠️ 自动修复功能仍需进一步完善")
        print("请检查失败的测试并继续优化")


if __name__ == "__main__":
    asyncio.run(main())
