# aider-plus 长记忆系统

## 概述

长记忆系统是aider-plus的核心功能之一，它能够记录和学习用户的工作习惯、项目特点和偏好设置，让AI助手变得更加智能和个性化。

## 系统架构

### 两级记忆结构

1. **全局记忆** - 存储在 `bot_agent/memory/global/`
   - 工作习惯 (`work_habits.json`)
   - 偏好设置 (`preferences.json`) 
   - 常用模式 (`common_patterns.json`)
   - 环境信息 (`environment.json`)

2. **项目记忆** - 存储在 `项目/docs/memory/`
   - 项目架构 (`architecture.json`)
   - 编码规范 (`coding_standards.json`)
   - 重要决策 (`decisions.json`)
   - 项目模式 (`project_patterns.json`)
   - 依赖信息 (`dependencies.json`)
   - 团队偏好 (`team_preferences.json`)

## 核心组件

### 1. GlobalMemoryManager (全局记忆管理器)
负责管理用户的全局工作习惯和偏好：

```python
from bot_agent.memory.global_memory import GlobalMemoryManager

# 创建全局记忆管理器
global_memory = GlobalMemoryManager()

# 记录工作习惯
global_memory.save_work_habit("coding_style", {
    "description": "偏好使用中文注释",
    "pattern": "中文注释模式",
    "frequency": 1
})

# 记录偏好设置
global_memory.save_preference("tools", {
    "editor": "VSCode",
    "terminal": "PowerShell"
})
```

### 2. ProjectMemoryManager (项目记忆管理器)
负责管理特定项目的规范和特点：

```python
from bot_agent.memory.project_memory import ProjectMemoryManager

# 创建项目记忆管理器
project_memory = ProjectMemoryManager("/path/to/project")

# 记录项目架构
project_memory.save_architecture_info({
    "overview": "微服务架构",
    "patterns": ["API Gateway", "Service Discovery"],
    "technologies": ["FastAPI", "PostgreSQL"]
})

# 记录编码规范
project_memory.save_coding_standard("documentation", {
    "description": "使用中文注释和文档",
    "applies_to": ["python"]
})
```

### 3. MemoryAnalyzer (记忆分析器)
使用AI分析用户行为，自动分类记忆内容：

```python
from bot_agent.memory.memory_analyzer import MemoryAnalyzer

analyzer = MemoryAnalyzer()

# 分析任务完成情况
global_memories, project_memories = analyzer.analyze_task_completion(task, result)

# 分析用户反馈
global_feedback, project_feedback = analyzer.analyze_user_feedback(feedback, context)
```

### 4. MemoryIntegration (记忆集成)
将记忆系统集成到工作流中：

```python
from bot_agent.memory.memory_integration import MemoryIntegration

integration = MemoryIntegration()

# 为任务准备上下文
context = integration.prepare_context_for_task(task, project_path)

# 从任务完成中学习
await integration.learn_from_task_completion(task, result, project_path)
```

## 自动学习机制

### AI智能分类
系统会自动判断什么内容应该存储在全局记忆，什么应该存储在项目记忆：

- **全局记忆**: 工作习惯、工具偏好、通用模式
- **项目记忆**: 项目架构、特定规范、团队约定

### 学习触发点
1. **任务完成时**: 分析任务类型、解决方案、使用的工具
2. **用户反馈时**: 提取偏好信息、改进建议
3. **代码变更时**: 学习编码习惯、命名规范

## 记忆内容示例

### 全局记忆示例

#### 工作习惯
```json
{
  "coding_style": [
    {
      "description": "偏好使用中文注释和文档字符串",
      "pattern": "中文注释模式",
      "examples": ["# 这是中文注释", "\"\"\"这是中文文档字符串\"\"\""],
      "frequency": 5
    }
  ],
  "naming_convention": [
    {
      "description": "使用下划线命名法（snake_case）",
      "pattern": "snake_case",
      "examples": ["user_name", "get_user_info"],
      "frequency": 3
    }
  ]
}
```

#### 偏好设置
```json
{
  "tools": {
    "editor": "VSCode",
    "terminal": "PowerShell",
    "version_control": "GitLab"
  },
  "programming_languages": {
    "primary": "Python",
    "secondary": "JavaScript"
  },
  "frameworks": {
    "web": "FastAPI",
    "database": "PostgreSQL"
  }
}
```

### 项目记忆示例

#### 项目架构
```json
{
  "overview": "AI API代理服务",
  "patterns": ["代理模式", "策略模式"],
  "technologies": ["FastAPI", "asyncio", "httpx"],
  "structure": "分层架构：API层 -> 服务层 -> 数据层"
}
```

#### 编码规范
```json
{
  "documentation": [
    {
      "description": "API文档使用中文描述，代码注释使用中文",
      "applies_to": ["python"],
      "examples": [
        "def get_user_info(user_id: int) -> dict:",
        "    \"\"\"获取用户信息\"\"\""
      ]
    }
  ]
}
```

## 使用效果

### 任务执行时
当执行任务时，AI会自动参考相关记忆：

```
任务类型: CODE_GENERATION
任务标题: 创建用户认证API

# 全局工作记忆
## 相关工作习惯
- coding_style: 偏好使用中文注释 (频率: 5)
- naming_convention: 使用下划线命名法 (频率: 3)

## 偏好设置
tools: editor: VSCode, terminal: PowerShell
frameworks: web: FastAPI, database: PostgreSQL

# 项目记忆
## 项目架构
Overview: AI API代理服务; Technologies: FastAPI, asyncio, httpx

## python 编码规范
- API文档使用中文描述，代码注释使用中文

执行指令：
请立即开始实现上述功能，参考上述记忆中的工作习惯和项目规范...
```

### 持续改进
- 记忆会根据使用频率自动排序
- 相似的习惯会自动合并
- 过时的记忆会逐渐淡化

## 文件结构

```
aider-plus/
├── bot_agent/memory/global/          # 全局记忆
│   ├── work_habits.json             # 工作习惯
│   ├── preferences.json             # 偏好设置
│   ├── common_patterns.json         # 常用模式
│   └── environment.json             # 环境信息
│
└── 项目目录/docs/memory/             # 项目记忆
    ├── architecture.json            # 项目架构
    ├── coding_standards.json        # 编码规范
    ├── decisions.json               # 重要决策
    ├── project_patterns.json        # 项目模式
    ├── dependencies.json            # 依赖信息
    └── team_preferences.json        # 团队偏好
```

## 配置和自定义

### 环境变量
- `MEMORY_ENABLED`: 是否启用记忆系统 (默认: true)
- `GLOBAL_MEMORY_DIR`: 全局记忆目录 (默认: bot_agent/memory/global)
- `PROJECT_MEMORY_DIR`: 项目记忆目录名 (默认: docs/memory)

### 自定义记忆类型
可以通过扩展MemoryAnalyzer来添加新的记忆类型和分析逻辑。

## 最佳实践

1. **定期检查记忆内容**: 确保记录的习惯和偏好是最新的
2. **提供明确反馈**: 通过评论和反馈帮助系统学习
3. **项目文档化**: 在项目开始时记录架构决策和规范
4. **团队协作**: 确保团队成员了解项目记忆的内容

## 隐私和安全

- 记忆文件存储在本地，不会上传到外部服务
- 敏感信息（如密钥、密码）不会被记录
- 可以通过配置控制记忆的范围和内容

## 故障排除

### 常见问题
1. **记忆文件损坏**: 删除对应的JSON文件，系统会重新创建
2. **记忆内容错误**: 手动编辑JSON文件或使用API重置
3. **性能问题**: 定期清理过时的记忆内容

### 调试模式
设置环境变量 `MEMORY_DEBUG=true` 启用详细日志。
