{% extends "base.html" %}

{% block title %}分析链路演示 - 对话分析系统{% endblock %}

{% block extra_css %}
<link href="/static/css/analysis_chain.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-link text-primary"></i>
        分析链路演示
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadDemoData()">
                <i class="fas fa-play"></i> 加载演示数据
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearDemo()">
                <i class="fas fa-trash"></i> 清空
            </button>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle"></i>
    <strong>功能说明：</strong>
    这是新的结构化分析链路展示系统，将传统的对话记录转换为清晰的问题解决思维链：
    <strong>问题识别 → 解决方案 → 执行过程 → 结果评估</strong>
</div>

<!-- 分析链路容器 -->
<div id="analysisChainContainer">
    <div class="text-center text-muted py-5">
        <i class="fas fa-link fa-3x mb-3"></i>
        <div>点击"加载演示数据"查看分析链路效果</div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="/static/js/analysis_chain.js"></script>
<script>
let analysisChain = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    analysisChain = new AnalysisChain('analysisChainContainer');
});

// 加载演示数据
function loadDemoData() {
    const demoSession = {
        session_id: "demo_session_001",
        task_id: "demo_task_001", 
        task_title: "修复用户登录功能的验证码显示问题",
        task_type: "BUG_FIX",
        project_path: "/project/user-auth-system",
        started_at: "2025-01-15T10:30:00Z",
        ended_at: "2025-01-15T10:45:30Z",
        total_duration: 930,
        status: "success",
        final_result: "成功修复验证码显示问题，用户可以正常登录",
        metadata: {
            task_analysis: {
                task_type: "BUG_FIX",
                confidence: 95,
                estimated_time: "15-20分钟",
                keywords: ["验证码", "登录", "显示问题", "前端"],
                risks: ["可能影响现有用户会话", "需要测试多种浏览器兼容性"],
                ai_reasoning: "这是一个前端显示问题，可能涉及CSS样式或JavaScript逻辑错误"
            }
        },
        rounds: [
            {
                round_number: 1,
                round_name: "问题分析",
                prompt: "用户反馈登录页面的验证码图片无法显示，请帮我分析可能的原因并提供解决方案。",
                response: "我来帮您分析验证码显示问题。可能的原因包括：\n1. 验证码生成接口异常\n2. 前端图片路径错误\n3. CSS样式问题\n4. 浏览器缓存问题\n\n让我先检查验证码生成接口的状态...",
                model_name: "deepseek/deepseek-r1:free",
                timestamp: "2025-01-15T10:30:15Z",
                duration: 3.2,
                status: "success",
                retry_count: 0,
                token_usage: {
                    prompt_tokens: 45,
                    completion_tokens: 120,
                    total_tokens: 165
                }
            },
            {
                round_number: 2,
                round_name: "代码检查",
                prompt: "请检查验证码相关的前端代码，特别是图片显示部分。",
                response: "检查前端代码后发现问题：\n\n```javascript\n// 问题代码\nconst captchaImg = document.getElementById('captcha-img');\ncaptchaImg.src = '/api/captcha?t=' + Date.now();\n```\n\n问题在于图片元素的CSS样式设置了 `display: none`，导致验证码无法显示。需要修改CSS样式。",
                model_name: "deepseek/deepseek-r1:free", 
                timestamp: "2025-01-15T10:32:45Z",
                duration: 5.8,
                status: "success",
                retry_count: 0,
                token_usage: {
                    prompt_tokens: 78,
                    completion_tokens: 156,
                    total_tokens: 234
                }
            },
            {
                round_number: 3,
                round_name: "解决方案实施",
                prompt: "请提供具体的修复代码。",
                response: "以下是修复方案：\n\n```css\n/* 修复验证码显示问题 */\n#captcha-img {\n    display: block;\n    width: 120px;\n    height: 40px;\n    border: 1px solid #ddd;\n    border-radius: 4px;\n}\n```\n\n```javascript\n// 优化验证码刷新逻辑\nfunction refreshCaptcha() {\n    const img = document.getElementById('captcha-img');\n    img.src = '/api/captcha?t=' + Date.now();\n    img.style.display = 'block';\n}\n```",
                model_name: "deepseek/deepseek-r1:free",
                timestamp: "2025-01-15T10:35:20Z", 
                duration: 4.1,
                status: "success",
                retry_count: 0,
                token_usage: {
                    prompt_tokens: 23,
                    completion_tokens: 189,
                    total_tokens: 212
                }
            },
            {
                round_number: 4,
                round_name: "测试验证",
                prompt: "代码已修改，请帮我制定测试方案确保修复有效。",
                response: "测试方案如下：\n\n1. **功能测试**\n   - 刷新登录页面，验证验证码正常显示\n   - 点击验证码图片，确认可以刷新\n   - 输入正确验证码，确认登录流程正常\n\n2. **兼容性测试**\n   - Chrome、Firefox、Safari浏览器测试\n   - 移动端响应式测试\n\n3. **性能测试**\n   - 验证码加载速度测试\n   - 并发用户访问测试\n\n测试完成后确认问题已解决。",
                model_name: "deepseek/deepseek-r1:free",
                timestamp: "2025-01-15T10:42:10Z",
                duration: 6.3,
                status: "success", 
                retry_count: 0,
                token_usage: {
                    prompt_tokens: 34,
                    completion_tokens: 198,
                    total_tokens: 232
                }
            },
            {
                round_number: 5,
                round_name: "结果确认",
                prompt: "测试完成，验证码显示正常，用户可以正常登录了。",
                response: "太好了！问题已成功解决。\n\n**解决方案总结：**\n- 识别问题：CSS样式导致验证码隐藏\n- 修复方法：调整CSS display属性和优化JavaScript逻辑\n- 测试验证：多浏览器兼容性测试通过\n- 结果：用户登录功能恢复正常\n\n建议后续加强前端代码审查，避免类似问题再次发生。",
                model_name: "deepseek/deepseek-r1:free",
                timestamp: "2025-01-15T10:45:30Z",
                duration: 2.8,
                status: "success",
                retry_count: 0,
                token_usage: {
                    prompt_tokens: 28,
                    completion_tokens: 145,
                    total_tokens: 173
                }
            }
        ]
    };

    // 渲染分析链路
    if (analysisChain) {
        analysisChain.renderAnalysisChain(demoSession);
    }
}

// 清空演示
function clearDemo() {
    const container = document.getElementById('analysisChainContainer');
    container.innerHTML = `
        <div class="text-center text-muted py-5">
            <i class="fas fa-link fa-3x mb-3"></i>
            <div>点击"加载演示数据"查看分析链路效果</div>
        </div>
    `;
}
</script>
{% endblock %}
