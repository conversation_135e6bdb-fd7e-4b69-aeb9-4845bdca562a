version: '3.8'

services:
  # 统一服务 (包含 Bot 代理、Aider 和 Augment)
  aider-plus-unified:
    image: ${CI_REGISTRY_IMAGE}-unified:${IMAGE_TAG}
    container_name: aider-plus-unified
    restart: unless-stopped
    ports:
      - "8000:8000"  # Bot 代理服务端口
      - "8080:8080"  # Aider 服务端口
    environment:
      # Bot 代理服务环境变量
      - HOST=0.0.0.0
      - PORT=8000
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key}
      - VALID_API_KEYS=${VALID_API_KEYS:-test-api-key}
      - LOG_LEVEL=INFO
      # Aider 服务环境变量
      - AIDER_PORT=8080
      - AIDER_HOST=0.0.0.0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./workspace:/workspace
    depends_on:
      - consul
      - redis
    networks:
      - aider-network

  # Consul 服务发现
  consul:
    image: ${CONSUL_IMAGE}
    container_name: aider-plus-consul
    restart: unless-stopped
    ports:
      - "8500:8500"
      - "8600:8600/tcp"
      - "8600:8600/udp"
    command: "agent -server -ui -node=server-1 -bootstrap-expect=1 -client=0.0.0.0"
    volumes:
      - ${DATA_STORAGE_DIR}/consul:/consul/data
    networks:
      - aider-network

  # Redis 缓存
  redis:
    image: ${REDIS_IMAGE}
    container_name: aider-plus-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - ${DATA_STORAGE_DIR}/redis:/data
    networks:
      - aider-network

networks:
  aider-network:
    driver: bridge
