"""
认证和授权模块 - 提供 API 认证和授权功能
"""

import logging
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Optional

import jwt
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

logger = logging.getLogger(__name__)

# 从环境变量获取密钥，或使用默认值（仅用于开发环境）
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-for-development-only")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
JWT_EXPIRATION_MINUTES = int(os.getenv("JWT_EXPIRATION_MINUTES", "60"))

# 创建安全依赖
security = HTTPBearer()


def create_access_token(data: Dict) -> str:
    """
    创建 JWT 访问令牌

    Args:
        data: 要编码的数据

    Returns:
        str: JWT 令牌
    """
    to_encode = data.copy()
    
    # 设置过期时间
    expire = datetime.utcnow() + timedelta(minutes=JWT_EXPIRATION_MINUTES)
    to_encode.update({"exp": expire})
    
    # 编码 JWT
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    return encoded_jwt


def decode_token(token: str) -> Dict:
    """
    解码 JWT 令牌

    Args:
        token: JWT 令牌

    Returns:
        Dict: 解码后的数据

    Raises:
        HTTPException: 如果令牌无效或已过期
    """
    try:
        # 解码 JWT
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        # 检查令牌是否已过期
        if payload.get("exp") < time.time():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return payload
    
    except jwt.PyJWTError as e:
        logger.warning(f"Invalid token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> Dict:
    """
    获取当前用户

    Args:
        credentials: HTTP 授权凭证

    Returns:
        Dict: 用户信息

    Raises:
        HTTPException: 如果凭证无效
    """
    token = credentials.credentials
    user = decode_token(token)
    
    return user


async def get_api_key(
    request: Request,
    x_api_key: Optional[str] = None,
) -> str:
    """
    获取 API 密钥

    Args:
        request: FastAPI 请求对象
        x_api_key: HTTP 头中的 API 密钥

    Returns:
        str: API 密钥

    Raises:
        HTTPException: 如果 API 密钥无效
    """
    # 首先检查 HTTP 头
    api_key = x_api_key
    
    # 如果 HTTP 头中没有 API 密钥，则检查查询参数
    if not api_key:
        api_key = request.query_params.get("api_key")
    
    # 如果仍然没有 API 密钥，则抛出异常
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is required",
        )
    
    # 验证 API 密钥
    # 在实际应用中，应该从数据库或配置中获取有效的 API 密钥
    valid_api_keys = os.getenv("VALID_API_KEYS", "test-api-key").split(",")
    
    if api_key not in valid_api_keys:
        logger.warning(f"Invalid API key: {api_key}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
        )
    
    return api_key


def has_permission(
    user: Dict,
    required_permission: str,
) -> bool:
    """
    检查用户是否具有所需权限

    Args:
        user: 用户信息
        required_permission: 所需权限

    Returns:
        bool: 是否具有权限
    """
    # 获取用户权限
    permissions = user.get("permissions", [])
    
    # 检查是否具有所需权限
    return required_permission in permissions


def require_permission(required_permission: str):
    """
    创建权限依赖

    Args:
        required_permission: 所需权限

    Returns:
        Callable: 权限依赖
    """
    
    async def permission_dependency(user: Dict = Depends(get_current_user)):
        if not has_permission(user, required_permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {required_permission} is required",
            )
        return user
    
    return permission_dependency
