#!/usr/bin/env python3
"""
Bot 代理服务启动脚本
"""

import argparse
import logging
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from bot_agent.main import start


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动 Bot 代理服务")
    parser.add_argument(
        "--host",
        help="服务器主机名",
        default=os.getenv("HOST", "0.0.0.0"),
    )
    parser.add_argument(
        "--port",
        help="服务器端口",
        type=int,
        default=int(os.getenv("PORT", "8000")),
    )
    parser.add_argument(
        "--consul-host",
        help="Consul 服务器主机名",
        default=os.getenv("CONSUL_HOST", "localhost"),
    )
    parser.add_argument(
        "--consul-port",
        help="Consul 服务器端口",
        type=int,
        default=int(os.getenv("CONSUL_PORT", "8500")),
    )
    parser.add_argument(
        "--log-level",
        help="日志级别",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default=os.getenv("LOG_LEVEL", "INFO"),
    )
    args = parser.parse_args()

    # 设置环境变量
    os.environ["HOST"] = args.host
    os.environ["PORT"] = str(args.port)
    os.environ["CONSUL_HOST"] = args.consul_host
    os.environ["CONSUL_PORT"] = str(args.consul_port)

    # 配置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 启动服务
    print(f"启动 Bot 代理服务，监听 {args.host}:{args.port}")
    start()


if __name__ == "__main__":
    main()
