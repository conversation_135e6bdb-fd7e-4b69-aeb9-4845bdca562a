"""
Pipeline分析器 - 分析GitLab CI/CD Pipeline的部署状态和问题
"""

import logging
import re
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from bot_agent.clients.gitlab_client import GitLabClient

logger = logging.getLogger(__name__)


@dataclass
class PipelineAnalysis:
    """Pipeline分析结果"""
    pipeline_id: int
    project_id: int
    status: str
    ref: str
    stages: List[str]
    failed_jobs: List[Dict[str, Any]]
    deployment_jobs: List[Dict[str, Any]]
    error_summary: str
    recommendations: List[str]
    logs_analyzed: bool
    health_check_results: Optional[Dict[str, Any]] = None


class PipelineAnalyzer:
    """
    Pipeline分析器
    
    功能：
    1. 分析Pipeline失败原因
    2. 收集相关日志和错误信息
    3. 检查部署状态
    4. 提供修复建议
    5. 执行健康检查
    """
    
    def __init__(self, gitlab_client: GitLabClient = None):
        """初始化Pipeline分析器"""
        self.gitlab_client = gitlab_client or GitLabClient()
        logger.info("PipelineAnalyzer initialized")
    
    async def analyze_pipeline(self, project_id: int, pipeline_id: int) -> PipelineAnalysis:
        """
        分析Pipeline
        
        Args:
            project_id: 项目ID
            pipeline_id: Pipeline ID
            
        Returns:
            PipelineAnalysis: 分析结果
        """
        logger.info(f"开始分析Pipeline {pipeline_id}")
        
        try:
            # 获取Pipeline信息
            pipeline = self.gitlab_client.get_pipeline(project_id, pipeline_id)
            if not pipeline:
                raise Exception(f"无法获取Pipeline {pipeline_id}信息")
            
            # 获取Pipeline作业
            jobs = self.gitlab_client.get_pipeline_jobs(project_id, pipeline_id)
            
            # 分析作业
            failed_jobs = [job for job in jobs if job.get('status') == 'failed']
            deployment_jobs = [job for job in jobs if self._is_deployment_job(job)]
            
            # 收集错误信息
            error_summary = await self._analyze_errors(project_id, failed_jobs)
            
            # 生成建议
            recommendations = await self._generate_recommendations(
                pipeline, failed_jobs, deployment_jobs
            )
            
            # 执行健康检查（如果有部署作业）
            health_check_results = None
            if deployment_jobs and pipeline.get('status') == 'success':
                health_check_results = await self._perform_health_checks(
                    project_id, deployment_jobs
                )
            
            return PipelineAnalysis(
                pipeline_id=pipeline_id,
                project_id=project_id,
                status=pipeline.get('status', 'unknown'),
                ref=pipeline.get('ref', 'unknown'),
                stages=pipeline.get('stages', []),
                failed_jobs=failed_jobs,
                deployment_jobs=deployment_jobs,
                error_summary=error_summary,
                recommendations=recommendations,
                logs_analyzed=len(failed_jobs) > 0,
                health_check_results=health_check_results
            )
            
        except Exception as e:
            logger.error(f"分析Pipeline {pipeline_id}失败: {e}", exc_info=True)
            return PipelineAnalysis(
                pipeline_id=pipeline_id,
                project_id=project_id,
                status="analysis_failed",
                ref="unknown",
                stages=[],
                failed_jobs=[],
                deployment_jobs=[],
                error_summary=f"分析失败: {str(e)}",
                recommendations=["请检查Pipeline配置和权限"],
                logs_analyzed=False
            )
    
    def _is_deployment_job(self, job: Dict) -> bool:
        """判断是否为部署作业"""
        job_name = job.get('name', '').lower()
        stage = job.get('stage', '').lower()
        
        deployment_indicators = [
            'deploy', 'deployment', '部署',
            'release', 'publish', '发布',
            'production', 'staging', 'prod'
        ]
        
        return any(indicator in job_name or indicator in stage 
                  for indicator in deployment_indicators)
    
    async def _analyze_errors(self, project_id: int, failed_jobs: List[Dict]) -> str:
        """分析失败作业的错误"""
        if not failed_jobs:
            return "无失败作业"
        
        error_summaries = []
        
        for job in failed_jobs:
            job_id = job.get('id')
            job_name = job.get('name', 'unknown')
            
            try:
                # 获取作业日志
                logs = self.gitlab_client.get_job_log(project_id, job_id)
                if logs:
                    # 提取关键错误信息
                    key_errors = self._extract_key_errors(logs)
                    if key_errors:
                        error_summaries.append(f"**{job_name}**:\n{key_errors}")
                    else:
                        # 如果没有找到关键错误，提取最后几行
                        last_lines = '\n'.join(logs.strip().split('\n')[-5:])
                        error_summaries.append(f"**{job_name}**:\n```\n{last_lines}\n```")
                else:
                    error_summaries.append(f"**{job_name}**: 无法获取日志")
                    
            except Exception as e:
                error_summaries.append(f"**{job_name}**: 日志获取失败 - {str(e)}")
        
        return '\n\n'.join(error_summaries) if error_summaries else "无法分析错误信息"
    
    def _extract_key_errors(self, logs: str) -> str:
        """从日志中提取关键错误信息"""
        if not logs:
            return ""
        
        # 常见错误模式
        error_patterns = [
            (r'ERROR:\s*(.+)', 'ERROR'),
            (r'Error:\s*(.+)', 'Error'),
            (r'FAILED:\s*(.+)', 'FAILED'),
            (r'Exception:\s*(.+)', 'Exception'),
            (r'Fatal:\s*(.+)', 'Fatal'),
            (r'npm ERR!\s*(.+)', 'NPM Error'),
            (r'docker:\s*(.+)', 'Docker Error'),
            (r'permission denied:\s*(.+)', 'Permission Error'),
            (r'No such file or directory:\s*(.+)', 'File Not Found'),
            (r'command not found:\s*(.+)', 'Command Not Found'),
        ]
        
        found_errors = []
        
        for pattern, error_type in error_patterns:
            matches = re.findall(pattern, logs, re.IGNORECASE | re.MULTILINE)
            for match in matches[-3:]:  # 最多取最后3个匹配
                found_errors.append(f"- {error_type}: {match.strip()}")
        
        if found_errors:
            return '\n'.join(found_errors)
        
        # 如果没有找到特定错误模式，查找包含"error"、"fail"等关键词的行
        lines = logs.split('\n')
        error_lines = []
        
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in ['error', 'fail', 'exception', 'fatal']):
                if len(line.strip()) > 10:  # 忽略太短的行
                    error_lines.append(line.strip())
        
        if error_lines:
            # 取最后几个错误行
            return '\n'.join([f"- {line}" for line in error_lines[-5:]])
        
        return ""
    
    async def _generate_recommendations(self, pipeline: Dict, failed_jobs: List[Dict], 
                                      deployment_jobs: List[Dict]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 基于Pipeline状态的建议
        status = pipeline.get('status')
        
        if status == 'failed':
            recommendations.append("🔍 **立即行动**: Pipeline失败，需要分析和修复")
            
            if failed_jobs:
                recommendations.append(f"📋 **失败作业**: 共有 {len(failed_jobs)} 个作业失败")
                
                # 基于失败作业类型的建议
                job_types = [job.get('stage', 'unknown') for job in failed_jobs]
                
                if 'test' in job_types:
                    recommendations.append("🧪 **测试失败**: 检查单元测试和集成测试，修复失败的测试用例")
                
                if 'build' in job_types:
                    recommendations.append("🔨 **构建失败**: 检查依赖、编译错误和构建配置")
                
                if any(self._is_deployment_job(job) for job in failed_jobs):
                    recommendations.append("🚀 **部署失败**: 检查部署配置、环境变量和服务状态")
                    recommendations.append("⚠️ **回滚建议**: 考虑回滚到上一个稳定版本")
            
        elif status == 'success':
            if deployment_jobs:
                recommendations.append("✅ **部署成功**: 建议进行部署后验证")
                recommendations.append("🔍 **健康检查**: 验证所有服务是否正常运行")
                recommendations.append("📊 **监控**: 密切关注应用性能和错误率")
        
        elif status == 'running':
            recommendations.append("⏳ **进行中**: Pipeline正在运行，持续监控")
            if deployment_jobs:
                recommendations.append("🔄 **部署监控**: 准备在出现问题时快速响应")
        
        # 通用建议
        recommendations.extend([
            "📝 **日志检查**: 查看详细的作业日志以获取更多信息",
            "🔄 **重试机制**: 如果是临时问题，可以考虑重新运行Pipeline",
            "📞 **团队协作**: 如需帮助，请联系相关开发团队"
        ])
        
        return recommendations
    
    async def _perform_health_checks(self, project_id: int, 
                                   deployment_jobs: List[Dict]) -> Dict[str, Any]:
        """执行健康检查"""
        health_results = {
            "overall_status": "unknown",
            "checks": [],
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 获取项目信息以确定部署环境
            project = self.gitlab_client.get_project(project_id)
            if not project:
                health_results["overall_status"] = "error"
                health_results["checks"].append({
                    "name": "project_info",
                    "status": "failed",
                    "message": "无法获取项目信息"
                })
                return health_results
            
            # 检查环境
            environments = self.gitlab_client.get_project_environments(project_id)
            
            if environments:
                for env in environments:
                    env_name = env.get('name', 'unknown')
                    external_url = env.get('external_url')
                    
                    check_result = {
                        "name": f"environment_{env_name}",
                        "status": "unknown",
                        "message": f"环境 {env_name}",
                        "url": external_url
                    }
                    
                    if external_url:
                        # 这里可以添加实际的HTTP健康检查
                        # 目前只是记录环境信息
                        check_result["status"] = "info"
                        check_result["message"] = f"环境 {env_name} 可用，URL: {external_url}"
                    else:
                        check_result["status"] = "warning"
                        check_result["message"] = f"环境 {env_name} 没有配置外部URL"
                    
                    health_results["checks"].append(check_result)
            
            # 检查最近的部署
            for job in deployment_jobs:
                if job.get('status') == 'success':
                    health_results["checks"].append({
                        "name": f"deployment_{job.get('name')}",
                        "status": "success",
                        "message": f"部署作业 {job.get('name')} 成功完成"
                    })
            
            # 确定整体状态
            check_statuses = [check["status"] for check in health_results["checks"]]
            if "failed" in check_statuses:
                health_results["overall_status"] = "failed"
            elif "warning" in check_statuses:
                health_results["overall_status"] = "warning"
            elif "success" in check_statuses:
                health_results["overall_status"] = "success"
            else:
                health_results["overall_status"] = "info"
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}", exc_info=True)
            health_results["overall_status"] = "error"
            health_results["checks"].append({
                "name": "health_check",
                "status": "failed",
                "message": f"健康检查执行失败: {str(e)}"
            })
        
        return health_results
    
    def generate_analysis_report(self, analysis: PipelineAnalysis) -> str:
        """生成分析报告"""
        report = f"""
## 🔍 Pipeline分析报告

**Pipeline ID**: {analysis.pipeline_id}
**项目ID**: {analysis.project_id}
**状态**: {analysis.status}
**分支**: {analysis.ref}
**阶段**: {', '.join(analysis.stages)}

### 📊 作业统计
- **失败作业**: {len(analysis.failed_jobs)}
- **部署作业**: {len(analysis.deployment_jobs)}
- **日志分析**: {'已完成' if analysis.logs_analyzed else '未执行'}

### ❌ 错误分析
{analysis.error_summary}

### 💡 修复建议
"""
        
        for i, recommendation in enumerate(analysis.recommendations, 1):
            report += f"{i}. {recommendation}\n"
        
        # 添加健康检查结果
        if analysis.health_check_results:
            health = analysis.health_check_results
            report += f"""
### 🏥 健康检查结果
**整体状态**: {health['overall_status']}
**检查时间**: {health['timestamp']}

**详细检查**:
"""
            for check in health['checks']:
                status_emoji = {
                    'success': '✅',
                    'warning': '⚠️',
                    'failed': '❌',
                    'info': 'ℹ️',
                    'unknown': '❓'
                }.get(check['status'], '❓')
                
                report += f"- {status_emoji} {check['message']}\n"
        
        report += f"""
### 🔗 相关链接
- [Pipeline详情](pipeline_url_placeholder)
- [项目页面](project_url_placeholder)

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return report
