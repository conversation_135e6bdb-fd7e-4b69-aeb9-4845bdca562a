#!/usr/bin/env python3
"""
分析任务 task_1748396755_1748396755 的执行效果
使用新的分析链路组件进行结构化分析
"""

import json
import sys
from datetime import datetime

def load_task_data():
    """加载任务数据"""
    try:
        with open('logs/conversations/2025-05-28/task_1748396755_1748396755.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 无法加载任务数据: {e}")
        return None

def analyze_task_effectiveness(task_data):
    """分析任务执行效果"""
    
    print("=" * 80)
    print("🔍 任务执行效果分析报告")
    print("=" * 80)
    
    # 基本信息
    print(f"\n📋 任务基本信息")
    print(f"- 任务ID: {task_data['task_id']}")
    print(f"- 任务标题: {task_data['task_title']}")
    print(f"- 任务类型: {task_data['task_type']}")
    print(f"- 执行状态: {task_data['status']}")
    print(f"- 总耗时: {task_data['total_duration']:.2f}秒")
    
    # 问题识别分析
    print(f"\n🎯 1. 问题识别阶段")
    print(f"✅ 原始问题: 作业失败分析 - test (Job 728)")
    print(f"✅ 问题类型: script_failure")
    print(f"✅ 项目信息: ai-proxy项目，aider-plus-dev分支")
    print(f"✅ 失败作业: Pipeline 202, Job 728")
    
    # 解决方案设计分析
    print(f"\n💡 2. 解决方案设计阶段")
    
    # 检查是否有具体的解决方案
    final_result = task_data.get('final_result', '')
    
    if '未识别到具体错误' in final_result:
        print(f"❌ 问题: 未能获取到具体的错误信息")
        print(f"❌ 问题: 分析过于表面，缺乏深度")
    else:
        print(f"✅ 识别到具体错误")
    
    if 'pytest -v' in final_result:
        print(f"⚠️  解决方案: 提供了通用的pytest命令")
        print(f"⚠️  问题: 缺乏针对性的具体修复方案")
    
    # 执行过程分析
    print(f"\n⚙️ 3. 执行过程阶段")
    rounds = task_data.get('rounds', [])
    print(f"- 总轮次: {len(rounds)}轮")
    
    for i, round_data in enumerate(rounds, 1):
        print(f"  第{i}轮: {round_data['round_name']}")
        print(f"    - 耗时: {round_data['duration']:.2f}秒")
        print(f"    - 状态: {round_data['status']}")
        print(f"    - 模型: {round_data['model_name']}")
    
    # 检查执行深度
    if '已执行 0/1 个命令' in final_result:
        print(f"❌ 问题: 没有执行任何实际的修复命令")
        print(f"❌ 问题: 缺乏实际的问题解决行动")
    
    if '验证 0/2 通过' in final_result:
        print(f"❌ 问题: 没有进行任何验证步骤")
        print(f"❌ 问题: 无法确认问题是否真正解决")
    
    # 结果评估分析
    print(f"\n📊 4. 结果评估阶段")
    
    success_rate = 100 if task_data['status'] == 'success' else 0
    print(f"- 执行状态: {task_data['status']}")
    print(f"- 成功率: {success_rate}%")
    
    # 深度分析
    print(f"\n🔍 深度分析")
    
    # 检查是否达到预期
    expected_behaviors = [
        ("获取实际日志", "GitLabClient获取Job的实际日志内容" in task_data.get('metadata', {}).get('initial_request', '')),
        ("分析具体错误", "未识别到具体错误" not in final_result),
        ("执行修复命令", "已执行 0/1 个命令" not in final_result),
        ("验证修复效果", "验证 0/2 通过" not in final_result),
        ("提供针对性方案", "pytest -v" not in final_result or len([x for x in final_result.split('\n') if x.strip()]) > 10)
    ]
    
    print(f"\n✅ 预期行为检查:")
    achieved_count = 0
    for behavior, achieved in expected_behaviors:
        status = "✅" if achieved else "❌"
        print(f"  {status} {behavior}: {'达成' if achieved else '未达成'}")
        if achieved:
            achieved_count += 1
    
    achievement_rate = (achieved_count / len(expected_behaviors)) * 100
    print(f"\n📈 总体达成率: {achievement_rate:.1f}% ({achieved_count}/{len(expected_behaviors)})")
    
    # 问题总结
    print(f"\n🚨 主要问题:")
    print(f"1. 系统没有真正获取GitLab Job的实际日志")
    print(f"2. 分析过于表面，只是返回了通用的错误报告格式")
    print(f"3. 没有执行任何实际的修复命令")
    print(f"4. 没有进行任何验证步骤")
    print(f"5. 提供的解决方案过于通用，缺乏针对性")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    print(f"1. 确保GitLabClient能够真正获取到Job日志")
    print(f"2. 实现真正的日志分析逻辑，而不是返回模板")
    print(f"3. 添加实际的命令执行能力")
    print(f"4. 实现验证机制确保问题真正解决")
    print(f"5. 根据具体错误类型提供针对性解决方案")
    
    return achievement_rate

def main():
    """主函数"""
    print("🚀 开始分析任务执行效果...")
    
    task_data = load_task_data()
    if not task_data:
        sys.exit(1)
    
    achievement_rate = analyze_task_effectiveness(task_data)
    
    print(f"\n" + "=" * 80)
    print(f"📋 分析结论")
    print(f"=" * 80)
    
    if achievement_rate >= 80:
        print(f"🎉 任务执行效果: 优秀 ({achievement_rate:.1f}%)")
        print(f"✅ 系统基本达到了预期效果")
    elif achievement_rate >= 60:
        print(f"⚠️  任务执行效果: 良好 ({achievement_rate:.1f}%)")
        print(f"🔧 系统部分达到预期，需要优化")
    elif achievement_rate >= 40:
        print(f"❌ 任务执行效果: 一般 ({achievement_rate:.1f}%)")
        print(f"🚨 系统存在明显问题，需要重大改进")
    else:
        print(f"💥 任务执行效果: 差 ({achievement_rate:.1f}%)")
        print(f"🆘 系统严重偏离预期，需要重新设计")
    
    print(f"\n🎯 核心问题: 系统虽然返回了成功状态，但实际上没有执行任何有意义的分析和修复操作")
    print(f"🔧 关键改进: 需要实现真正的GitLab集成和日志分析能力")

if __name__ == "__main__":
    main()
