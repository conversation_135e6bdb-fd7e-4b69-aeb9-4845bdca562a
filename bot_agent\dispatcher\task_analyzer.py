"""
任务分析器 - 使用AI智能分析任务内容，确定任务类型和处理方式
"""

import logging
import re
import json
import os
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union

from bot_agent.config.model_config import ModelConfig

logger = logging.getLogger(__name__)


class TaskType(str, Enum):
    """任务类型枚举"""
    CODE_GENERATION = "code_generation"  # 代码生成
    CODE_OPTIMIZATION = "code_optimization"  # 代码优化
    TEST_GENERATION = "test_generation"  # 测试生成
    BUG_FIX = "bug_fix"  # 错误修复
    DOCUMENTATION = "documentation"  # 文档生成
    INFORMATION_QUERY = "information_query"  # 信息查询
    PROJECT_ANALYSIS = "project_analysis"  # 项目分析
    UNKNOWN = "unknown"  # 未知类型


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class AITaskAnalyzer:
    """
    基于AI的智能任务分析器，使用大语言模型来分析任务内容
    """

    def __init__(self):
        """初始化AI任务分析器"""
        # 使用配置管理器获取推理分析模型
        self.model_name = ModelConfig.get_openrouter_analysis_model()

        # 记录当前配置
        ModelConfig.log_current_config()
        logger.info(f"AI Task analyzer initialized with analysis model: {self.model_name}")

    def analyze_task(
        self,
        title: str,
        description: str,
        labels: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Union[str, List[str], Dict]]:
        """
        使用AI分析任务内容，确定任务类型和处理方式

        Args:
            title: 任务标题
            description: 任务描述
            labels: 任务标签列表
            metadata: 额外的元数据

        Returns:
            Dict: 包含任务分析结果的字典
        """
        try:
            # 使用AI分析任务
            ai_analysis = self._ai_analyze_task(title, description, labels)

            # 提取AI分析结果
            task_type = TaskType(ai_analysis.get("task_type", "unknown"))
            confidence = ai_analysis.get("confidence", 0.8)
            priority = TaskPriority(ai_analysis.get("priority", "medium"))

            # 其他分析（保留原有逻辑作为补充）
            content = f"{title}\n{description}"
            keywords = self._extract_keywords(content)
            component = "aider"  # 所有任务都使用aider处理
            code_complexity = ai_analysis.get("complexity", "medium")
            estimated_time = self._estimate_completion_time(task_type, code_complexity, content)
            dependencies = self._identify_dependencies(content)
            risks = ai_analysis.get("risks", [])

            # 返回分析结果
            return {
                "task_type": task_type,
                "confidence": confidence,
                "priority": priority,
                "keywords": keywords,
                "component": component,
                "estimated_time": estimated_time,
                "dependencies": dependencies,
                "risks": risks,
                "ai_reasoning": ai_analysis.get("reasoning", ""),
                "metadata": {
                    "has_code_blocks": self._contains_code_blocks(description),
                    "mentioned_files": self._extract_file_paths(description),
                    "language_hints": self._detect_programming_languages(content),
                    "code_complexity": code_complexity,
                    "user_metadata": metadata or {},
                    "ai_analysis": ai_analysis,
                }
            }
        except Exception as e:
            logger.error(f"AI任务分析失败，使用默认分析: {e}")
            # 如果AI分析失败，使用默认的代码生成类型
            return self._fallback_analysis(title, description, labels, metadata)

    def _ai_analyze_task(self, title: str, description: str, labels: Optional[List[str]] = None) -> Dict:
        """
        使用AI分析任务

        Args:
            title: 任务标题
            description: 任务描述
            labels: 任务标签

        Returns:
            Dict: AI分析结果
        """
        try:
            # 导入AI模型
            import os
            import openai

            # 设置OpenRouter API
            client = openai.OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=os.getenv("OPENROUTER_API_KEY")
            )

            # 构建分析提示
            labels_text = f"标签: {', '.join(labels)}" if labels else "无标签"

            prompt = f"""
请分析以下任务，确定其类型、优先级和复杂度。

任务标题: {title}
任务描述: {description}
{labels_text}

请返回JSON格式的分析结果，包含以下字段：
- task_type: 任务类型，可选值为 "code_generation", "code_optimization", "test_generation", "bug_fix", "documentation", "information_query", "project_analysis", "unknown"
- confidence: 置信度 (0.0-1.0)
- priority: 优先级，可选值为 "high", "medium", "low"
- complexity: 复杂度，可选值为 "low", "medium", "high"
- reasoning: 分析推理过程
- risks: 风险列表，每个风险包含 type, level, description

分析要点：
1. "增加功能"、"实现功能"、"开发"、"创建"、"新增" 等通常是代码生成任务
2. "修复"、"解决问题"、"调试" 等通常是bug修复任务
3. "优化"、"改进"、"重构" 等通常是代码优化任务
4. "测试"、"单元测试" 等通常是测试生成任务
5. "文档"、"注释" 等通常是文档任务
6. "查询"、"列出"、"显示"、"查看"、"获取信息"、"哪些"、"什么" 等通常是信息查询任务
7. "分析项目"、"项目结构"、"代码分析" 等通常是项目分析任务

特别注意：
- 如果用户询问"用的哪些镜像"、"列出来"、"查看配置"等，这是信息查询，不是要创建新内容
- 信息查询任务的目标是获取和展示现有信息，而不是生成新代码

请只返回JSON，不要其他文字。
"""

            response = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "你是一个专业的软件开发任务分析专家，能够准确识别和分类各种开发任务。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )

            # 解析AI响应
            ai_response = response.choices[0].message.content.strip()
            logger.info(f"AI分析响应: {ai_response}")

            # 尝试解析JSON
            try:
                # 如果响应包含```json标记，提取JSON部分
                if "```json" in ai_response:
                    start = ai_response.find("```json") + 7
                    end = ai_response.find("```", start)
                    if end != -1:
                        json_content = ai_response[start:end].strip()
                    else:
                        json_content = ai_response[start:].strip()
                else:
                    json_content = ai_response.strip()

                result = json.loads(json_content)
                logger.info(f"AI任务分析成功: {result}")
                return result
            except json.JSONDecodeError:
                # 如果不是标准JSON，尝试提取关键信息
                logger.warning(f"AI响应不是标准JSON，尝试解析: {ai_response}")
                return self._parse_ai_response(ai_response)

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            # 使用备用分析方法
            fallback_result = self._fallback_analysis(title, description, labels, {})
            return {
                "task_type": fallback_result["task_type"].value,
                "confidence": fallback_result["confidence"],
                "priority": fallback_result["priority"].value,
                "complexity": "medium",
                "reasoning": f"AI分析失败，使用备用分析: {fallback_result['ai_reasoning']}",
                "risks": []
            }

    def _convert_model_name(self, aider_model: str) -> str:
        """
        将Aider格式的模型名称转换为OpenRouter直接调用格式

        Args:
            aider_model: Aider格式的模型名称 (如: openrouter/deepseek/deepseek-r1:free)

        Returns:
            str: OpenRouter直接调用格式的模型名称 (如: deepseek/deepseek-r1:free)
        """
        # 如果模型名称以 "openrouter/" 开头，移除这个前缀
        if aider_model.startswith("openrouter/"):
            converted_model = aider_model[len("openrouter/"):]
            logger.debug(f"转换模型名称: {aider_model} -> {converted_model}")
            return converted_model

        # 如果不是openrouter格式，直接返回原名称
        return aider_model

    def _parse_ai_response(self, response: str) -> Dict:
        """
        解析非标准JSON的AI响应

        Args:
            response: AI响应文本

        Returns:
            Dict: 解析结果
        """
        # 简单的关键词匹配作为备用
        response_lower = response.lower()

        # 确定任务类型
        if any(word in response_lower for word in ["code_generation", "代码生成", "功能开发"]):
            task_type = "code_generation"
        elif any(word in response_lower for word in ["bug_fix", "修复", "调试"]):
            task_type = "bug_fix"
        elif any(word in response_lower for word in ["optimization", "优化", "重构"]):
            task_type = "code_optimization"
        elif any(word in response_lower for word in ["test", "测试"]):
            task_type = "test_generation"
        elif any(word in response_lower for word in ["documentation", "文档"]):
            task_type = "documentation"
        else:
            task_type = "code_generation"  # 默认

        return {
            "task_type": task_type,
            "confidence": 0.7,
            "priority": "medium",
            "complexity": "medium",
            "reasoning": "基于关键词匹配的备用分析",
            "risks": []
        }

    def _fallback_analysis(self, title: str, description: str, labels: Optional[List[str]], metadata: Optional[Dict]) -> Dict:
        """
        备用分析方法（当AI分析失败时使用）

        Args:
            title: 任务标题
            description: 任务描述
            labels: 任务标签
            metadata: 元数据

        Returns:
            Dict: 分析结果
        """
        content = f"{title}\n{description}".lower()

        # 更精确的关键词匹配 - 按优先级顺序检查

        # 1. 信息查询关键词（优先级最高，避免误分类）
        query_keywords = ["查询", "列出", "显示", "查看", "获取", "哪些", "什么", "怎么", "如何", "show", "list", "display", "get", "what", "which", "how"]
        query_context_keywords = ["用的哪些", "列出来", "帮我列出", "显示一下", "查看一下", "有哪些", "都有什么"]

        if (any(word in content for word in query_keywords) or
            any(phrase in content for phrase in query_context_keywords)):
            # 进一步检查是否是项目分析类查询
            if any(word in content for word in ["项目结构", "代码分析", "架构分析", "project structure", "code analysis"]):
                task_type = TaskType.PROJECT_ANALYSIS
                confidence = 0.9
            else:
                task_type = TaskType.INFORMATION_QUERY
                confidence = 0.9

        # 2. Bug修复关键词
        elif (any(word in content for word in ["修复", "解决", "调试", "bug", "错误", "故障", "异常", "报错", "fix", "debug", "error"]) or
              any(phrase in content for phrase in ["修复问题", "解决问题", "fix issue", "bug issue", "问题修复", "issue fix"])):
            task_type = TaskType.BUG_FIX
            confidence = 0.8

        # 3. 测试相关关键词
        elif any(word in content for word in ["测试", "test", "单元测试", "集成测试", "unit test", "integration test"]):
            task_type = TaskType.TEST_GENERATION
            confidence = 0.8

        # 4. 优化相关关键词
        elif any(word in content for word in ["优化", "改进", "重构", "性能", "提升", "optimize", "improve", "refactor", "performance"]):
            task_type = TaskType.CODE_OPTIMIZATION
            confidence = 0.8

        # 5. 文档相关关键词
        elif any(word in content for word in ["文档", "注释", "说明", "写文档", "添加文档", "添加注释", "readme"]):
            task_type = TaskType.DOCUMENTATION
            confidence = 0.8

        # 6. 代码生成关键词
        elif any(word in content for word in ["增加", "添加", "实现", "开发", "创建", "新增", "功能", "轮换", "构建", "编写", "设计", "搭建", "制作", "add", "create", "implement", "develop", "build", "write", "design", "make"]):
            task_type = TaskType.CODE_GENERATION
            confidence = 0.7

        # 7. 默认为信息查询（对于模糊请求）
        else:
            task_type = TaskType.INFORMATION_QUERY
            confidence = 0.5

        # 根据任务类型调整优先级
        if task_type == TaskType.BUG_FIX:
            priority = TaskPriority.HIGH
        elif task_type == TaskType.CODE_GENERATION:
            priority = TaskPriority.MEDIUM
        else:
            priority = TaskPriority.MEDIUM

        return {
            "task_type": task_type,
            "confidence": confidence,
            "priority": priority,
            "keywords": self._extract_keywords(content),
            "component": "aider",
            "estimated_time": {"min": 15, "expected": 30, "max": 45},
            "dependencies": [],
            "risks": [],
            "ai_reasoning": f"备用关键词分析: 检测到关键词匹配 {task_type.value} 类型",
            "metadata": {
                "has_code_blocks": self._contains_code_blocks(description),
                "mentioned_files": self._extract_file_paths(description),
                "language_hints": self._detect_programming_languages(content),
                "code_complexity": "medium",
                "user_metadata": metadata or {},
                "fallback_analysis": True,
            }
        }

    def _determine_task_type(
        self,
        content: str,
        labels: Optional[List[str]] = None
    ) -> Tuple[TaskType, float]:
        """
        确定任务类型和置信度

        Args:
            content: 任务内容
            labels: 任务标签

        Returns:
            Tuple[TaskType, float]: 任务类型和置信度
        """
        # 初始化各类型的分数
        scores = {task_type: 0 for task_type in TaskType}

        # 根据标签确定类型
        if labels:
            for label in labels:
                label = label.lower()
                if "bug" in label or "fix" in label:
                    scores[TaskType.BUG_FIX] += 2
                elif "feature" in label or "implement" in label:
                    scores[TaskType.CODE_GENERATION] += 2
                elif "optimize" in label or "performance" in label:
                    scores[TaskType.CODE_OPTIMIZATION] += 2
                elif "test" in label:
                    scores[TaskType.TEST_GENERATION] += 2
                elif "doc" in label:
                    scores[TaskType.DOCUMENTATION] += 2

        # 根据关键词确定类型
        content_lower = content.lower()
        for task_type, keywords in self.TASK_TYPE_KEYWORDS.items():
            for keyword in keywords:
                if keyword.lower() in content_lower:
                    scores[task_type] += 1

        # 找出得分最高的类型
        max_score = 0
        best_type = TaskType.UNKNOWN

        for task_type, score in scores.items():
            if score > max_score:
                max_score = score
                best_type = task_type

        # 计算置信度 (0-1)
        confidence = min(max_score / 5, 1.0) if max_score > 0 else 0.0

        return best_type, confidence

    def _extract_keywords(self, content: str) -> List[str]:
        """
        从任务内容中提取关键词

        Args:
            content: 任务内容

        Returns:
            List[str]: 关键词列表
        """
        # 简单实现：提取所有可能的技术术语
        # 在实际应用中，可以使用更复杂的 NLP 技术
        tech_terms = [
            "python", "javascript", "java", "c#", "golang", "react", "vue", "angular",
            "flask", "django", "fastapi", "spring", "node.js", "express", "api",
            "database", "sql", "nosql", "mongodb", "postgresql", "mysql", "redis",
            "docker", "kubernetes", "ci/cd", "git", "github", "gitlab", "bitbucket",
            "rest", "graphql", "grpc", "websocket", "http", "https", "jwt", "oauth",
            "authentication", "authorization", "security", "performance", "scalability",
            "testing", "unit test", "integration test", "e2e test", "documentation"
        ]

        found_terms = []
        content_lower = content.lower()

        for term in tech_terms:
            if term in content_lower:
                found_terms.append(term)

        return found_terms

    def _determine_priority(
        self,
        content: str,
        labels: Optional[List[str]] = None
    ) -> TaskPriority:
        """
        确定任务优先级

        Args:
            content: 任务内容
            labels: 任务标签

        Returns:
            TaskPriority: 任务优先级
        """
        # 检查标签中是否有优先级指示
        if labels:
            for label in labels:
                label_lower = label.lower()
                if "high" in label_lower or "urgent" in label_lower or "critical" in label_lower:
                    return TaskPriority.HIGH
                elif "medium" in label_lower:
                    return TaskPriority.MEDIUM
                elif "low" in label_lower:
                    return TaskPriority.LOW

        # 检查内容中的优先级关键词
        content_lower = content.lower()
        if any(word in content_lower for word in ["urgent", "critical", "asap", "立即", "紧急"]):
            return TaskPriority.HIGH
        elif any(word in content_lower for word in ["soon", "medium", "尽快"]):
            return TaskPriority.MEDIUM

        # 默认为中等优先级
        return TaskPriority.MEDIUM

    def _determine_component(self, task_type: TaskType) -> str:
        """
        根据任务类型确定处理组件

        Args:
            task_type: 任务类型

        Returns:
            str: 处理组件名称
        """
        # 根据任务类型映射到处理组件
        # 注意：现在所有任务都分发给 Aider 处理
        component_mapping = {
            TaskType.CODE_GENERATION: "aider",
            TaskType.BUG_FIX: "aider",
            TaskType.CODE_OPTIMIZATION: "aider",
            TaskType.TEST_GENERATION: "aider",
            TaskType.DOCUMENTATION: "aider",
            TaskType.UNKNOWN: "aider",          # 默认也使用 aider
        }

        # 始终返回 aider，确保所有任务都经过 Aider 处理
        return "aider"

    def _contains_code_blocks(self, text: str) -> bool:
        """
        检查文本是否包含代码块

        Args:
            text: 要检查的文本

        Returns:
            bool: 是否包含代码块
        """
        # 检查 Markdown 代码块
        return bool(re.search(r'```[\w]*\n[\s\S]*?\n```', text))

    def _extract_file_paths(self, text: str) -> List[str]:
        """
        从文本中提取文件路径

        Args:
            text: 要分析的文本

        Returns:
            List[str]: 文件路径列表
        """
        # 匹配常见的文件路径模式
        file_patterns = [
            r'\b[\w\-\.\/]+\.(py|js|java|cs|go|rb|php|html|css|json|yml|yaml|md|txt)\b',
            r'`[\w\-\.\/]+\.(py|js|java|cs|go|rb|php|html|css|json|yml|yaml|md|txt)`',
        ]

        file_paths = []
        for pattern in file_patterns:
            matches = re.findall(pattern, text)
            if matches:
                file_paths.extend(matches)

        return list(set(file_paths))  # 去重

    def _detect_programming_languages(self, text: str) -> List[str]:
        """
        检测文本中提到的编程语言

        Args:
            text: 要分析的文本

        Returns:
            List[str]: 编程语言列表
        """
        languages = [
            "python", "javascript", "typescript", "java", "c#", "c++", "go", "ruby",
            "php", "swift", "kotlin", "rust", "scala", "perl", "r", "bash", "shell"
        ]

        detected = []
        text_lower = text.lower()

        for lang in languages:
            if lang in text_lower:
                detected.append(lang)

        return detected

    def _analyze_code_complexity(self, text: str) -> str:
        """
        分析代码复杂度

        Args:
            text: 要分析的文本

        Returns:
            str: 复杂度评估 (low, medium, high)
        """
        # 提取代码块
        code_blocks = re.findall(r'```[\w]*\n([\s\S]*?)\n```', text)

        if not code_blocks:
            return "unknown"

        # 简单的复杂度评估
        complexity_indicators = {
            "low": ["简单", "基础", "straightforward", "simple", "basic"],
            "high": ["复杂", "困难", "挑战", "complex", "difficult", "challenging"],
        }

        # 检查代码长度和结构
        total_code_length = sum(len(block) for block in code_blocks)

        # 检查循环和条件语句数量
        loops_count = sum(
            block.count("for ") + block.count("while ") +
            block.count("foreach") + block.count("do ")
            for block in code_blocks
        )

        conditions_count = sum(
            block.count("if ") + block.count("else ") +
            block.count("switch ") + block.count("case ")
            for block in code_blocks
        )

        # 检查文本中的复杂度指示词
        text_lower = text.lower()
        for level, indicators in complexity_indicators.items():
            for indicator in indicators:
                if indicator in text_lower:
                    return level

        # 根据代码长度和结构评估复杂度
        if total_code_length > 500 or loops_count > 5 or conditions_count > 10:
            return "high"
        elif total_code_length > 200 or loops_count > 2 or conditions_count > 5:
            return "medium"
        else:
            return "low"

    def _estimate_completion_time(
        self,
        task_type: TaskType,
        complexity: str,
        content: str
    ) -> Dict[str, int]:
        """
        估计任务完成时间

        Args:
            task_type: 任务类型
            complexity: 代码复杂度
            content: 任务内容

        Returns:
            Dict[str, int]: 估计时间（分钟）
        """
        # 基础时间（分钟）
        base_times = {
            TaskType.CODE_GENERATION: 30,
            TaskType.CODE_OPTIMIZATION: 20,
            TaskType.TEST_GENERATION: 15,
            TaskType.BUG_FIX: 25,
            TaskType.DOCUMENTATION: 10,
            TaskType.UNKNOWN: 30,
        }

        # 复杂度系数
        complexity_factors = {
            "low": 0.7,
            "medium": 1.0,
            "high": 1.5,
            "unknown": 1.0,
        }

        # 计算基础估计时间
        base_time = base_times.get(task_type, 30)
        factor = complexity_factors.get(complexity, 1.0)

        # 应用复杂度系数
        estimated_minutes = int(base_time * factor)

        # 检查是否有特殊情况
        if "紧急" in content or "urgent" in content.lower():
            # 紧急任务可能需要更快完成
            estimated_minutes = max(10, int(estimated_minutes * 0.8))

        # 返回估计时间范围
        return {
            "min": max(5, int(estimated_minutes * 0.7)),
            "expected": estimated_minutes,
            "max": int(estimated_minutes * 1.3),
        }

    def _identify_dependencies(self, content: str) -> List[Dict[str, str]]:
        """
        识别任务依赖关系

        Args:
            content: 任务内容

        Returns:
            List[Dict[str, str]]: 依赖关系列表
        """
        dependencies = []

        # 查找引用的 Issue 或 PR
        issue_refs = re.findall(r'#(\d+)', content)
        for ref in issue_refs:
            dependencies.append({
                "type": "issue",
                "id": ref,
                "relationship": "related",
            })

        # 查找明确提到的依赖
        dependency_patterns = [
            r'依赖于\s*[#]?(\d+)',
            r'depends on\s*[#]?(\d+)',
            r'blocked by\s*[#]?(\d+)',
            r'after\s*[#]?(\d+)',
        ]

        for pattern in dependency_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                dependencies.append({
                    "type": "issue",
                    "id": match,
                    "relationship": "blocked_by",
                })

        return dependencies

    def _analyze_risks(self, content: str, task_type: TaskType) -> List[Dict[str, str]]:
        """
        分析任务风险因素

        Args:
            content: 任务内容
            task_type: 任务类型

        Returns:
            List[Dict[str, str]]: 风险因素列表
        """
        risks = []

        # 检查常见风险词
        risk_patterns = {
            "security": [
                r'安全', r'漏洞', r'注入', r'越权', r'认证', r'授权',
                r'security', r'vulnerability', r'injection', r'authentication', r'authorization',
            ],
            "performance": [
                r'性能', r'慢', r'优化', r'响应时间', r'延迟',
                r'performance', r'slow', r'optimize', r'response time', r'latency',
            ],
            "compatibility": [
                r'兼容性', r'浏览器', r'版本', r'支持',
                r'compatibility', r'browser', r'version', r'support',
            ],
            "data_loss": [
                r'数据丢失', r'数据损坏', r'备份',
                r'data loss', r'data corruption', r'backup',
            ],
        }

        content_lower = content.lower()

        for risk_type, patterns in risk_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content_lower):
                    risks.append({
                        "type": risk_type,
                        "level": "medium",
                        "description": f"Potential {risk_type} risk detected",
                    })
                    break  # 每种风险类型只添加一次

        # 根据任务类型添加特定风险
        if task_type == TaskType.CODE_GENERATION:
            risks.append({
                "type": "quality",
                "level": "medium",
                "description": "New code may require thorough testing",
            })
        elif task_type == TaskType.BUG_FIX:
            risks.append({
                "type": "regression",
                "level": "medium",
                "description": "Bug fix may cause regression in other areas",
            })

        return risks
