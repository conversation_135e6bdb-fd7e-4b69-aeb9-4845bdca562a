"""
代码生成工具 - AI自动编码的代码生成能力
"""

import os
import json
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
import tempfile

from .base_tool import BaseTool, ToolResult


class CodeGenerationTools(BaseTool):
    """
    代码生成工具
    
    功能：
    1. CRUD操作生成
    2. 模板代码生成
    3. 配置文件生成
    4. 测试用例生成
    5. Mock数据生成
    6. API接口生成
    7. 数据模型生成
    8. 样板代码生成
    """
    
    def __init__(self):
        super().__init__()
        self.templates = self._init_templates()
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'generate_crud',
            'generate_api_endpoints',
            'generate_data_models',
            'generate_test_cases',
            'generate_mock_data',
            'generate_config_files',
            'generate_boilerplate',
            'generate_documentation_templates'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "代码生成工具 - CRUD、API、模型、测试等代码自动生成"
    
    def _init_templates(self) -> Dict:
        """初始化代码模板"""
        return {
            'python_class': '''class {class_name}:
    """
    {description}
    """
    
    def __init__(self{init_params}):
        {init_body}
    
    {methods}
''',
            'python_function': '''def {function_name}({parameters}):
    """
    {description}
    
    Args:
        {args_docs}
    
    Returns:
        {return_docs}
    """
    {body}
''',
            'flask_route': '''@app.route('{route}', methods=['{method}'])
def {function_name}():
    """
    {description}
    """
    try:
        {body}
        return jsonify({{'success': True, 'data': result}})
    except Exception as e:
        return jsonify({{'success': False, 'error': str(e)}}), 500
''',
            'fastapi_route': '''@app.{method_lower}('{route}')
async def {function_name}({parameters}):
    """
    {description}
    """
    try:
        {body}
        return {{'success': True, 'data': result}}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
''',
            'sqlalchemy_model': '''class {model_name}(db.Model):
    """
    {description}
    """
    __tablename__ = '{table_name}'
    
    {fields}
    
    def __repr__(self):
        return f'<{model_name} {{self.id}}>'
    
    def to_dict(self):
        return {{
            {to_dict_fields}
        }}
''',
            'pytest_test': '''def test_{test_name}():
    """
    测试 {description}
    """
    # Arrange
    {arrange}
    
    # Act
    {act}
    
    # Assert
    {assert_statements}
'''
        }
    
    async def generate_crud(self, model_name: str, fields: List[Dict], 
                           framework: str = "flask") -> ToolResult:
        """
        生成CRUD操作代码
        
        Args:
            model_name: 模型名称
            fields: 字段定义列表
            framework: 框架类型 (flask, fastapi, django)
            
        Returns:
            ToolResult: CRUD代码生成结果
        """
        try:
            crud_code = {
                'model': self._generate_model_code(model_name, fields, framework),
                'create': self._generate_create_code(model_name, fields, framework),
                'read': self._generate_read_code(model_name, fields, framework),
                'update': self._generate_update_code(model_name, fields, framework),
                'delete': self._generate_delete_code(model_name, fields, framework)
            }
            
            # 生成完整的CRUD文件
            full_crud_code = self._combine_crud_code(crud_code, framework)
            
            return ToolResult(
                success=True,
                data={
                    'model_name': model_name,
                    'framework': framework,
                    'crud_code': crud_code,
                    'full_code': full_crud_code,
                    'fields': fields
                },
                message=f"成功生成 {model_name} 的CRUD代码"
            )
            
        except Exception as e:
            self.log_error(e, f"CRUD生成: {model_name}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def generate_api_endpoints(self, api_spec: Dict, framework: str = "flask") -> ToolResult:
        """
        生成API端点代码
        
        Args:
            api_spec: API规范
            framework: 框架类型
            
        Returns:
            ToolResult: API端点生成结果
        """
        try:
            endpoints = []
            
            for endpoint in api_spec.get('endpoints', []):
                endpoint_code = self._generate_endpoint_code(endpoint, framework)
                endpoints.append({
                    'path': endpoint['path'],
                    'method': endpoint['method'],
                    'code': endpoint_code
                })
            
            # 生成完整的API文件
            api_file_code = self._generate_api_file(endpoints, framework)
            
            return ToolResult(
                success=True,
                data={
                    'framework': framework,
                    'endpoints': endpoints,
                    'api_file_code': api_file_code,
                    'endpoint_count': len(endpoints)
                },
                message=f"成功生成 {len(endpoints)} 个API端点"
            )
            
        except Exception as e:
            self.log_error(e, f"API端点生成: {framework}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def generate_data_models(self, schema: Dict, framework: str = "sqlalchemy") -> ToolResult:
        """
        生成数据模型代码
        
        Args:
            schema: 数据库模式
            framework: ORM框架 (sqlalchemy, django, peewee)
            
        Returns:
            ToolResult: 数据模型生成结果
        """
        try:
            models = []
            
            for table_name, table_schema in schema.get('tables', {}).items():
                model_code = self._generate_model_from_schema(
                    table_name, table_schema, framework
                )
                models.append({
                    'table_name': table_name,
                    'model_name': self._to_class_name(table_name),
                    'code': model_code
                })
            
            # 生成模型文件
            models_file_code = self._generate_models_file(models, framework)
            
            return ToolResult(
                success=True,
                data={
                    'framework': framework,
                    'models': models,
                    'models_file_code': models_file_code,
                    'model_count': len(models)
                },
                message=f"成功生成 {len(models)} 个数据模型"
            )
            
        except Exception as e:
            self.log_error(e, f"数据模型生成: {framework}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def generate_test_cases(self, target_code: str, test_type: str = "unit") -> ToolResult:
        """
        生成测试用例
        
        Args:
            target_code: 目标代码
            test_type: 测试类型 (unit, integration, api)
            
        Returns:
            ToolResult: 测试用例生成结果
        """
        try:
            # 分析目标代码
            code_analysis = self._analyze_code_for_testing(target_code)
            
            # 生成测试用例
            test_cases = []
            for function in code_analysis['functions']:
                test_case = self._generate_function_test(function, test_type)
                test_cases.append(test_case)
            
            for class_info in code_analysis['classes']:
                class_tests = self._generate_class_tests(class_info, test_type)
                test_cases.extend(class_tests)
            
            # 生成完整的测试文件
            test_file_code = self._generate_test_file(test_cases, test_type)
            
            return ToolResult(
                success=True,
                data={
                    'test_type': test_type,
                    'test_cases': test_cases,
                    'test_file_code': test_file_code,
                    'test_count': len(test_cases)
                },
                message=f"成功生成 {len(test_cases)} 个测试用例"
            )
            
        except Exception as e:
            self.log_error(e, f"测试用例生成: {test_type}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def generate_mock_data(self, schema: Dict, count: int = 10) -> ToolResult:
        """
        生成Mock数据
        
        Args:
            schema: 数据模式
            count: 生成数量
            
        Returns:
            ToolResult: Mock数据生成结果
        """
        try:
            mock_data = {}
            
            for table_name, table_schema in schema.get('tables', {}).items():
                table_data = []
                for i in range(count):
                    record = self._generate_mock_record(table_schema, i)
                    table_data.append(record)
                mock_data[table_name] = table_data
            
            # 生成不同格式的Mock数据
            formats = {
                'json': json.dumps(mock_data, indent=2, ensure_ascii=False),
                'python': self._generate_python_mock_data(mock_data),
                'sql': self._generate_sql_insert_statements(mock_data, schema)
            }
            
            return ToolResult(
                success=True,
                data={
                    'mock_data': mock_data,
                    'formats': formats,
                    'record_count': count,
                    'table_count': len(mock_data)
                },
                message=f"成功生成 {len(mock_data)} 个表的Mock数据"
            )
            
        except Exception as e:
            self.log_error(e, f"Mock数据生成")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def generate_config_files(self, project_type: str, options: Dict = None) -> ToolResult:
        """
        生成配置文件
        
        Args:
            project_type: 项目类型 (flask, fastapi, django, react, vue)
            options: 配置选项
            
        Returns:
            ToolResult: 配置文件生成结果
        """
        try:
            options = options or {}
            config_files = {}
            
            if project_type == "flask":
                config_files.update(self._generate_flask_configs(options))
            elif project_type == "fastapi":
                config_files.update(self._generate_fastapi_configs(options))
            elif project_type == "django":
                config_files.update(self._generate_django_configs(options))
            elif project_type == "react":
                config_files.update(self._generate_react_configs(options))
            elif project_type == "vue":
                config_files.update(self._generate_vue_configs(options))
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的项目类型: {project_type}"
                )
            
            return ToolResult(
                success=True,
                data={
                    'project_type': project_type,
                    'config_files': config_files,
                    'file_count': len(config_files)
                },
                message=f"成功生成 {len(config_files)} 个配置文件"
            )
            
        except Exception as e:
            self.log_error(e, f"配置文件生成: {project_type}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _generate_model_code(self, model_name: str, fields: List[Dict], framework: str) -> str:
        """生成模型代码"""
        if framework == "flask":
            return self._generate_sqlalchemy_model(model_name, fields)
        elif framework == "fastapi":
            return self._generate_pydantic_model(model_name, fields)
        elif framework == "django":
            return self._generate_django_model(model_name, fields)
        else:
            return f"# {model_name} model for {framework}"
    
    def _generate_sqlalchemy_model(self, model_name: str, fields: List[Dict]) -> str:
        """生成SQLAlchemy模型"""
        field_definitions = []
        to_dict_fields = []
        
        for field in fields:
            field_name = field['name']
            field_type = field.get('type', 'String')
            nullable = field.get('nullable', True)
            primary_key = field.get('primary_key', False)
            
            # 生成字段定义
            field_def = f"    {field_name} = db.Column(db.{field_type}"
            if primary_key:
                field_def += ", primary_key=True"
            if not nullable:
                field_def += ", nullable=False"
            field_def += ")"
            field_definitions.append(field_def)
            
            # 生成to_dict字段
            to_dict_fields.append(f"            '{field_name}': self.{field_name}")
        
        return self.templates['sqlalchemy_model'].format(
            model_name=model_name,
            description=f"{model_name} 数据模型",
            table_name=model_name.lower() + 's',
            fields='\n'.join(field_definitions),
            to_dict_fields=',\n'.join(to_dict_fields)
        )
    
    def _generate_create_code(self, model_name: str, fields: List[Dict], framework: str) -> str:
        """生成创建操作代码"""
        if framework == "flask":
            return self.templates['flask_route'].format(
                route=f'/{model_name.lower()}s',
                method='POST',
                function_name=f'create_{model_name.lower()}',
                description=f'创建新的{model_name}',
                body=f'''        data = request.get_json()
        new_{model_name.lower()} = {model_name}(**data)
        db.session.add(new_{model_name.lower()})
        db.session.commit()
        result = new_{model_name.lower()}.to_dict()'''
            )
        else:
            return f"# Create {model_name} for {framework}"
    
    def _generate_read_code(self, model_name: str, fields: List[Dict], framework: str) -> str:
        """生成读取操作代码"""
        if framework == "flask":
            return self.templates['flask_route'].format(
                route=f'/{model_name.lower()}s',
                method='GET',
                function_name=f'get_{model_name.lower()}s',
                description=f'获取{model_name}列表',
                body=f'''        {model_name.lower()}s = {model_name}.query.all()
        result = [item.to_dict() for item in {model_name.lower()}s]'''
            )
        else:
            return f"# Read {model_name} for {framework}"
    
    def _generate_update_code(self, model_name: str, fields: List[Dict], framework: str) -> str:
        """生成更新操作代码"""
        if framework == "flask":
            return self.templates['flask_route'].format(
                route=f'/{model_name.lower()}s/<int:id>',
                method='PUT',
                function_name=f'update_{model_name.lower()}',
                description=f'更新{model_name}',
                body=f'''        data = request.get_json()
        {model_name.lower()} = {model_name}.query.get_or_404(id)
        for key, value in data.items():
            setattr({model_name.lower()}, key, value)
        db.session.commit()
        result = {model_name.lower()}.to_dict()'''
            )
        else:
            return f"# Update {model_name} for {framework}"
    
    def _generate_delete_code(self, model_name: str, fields: List[Dict], framework: str) -> str:
        """生成删除操作代码"""
        if framework == "flask":
            return self.templates['flask_route'].format(
                route=f'/{model_name.lower()}s/<int:id>',
                method='DELETE',
                function_name=f'delete_{model_name.lower()}',
                description=f'删除{model_name}',
                body=f'''        {model_name.lower()} = {model_name}.query.get_or_404(id)
        db.session.delete({model_name.lower()})
        db.session.commit()
        result = {{'message': '{model_name} deleted successfully'}}'''
            )
        else:
            return f"# Delete {model_name} for {framework}"
    
    def _combine_crud_code(self, crud_code: Dict, framework: str) -> str:
        """组合CRUD代码"""
        if framework == "flask":
            imports = '''from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
db = SQLAlchemy(app)

'''
            return imports + '\n\n'.join(crud_code.values())
        else:
            return '\n\n'.join(crud_code.values())
    
    def _generate_flask_configs(self, options: Dict) -> Dict:
        """生成Flask配置文件"""
        return {
            'config.py': '''import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
''',
            'requirements.txt': '''Flask==2.3.3
Flask-SQLAlchemy==3.0.5
python-dotenv==1.0.0
''',
            '.env': '''FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///app.db
'''
        }
    
    def _to_class_name(self, table_name: str) -> str:
        """将表名转换为类名"""
        return ''.join(word.capitalize() for word in table_name.split('_'))
    
    def _analyze_code_for_testing(self, code: str) -> Dict:
        """分析代码用于生成测试"""
        # 简化实现
        functions = re.findall(r'def\s+(\w+)\s*\([^)]*\):', code)
        classes = re.findall(r'class\s+(\w+)\s*[:\(]', code)
        
        return {
            'functions': [{'name': func} for func in functions],
            'classes': [{'name': cls} for cls in classes]
        }
    
    def _generate_function_test(self, function_info: Dict, test_type: str) -> Dict:
        """生成函数测试"""
        func_name = function_info['name']
        return {
            'name': f'test_{func_name}',
            'code': self.templates['pytest_test'].format(
                test_name=func_name,
                description=f'{func_name}函数',
                arrange='# 准备测试数据',
                act=f'result = {func_name}()',
                assert_statements='assert result is not None'
            )
        }
    
    def _generate_class_tests(self, class_info: Dict, test_type: str) -> List[Dict]:
        """生成类测试"""
        class_name = class_info['name']
        return [{
            'name': f'test_{class_name.lower()}_creation',
            'code': self.templates['pytest_test'].format(
                test_name=f'{class_name.lower()}_creation',
                description=f'{class_name}类实例化',
                arrange='# 准备测试数据',
                act=f'instance = {class_name}()',
                assert_statements='assert instance is not None'
            )
        }]
    
    def _generate_test_file(self, test_cases: List[Dict], test_type: str) -> str:
        """生成测试文件"""
        imports = '''import pytest
from unittest.mock import Mock, patch

'''
        test_code = '\n\n'.join([test['code'] for test in test_cases])
        return imports + test_code
    
    def _generate_mock_record(self, schema: Dict, index: int) -> Dict:
        """生成单条Mock记录"""
        record = {}
        for field_name, field_info in schema.get('fields', {}).items():
            field_type = field_info.get('type', 'string')
            
            if field_type == 'integer':
                record[field_name] = index + 1
            elif field_type == 'string':
                record[field_name] = f'{field_name}_{index + 1}'
            elif field_type == 'boolean':
                record[field_name] = index % 2 == 0
            elif field_type == 'datetime':
                record[field_name] = f'2024-01-{(index % 28) + 1:02d}T10:00:00Z'
            else:
                record[field_name] = f'value_{index + 1}'
        
        return record
    
    def _generate_python_mock_data(self, mock_data: Dict) -> str:
        """生成Python格式的Mock数据"""
        return f"mock_data = {json.dumps(mock_data, indent=4, ensure_ascii=False)}"
    
    def _generate_sql_insert_statements(self, mock_data: Dict, schema: Dict) -> str:
        """生成SQL插入语句"""
        sql_statements = []
        
        for table_name, records in mock_data.items():
            if records:
                columns = list(records[0].keys())
                sql_statements.append(f"-- {table_name} 表数据")
                
                for record in records:
                    values = [f"'{v}'" if isinstance(v, str) else str(v) for v in record.values()]
                    sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(values)});"
                    sql_statements.append(sql)
                
                sql_statements.append("")
        
        return '\n'.join(sql_statements)
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            if len(args) >= 2:
                return await self.generate_crud(args[0], args[1])
            else:
                return await self.generate_config_files(args[0])
        else:
            return ToolResult(
                success=False,
                error="缺少必要参数"
            )
