# 模型架构设计

## 概述

本项目采用了专门化的AI模型架构，针对不同的任务类型使用最适合的模型：

## 模型分工

### 🧠 DeepSeek R1 - 推理分析模型
- **用途**: 任务分析、推理、决策
- **组件**: TaskAnalyzer (任务分析器)
- **环境变量**: `ANALYSIS_MODEL`
- **默认值**: `openrouter/deepseek/deepseek-r1:free`
- **特点**: 
  - 强大的推理能力
  - 适合复杂的逻辑分析
  - 能够准确识别任务类型和优先级

### 💻 DeepSeek Chat - 代码生成模型
- **用途**: 代码生成、修改、优化
- **组件**: Aider (代码生成引擎)
- **环境变量**: `AIDER_MODEL`
- **默认值**: `openrouter/deepseek/deepseek-chat`
- **特点**:
  - 专门优化的代码生成能力
  - 更好的代码理解和生成质量
  - 适合实际的编程任务

## 环境变量配置

```bash
# OpenRouter API密钥
OPENROUTER_API_KEY=your-api-key-here

# 推理分析模型 (用于任务分析)
ANALYSIS_MODEL=openrouter/deepseek/deepseek-r1:free

# 代码生成模型 (用于Aider)
AIDER_MODEL=openrouter/deepseek/deepseek-chat
```

## 模型名称格式转换

系统自动处理Aider格式和OpenRouter直接调用格式之间的转换：

- **Aider格式**: `openrouter/deepseek/deepseek-r1:free`
- **OpenRouter直接调用**: `deepseek/deepseek-r1:free`

转换逻辑在 `AITaskAnalyzer._convert_model_name()` 方法中实现。

## 工作流程

1. **任务接收**: GitLab Webhook 或 API 接收任务
2. **任务分析**: TaskAnalyzer 使用 DeepSeek R1 分析任务类型
3. **任务路由**: 根据分析结果路由到相应处理器
4. **代码生成**: Aider 使用 DeepSeek Chat 执行代码修改
5. **结果反馈**: 将执行结果反馈给用户

## 优势

1. **专业化**: 每个模型专注于自己最擅长的任务
2. **效率**: 避免使用过于强大的模型处理简单任务
3. **成本优化**: R1 free版本用于分析，Chat用于代码生成
4. **质量保证**: 使用最适合的模型确保最佳结果

## 扩展性

如果需要添加新的模型或任务类型，可以：

1. 添加新的环境变量
2. 在相应组件中添加模型选择逻辑
3. 更新模型转换方法支持新格式
