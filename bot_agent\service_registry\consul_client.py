"""
Consul 客户端 - 提供与 Consul 交互的功能
"""

import json
import logging
import os
import socket
import uuid
from typing import Dict, List, Optional, Union

import consul
import requests

logger = logging.getLogger(__name__)


class ConsulClient:
    """Consul 客户端，提供服务注册、发现和健康检查功能"""

    def __init__(
        self,
        host: str = "localhost",
        port: int = 8500,
        scheme: str = "http",
        service_name: str = "bot-agent",
        service_id: Optional[str] = None,
        service_port: int = 8000,
        tags: Optional[List[str]] = None,
        check_interval: str = "10s",
        check_timeout: str = "5s",
    ):
        """
        初始化 Consul 客户端

        Args:
            host: Consul 服务器主机名
            port: Consul 服务器端口
            scheme: 协议 (http/https)
            service_name: 服务名称
            service_id: 服务 ID，如果不提供则自动生成
            service_port: 服务端口
            tags: 服务标签列表
            check_interval: 健康检查间隔
            check_timeout: 健康检查超时
        """
        self.host = host
        self.port = port
        self.scheme = scheme
        self.service_name = service_name
        self.service_id = service_id or f"{service_name}-{str(uuid.uuid4())[:8]}"
        self.service_port = service_port
        self.tags = tags or ["bot-agent", "api"]
        self.check_interval = check_interval
        self.check_timeout = check_timeout

        # 获取本机 IP
        self.service_host = self._get_local_ip()

        # 初始化 Consul 客户端
        self.consul = consul.Consul(host=host, port=port, scheme=scheme)
        logger.info(
            f"Initialized Consul client for {service_name} "
            f"(ID: {self.service_id}) at {self.service_host}:{service_port}"
        )

    def _get_local_ip(self) -> str:
        """获取本机 IP 地址"""
        try:
            # 创建一个临时 socket 连接来获取本机 IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception as e:
            logger.warning(f"Failed to get local IP: {e}. Using localhost.")
            return "127.0.0.1"

    def register_service(self, health_check_url: str = "/health") -> bool:
        """
        向 Consul 注册服务

        Args:
            health_check_url: 健康检查 URL 路径

        Returns:
            bool: 注册是否成功
        """
        try:
            # 获取环境变量中的主机名，如果没有则使用本地 IP
            service_address = os.getenv("SERVICE_ADDRESS", self.service_host)

            # 获取环境变量中的外部端口，如果没有则使用服务端口
            external_port = int(os.getenv("EXTERNAL_PORT", str(self.service_port)))

            # 构建健康检查 URL (使用外部可访问的地址)
            check_url = f"{self.scheme}://{service_address}:{external_port}{health_check_url}"

            logger.info(f"Registering service with health check URL: {check_url}")

            # 注册服务
            self.consul.agent.service.register(
                name=self.service_name,
                service_id=self.service_id,
                address=service_address,  # 使用外部可访问的地址
                port=external_port,       # 使用外部可访问的端口
                tags=self.tags,
                check={
                    "http": check_url,
                    "interval": self.check_interval,
                    "timeout": self.check_timeout,
                },
            )
            logger.info(
                f"Service {self.service_name} registered with ID {self.service_id}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to register service: {e}")
            return False

    def deregister_service(self) -> bool:
        """
        从 Consul 注销服务

        Returns:
            bool: 注销是否成功
        """
        try:
            self.consul.agent.service.deregister(self.service_id)
            logger.info(f"Service {self.service_id} deregistered")
            return True
        except Exception as e:
            logger.error(f"Failed to deregister service: {e}")
            return False

    def discover_service(self, service_name: str) -> List[Dict[str, Union[str, int]]]:
        """
        发现指定服务的所有实例

        Args:
            service_name: 要发现的服务名称

        Returns:
            List[Dict]: 服务实例列表，每个实例包含 host, port, id 等信息
        """
        try:
            # 获取服务实例
            _, services = self.consul.catalog.service(service_name)

            # 格式化结果
            instances = []
            for service in services:
                instances.append({
                    "id": service["ServiceID"],
                    "name": service["ServiceName"],
                    "host": service["ServiceAddress"] or service["Address"],
                    "port": service["ServicePort"],
                    "tags": service["ServiceTags"],
                })

            logger.debug(f"Discovered {len(instances)} instances of {service_name}")
            return instances
        except Exception as e:
            logger.error(f"Failed to discover service {service_name}: {e}")
            return []

    def get_service_health(self, service_name: str) -> Dict[str, List[Dict]]:
        """
        获取服务的健康状态

        Args:
            service_name: 服务名称

        Returns:
            Dict: 包含健康和不健康实例的字典
        """
        try:
            # 获取服务健康检查结果
            checks = self.consul.health.service(service_name)[1]

            # 分类健康和不健康的实例
            healthy = []
            unhealthy = []

            for check in checks:
                service = check["Service"]
                instance = {
                    "id": service["ID"],
                    "name": service["Service"],
                    "host": service["Address"],
                    "port": service["Port"],
                    "tags": service["Tags"],
                }

                # 检查服务是否健康
                if check["Checks"][0]["Status"] == "passing":
                    healthy.append(instance)
                else:
                    unhealthy.append(instance)

            return {
                "healthy": healthy,
                "unhealthy": unhealthy
            }
        except Exception as e:
            logger.error(f"Failed to get health status for {service_name}: {e}")
            return {"healthy": [], "unhealthy": []}
