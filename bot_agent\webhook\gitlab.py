"""
GitLab Webhook 处理器 - 处理来自 GitLab 的 Webhook 事件
"""

import datetime
import hmac
import ipaddress
import json
import logging
import os
import re
import uuid
from typing import Dict, Optional

from fastapi import APIRouter, Depends, Header, HTTPException, Request

# 配置日志
logger = logging.getLogger(__name__)


def extract_file_path(title: str, description: str) -> Optional[str]:
    """
    从标题和描述中提取文件路径

    Args:
        title: 标题
        description: 描述

    Returns:
        Optional[str]: 文件路径，如果没有找到则返回 None
    """
    # 合并标题和描述
    text = f"{title}\n{description}"

    # 尝试匹配常见的文件路径模式
    patterns = [
        # 匹配引号中的路径
        r'[\'"]((?:/|[A-Za-z]:\\)[\w\-./\\]+\.\w+)[\'"]',
        # 匹配冒号后的路径
        r'(?:文件|file|path|路径)[:：]\s*((?:/|[A-Za-z]:\\)[\w\-./\\]+\.\w+)',
        # 匹配反引号中的路径
        r'`((?:/|[A-Za-z]:\\)[\w\-./\\]+\.\w+)`',
        # 匹配常见的文件路径格式
        r'(?<!\w)((?:/|[A-Za-z]:\\)[\w\-./\\]+\.\w+)(?!\w)',
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text)
        if matches:
            # 返回第一个匹配的路径
            return matches[0]

    return None

# 创建路由器
# 注意：这里不使用前缀，而是在 main.py 中注册路由时设置前缀
router = APIRouter(tags=["webhook"])

@router.get("/test")
async def test_gitlab_webhook():
    """
    测试 GitLab Webhook 路由是否正常工作

    Returns:
        Dict: 测试结果
    """
    logger.info("GitLab webhook 测试路由被访问")
    return {
        "status": "success",
        "message": "GitLab webhook 路由正常工作",
        "timestamp": datetime.datetime.now().isoformat()
    }


async def verify_gitlab_signature(
    request: Request,
    x_gitlab_token: Optional[str] = Header(None),
    x_forwarded_for: Optional[str] = Header(None),
) -> None:
    """
    验证 GitLab Webhook 签名和请求来源

    Args:
        request: FastAPI 请求对象
        x_gitlab_token: GitLab Webhook 令牌
        x_forwarded_for: 请求的原始IP地址

    Raises:
        HTTPException: 如果签名验证失败或请求来源不被允许
    """
    # 从环境变量获取配置的 GitLab 令牌
    expected_token = os.getenv("GITLAB_WEBHOOK_TOKEN")

    # 从环境变量获取允许的 IP 地址列表（可选）
    allowed_ips_str = os.getenv("GITLAB_WEBHOOK_ALLOWED_IPS", "")
    allowed_ips = [ip.strip() for ip in allowed_ips_str.split(",")] if allowed_ips_str else []

    # 验证 IP 地址（如果配置了允许的 IP 地址）
    if allowed_ips:
        client_ip = x_forwarded_for or request.client.host
        if client_ip:
            is_allowed = False
            for allowed_ip in allowed_ips:
                try:
                    # 检查是否是 CIDR 格式
                    if "/" in allowed_ip:
                        network = ipaddress.ip_network(allowed_ip, strict=False)
                        if ipaddress.ip_address(client_ip) in network:
                            is_allowed = True
                            break
                    # 直接比较 IP
                    elif client_ip == allowed_ip:
                        is_allowed = True
                        break
                except ValueError:
                    logger.warning(f"Invalid IP address or network: {allowed_ip}")
                    continue

            if not is_allowed:
                logger.warning(f"Request from unauthorized IP: {client_ip}")
                raise HTTPException(status_code=403, detail="Unauthorized source IP")

    # 如果未配置令牌，则跳过验证
    if not expected_token:
        logger.warning("GitLab webhook token not configured, skipping verification")
        return

    # 验证令牌
    if not x_gitlab_token:
        logger.warning("Missing GitLab webhook token")
        raise HTTPException(status_code=401, detail="Missing GitLab webhook token")

    if not hmac.compare_digest(x_gitlab_token, expected_token):
        logger.warning("Invalid GitLab webhook token")
        raise HTTPException(status_code=401, detail="Invalid GitLab webhook token")


# 定义一个可以直接调用的函数，不依赖于路由
async def process_gitlab_webhook(
    request: Request,
) -> Dict[str, str]:
    """
    处理 GitLab Webhook 事件的核心逻辑

    Args:
        request: FastAPI 请求对象

    Returns:
        Dict: 处理结果
    """
    logger.info("=== 处理 GitLab webhook 请求 ===")
    logger.info(f"请求方法: {request.method}")
    logger.info(f"请求路径: {request.url.path}")
    logger.info(f"请求头: {dict(request.headers)}")

    try:
        # 获取请求体
        payload = await request.json()
        logger.info(f"请求体: {payload}")

        # 获取事件类型
        event_type = request.headers.get("X-Gitlab-Event")
        logger.info(f"GitLab 事件类型: {event_type}")

        if not event_type:
            logger.warning("Missing GitLab event type")
            raise HTTPException(status_code=400, detail="Missing GitLab event type")

        # 获取事件对象类型和操作
        object_kind = payload.get("object_kind", "unknown")
        action = payload.get("object_attributes", {}).get("action", "unknown")

        logger.info(f"Received GitLab webhook event: {event_type}, object_kind: {object_kind}, action: {action}")

        # 处理逻辑...
        return await process_event_by_type(event_type, payload)

    except json.JSONDecodeError:
        logger.error("Invalid JSON payload")
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    except Exception as e:
        logger.error(f"Error processing GitLab webhook: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing webhook: {str(e)}")


# 处理不同类型的事件
async def process_event_by_type(event_type: str, payload: Dict) -> Dict[str, str]:
    """
    根据事件类型处理 GitLab webhook 事件

    Args:
        event_type: GitLab 事件类型
        payload: 请求负载

    Returns:
        Dict: 处理结果
    """
    if event_type == "Issue Hook":
        return await handle_issue_event(payload)
    elif event_type == "Note Hook":
        return await handle_note_event(payload)
    elif event_type == "Merge Request Hook":
        return await handle_merge_request_event(payload)
    elif event_type == "Push Hook":
        return await handle_push_event(payload)
    elif event_type == "Tag Push Hook":
        return await handle_tag_push_event(payload)
    elif event_type == "Pipeline Hook":
        return await handle_pipeline_event(payload)
    elif event_type == "Job Hook":
        return await handle_job_event(payload)
    else:
        logger.info(f"Unhandled GitLab event type: {event_type}")
        return {"status": "ignored", "message": f"Event type {event_type} not handled"}


@router.post("/")
async def gitlab_webhook(
    request: Request,
    _: None = Depends(verify_gitlab_signature),
) -> Dict[str, str]:
    """
    处理 GitLab Webhook 事件

    Args:
        request: FastAPI 请求对象
        _: 依赖项，用于验证 GitLab 签名

    Returns:
        Dict: 处理结果
    """
    logger.info("=== 收到 GitLab webhook 请求（通过路由） ===")
    return await process_gitlab_webhook(request)


async def handle_issue_event(payload: Dict) -> Dict[str, str]:
    """
    处理 Issue 事件

    Args:
        payload: GitLab Webhook 负载

    Returns:
        Dict: 处理结果
    """
    try:
        # 提取相关信息
        action = payload.get("object_attributes", {}).get("action")
        issue_id = payload.get("object_attributes", {}).get("id")
        issue_iid = payload.get("object_attributes", {}).get("iid")  # 项目内 Issue ID
        issue_title = payload.get("object_attributes", {}).get("title")
        issue_description = payload.get("object_attributes", {}).get("description", "")
        project_id = payload.get("project", {}).get("id")
        project_name = payload.get("project", {}).get("name")

        # 严格验证项目ID - 拒绝处理没有明确项目ID的webhook
        if not project_id:
            logger.error(f"Issue event missing project ID - webhook rejected for security. Payload keys: {list(payload.keys())}")
            return {"status": "error", "message": "Missing project ID - webhook rejected for security"}

        # 获取用户信息
        user = payload.get("user", {})
        user_name = user.get("username", "")
        user_display_name = user.get("name", "")

        # 获取标签
        labels = []
        if "labels" in payload:
            labels = [label.get("title") for label in payload.get("labels", [])]

        logger.info(f"Processing Issue event: {action} - {issue_title} (ID: {issue_id}, Project: {project_name}, User: {user_name})")

        # 检查是否是新建或更新 Issue
        if action in ["open", "update"]:
            # 从环境变量获取允许的用户列表
            allowed_users_str = os.getenv("ALLOWED_USERS", "aider-worker,aider-worker-wsl1")
            allowed_users = [user.strip() for user in allowed_users_str.split(",")]
            is_allowed_user = user_name in allowed_users

            if not is_allowed_user:
                logger.info(f"Issue {issue_iid} not created by allowed user (created by {user_name})")
                return {"status": "ignored", "message": f"Issue {issue_iid} not created by allowed user"}

            # 检查是否包含特定标签，表明需要 AI 处理
            ai_related_labels = any(label in ["ai", "bot", "aider"] for label in labels)

            # 检查是否在标题或描述中提及 AI 相关关键词
            ai_mentioned_keywords = any(keyword in issue_title.lower() or keyword in issue_description.lower()
                              for keyword in ["ai", "bot", "aider"])

            # 检查是否包含特定指令前缀，表明这是一个直接的 AI 任务
            ai_task_prefixes = [
                "/ai", "/bot", "/aider",
                "ai:", "bot:", "aider:",
                "ai task:", "bot task:", "aider task:"
            ]
            has_ai_task_prefix = any(issue_title.lower().startswith(prefix.lower()) for prefix in ai_task_prefixes)

            # 检查描述是否包含足够的内容来触发 AI 处理
            # 如果描述超过 50 个字符，我们认为它可能包含足够的信息来处理
            has_substantial_description = len(issue_description.strip()) > 50

            # 决定是否处理此 Issue
            # 1. 如果是允许的用户，总是处理
            # 2. 如果有 AI 相关标签或明确提及 AI，则处理
            # 3. 如果使用了任务前缀，则处理
            # 4. 如果描述足够详细，也可以考虑处理
            should_process = True  # 对于允许的用户，总是处理

            if should_process:
                # 从dispatcher导入TaskRouter
                from bot_agent.dispatcher.router import TaskRouter

                # 创建任务路由器
                task_router = TaskRouter()

                # 准备任务标题 - 如果有前缀，则移除前缀
                task_title = issue_title
                if has_ai_task_prefix:
                    for prefix in ai_task_prefixes:
                        if issue_title.lower().startswith(prefix.lower()):
                            task_title = issue_title[len(prefix):].strip()
                            if task_title.startswith(":"):  # 移除可能的额外冒号
                                task_title = task_title[1:].strip()
                            break

                # 提取文件路径
                file_path = extract_file_path(issue_title, issue_description)

                # 将任务分发到任务分发器
                result = await task_router.route_task(
                    title=task_title,
                    description=issue_description,
                    source="gitlab",
                    source_id=f"{project_id}:{issue_iid}",
                    labels=labels,
                    metadata={
                        "project_id": project_id,
                        "project_name": project_name,
                        "issue_id": issue_id,
                        "issue_iid": issue_iid,
                        "action": action,
                        "event_type": "Issue Hook",
                        "file_path": file_path,
                        "processing_reason": "ai_label" if ai_related_labels else
                                            "ai_keyword" if ai_mentioned_keywords else
                                            "ai_prefix" if has_ai_task_prefix else
                                            "substantial_description"
                    }
                )

                logger.info(f"Issue task routed: {result}")
                return {
                    "status": "success",
                    "message": f"Issue {issue_iid} processed and routed to {result.get('target_component')}",
                    "task_id": result.get("task_id")
                }
            else:
                logger.info(f"Issue {issue_iid} does not require AI processing")
                return {"status": "ignored", "message": f"Issue {issue_iid} does not require AI processing"}

        return {"status": "ignored", "message": f"Issue action {action} not handled"}

    except Exception as e:
        logger.error(f"Error handling Issue event: {e}", exc_info=True)
        return {"status": "error", "message": f"Error handling Issue event: {str(e)}"}


async def handle_note_event(payload: Dict) -> Dict[str, str]:
    """
    处理评论事件，特别是 @mention

    Args:
        payload: GitLab Webhook 负载

    Returns:
        Dict: 处理结果
    """
    try:
        # 提取相关信息
        note_body = payload.get("object_attributes", {}).get("note")
        note_type = payload.get("object_attributes", {}).get("noteable_type")
        project_id = payload.get("project", {}).get("id")
        project_name = payload.get("project", {}).get("name")

        # 严格验证项目ID - 拒绝处理没有明确项目ID的webhook
        if not project_id:
            logger.error(f"Note event missing project ID - webhook rejected for security. Payload keys: {list(payload.keys())}")
            return {"status": "error", "message": "Missing project ID - webhook rejected for security"}

        # 获取用户信息
        user = payload.get("user", {})
        user_name = user.get("username", "")

        # 获取 Bot 名称（从环境变量或使用默认值）
        bot_names = os.getenv("BOT_NAMES", "aider-bot").split(",")
        bot_names = [name.strip() for name in bot_names]

        # 从环境变量获取允许的用户列表
        allowed_users_str = os.getenv("ALLOWED_USERS", "aider-worker,aider-worker-wsl1")
        allowed_users = [user.strip() for user in allowed_users_str.split(",")]
        is_allowed_user = user_name in allowed_users

        if not is_allowed_user:
            logger.info(f"Note not created by allowed user (created by {user_name})")
            return {"status": "ignored", "message": f"Note not created by allowed user"}

        # 检查是否是 Issue 评论
        if note_type == "Issue":
            issue_id = payload.get("issue", {}).get("id")
            issue_iid = payload.get("issue", {}).get("iid")
            issue_title = payload.get("issue", {}).get("title")
            issue_description = payload.get("issue", {}).get("description", "")

            # 获取标签
            labels = []
            if "labels" in payload.get("issue", {}):
                labels = [label.get("title") for label in payload.get("issue", {}).get("labels", [])]

            logger.info(f"Processing Note event on Issue: {issue_title} (ID: {issue_id}, Project: {project_name}, User: {user_name})")

            # 检查是否提及了 Bot
            mentioned_bot = None
            for bot_name in bot_names:
                if f"@{bot_name}" in note_body:
                    mentioned_bot = bot_name
                    break

            # 检查是否包含特定指令前缀，表明这是一个直接的 AI 任务
            ai_task_prefixes = [
                "/ai", "/bot", "/aider",
                "ai:", "bot:", "aider:",
                "ai task:", "bot task:", "aider task:"
            ]
            has_ai_task_prefix = any(note_body.lower().strip().startswith(prefix.lower()) for prefix in ai_task_prefixes)

            # 如果评论内容足够长，也可以考虑处理
            has_substantial_content = len(note_body.strip()) > 50

            # 决定是否处理此评论
            # 对于允许的用户，总是处理
            should_process = True

            if should_process:
                # 从dispatcher导入TaskRouter
                from bot_agent.dispatcher.router import TaskRouter

                # 创建任务路由器
                task_router = TaskRouter()

                # 准备任务内容 - 如果有前缀，则移除前缀
                task_content = note_body
                if has_ai_task_prefix and not mentioned_bot:
                    for prefix in ai_task_prefixes:
                        if note_body.lower().strip().startswith(prefix.lower()):
                            task_content = note_body[len(prefix):].strip()
                            if task_content.startswith(":"):  # 移除可能的额外冒号
                                task_content = task_content[1:].strip()
                            break

                # 智能处理任务标题和描述
                first_line = task_content.split('\n')[0].strip()

                # 检查是否是简单的继续指令
                continue_keywords = ["继续", "继续啊", "继续吧", "continue", "next", "go on", "proceed"]
                is_continue_command = any(keyword in first_line.lower() for keyword in continue_keywords) and len(first_line) < 20

                if is_continue_command:
                    # 对于继续指令，使用原始Issue标题
                    task_title = issue_title
                    logger.info(f"检测到继续指令，使用原始Issue标题: {task_title}")

                    # 构建任务描述 - 主要使用Issue描述，评论作为补充
                    task_description = f"""
## 原始任务需求
{issue_description}

## 用户指令
{task_content}

## 任务说明
用户要求继续处理上述任务需求，请根据Issue描述中的具体要求进行实现。
"""
                else:
                    # 对于具体任务，使用评论内容作为标题
                    task_title = (first_line[:50] + '...') if len(first_line) > 50 else first_line
                    if not task_title:
                        task_title = f"Comment on: {issue_title}"

                    # 构建任务描述 - 包含Issue描述和评论内容
                    task_description = f"""
## 原始Issue背景
{issue_description}

## 具体任务要求
{task_content}
"""

                # 提取文件路径
                file_path = extract_file_path(task_title, task_description)

                # 将任务分发到任务分发器
                result = await task_router.route_task(
                    title=task_title,
                    description=task_description,
                    source="gitlab",
                    source_id=f"{project_id}:{issue_iid}:comment",
                    labels=labels,
                    metadata={
                        "project_id": project_id,
                        "project_name": project_name,
                        "issue_id": issue_id,
                        "issue_iid": issue_iid,
                        "note_body": note_body,
                        "mentioned_bot": mentioned_bot,
                        "event_type": "Note Hook",
                        "file_path": file_path,
                        "processing_reason": "bot_mention" if mentioned_bot else
                                           "ai_prefix" if has_ai_task_prefix else
                                           "substantial_content"
                    }
                )

                logger.info(f"Comment task routed: {result}")
                return {
                    "status": "success",
                    "message": f"@{mentioned_bot} mention in issue {issue_iid} processed and routed to {result.get('target_component')}",
                    "task_id": result.get("task_id")
                }

        # 检查是否是 MR 评论
        elif note_type == "MergeRequest":
            mr_id = payload.get("merge_request", {}).get("id")
            mr_iid = payload.get("merge_request", {}).get("iid")
            mr_title = payload.get("merge_request", {}).get("title")

            logger.info(f"Processing Note event on MR: {mr_title} (ID: {mr_id}, Project: {project_name}, User: {user_name})")

            # 检查是否提及了 Bot
            mentioned_bot = None
            for bot_name in bot_names:
                if f"@{bot_name}" in note_body:
                    mentioned_bot = bot_name
                    break

            if mentioned_bot:
                # 从dispatcher导入TaskRouter
                from bot_agent.dispatcher.router import TaskRouter

                # 创建任务路由器
                task_router = TaskRouter()

                # 将任务分发到任务分发器
                result = await task_router.route_task(
                    title=f"Comment on MR: {mr_title}",
                    description=note_body,
                    source="gitlab",
                    source_id=f"{project_id}:{mr_iid}:mr_comment",
                    labels=[],
                    metadata={
                        "project_id": project_id,
                        "project_name": project_name,
                        "mr_id": mr_id,
                        "mr_iid": mr_iid,
                        "note_body": note_body,
                        "mentioned_bot": mentioned_bot,
                        "event_type": "Note Hook",
                        "note_type": "MergeRequest",
                    }
                )

                logger.info(f"MR Comment task routed: {result}")
                return {
                    "status": "success",
                    "message": f"@{mentioned_bot} mention in MR {mr_iid} processed and routed to {result.get('target_component')}",
                    "task_id": result.get("task_id")
                }

        return {"status": "ignored", "message": f"Note type {note_type} not handled or no bot mentioned"}

    except Exception as e:
        logger.error(f"Error handling Note event: {e}", exc_info=True)
        return {"status": "error", "message": f"Error handling Note event: {str(e)}"}


async def handle_merge_request_event(payload: Dict) -> Dict[str, str]:
    """
    处理合并请求事件

    Args:
        payload: GitLab Webhook 负载

    Returns:
        Dict: 处理结果
    """
    try:
        # 提取相关信息
        action = payload.get("object_attributes", {}).get("action")
        mr_id = payload.get("object_attributes", {}).get("id")
        mr_iid = payload.get("object_attributes", {}).get("iid")
        mr_title = payload.get("object_attributes", {}).get("title")
        mr_description = payload.get("object_attributes", {}).get("description", "")
        project_id = payload.get("project", {}).get("id")
        project_name = payload.get("project", {}).get("name")

        # 严格验证项目ID - 拒绝处理没有明确项目ID的webhook
        if not project_id:
            logger.error(f"Merge Request event missing project ID - webhook rejected for security. Payload keys: {list(payload.keys())}")
            return {"status": "error", "message": "Missing project ID - webhook rejected for security"}

        # 获取用户信息
        user = payload.get("user", {})
        user_name = user.get("username", "")

        # 获取源分支和目标分支
        source_branch = payload.get("object_attributes", {}).get("source_branch")
        target_branch = payload.get("object_attributes", {}).get("target_branch")

        # 获取标签
        labels = []
        if "labels" in payload:
            labels = [label.get("title") for label in payload.get("labels", [])]

        logger.info(f"Processing Merge Request event: {action} - {mr_title} (ID: {mr_id}, Project: {project_name}, User: {user_name})")

        # 从环境变量获取允许的用户列表
        allowed_users_str = os.getenv("ALLOWED_USERS", "aider-worker,aider-worker-wsl1")
        allowed_users = [user.strip() for user in allowed_users_str.split(",")]
        is_allowed_user = user_name in allowed_users

        if not is_allowed_user:
            logger.info(f"MR {mr_iid} not created by allowed user (created by {user_name})")
            return {"status": "ignored", "message": f"MR {mr_iid} not created by allowed user"}

        # 检查是否是打开或更新 MR
        if action in ["open", "update", "reopen"]:
            # 检查是否包含特定标签，表明需要 AI 代码审查
            ai_review_labels = any(label in ["ai-review", "bot-review", "aider-review"] for label in labels)

            # 检查是否在标题或描述中提及 AI 相关关键词
            ai_review_keywords = any(keyword in mr_title.lower() or keyword in mr_description.lower()
                              for keyword in ["ai review", "bot review", "aider review",
                                             "code review", "review code", "review my code"])

            # 检查是否包含特定指令前缀，表明这是一个直接的代码审查请求
            review_prefixes = [
                "/review", "/code-review", "/ai-review",
                "review:", "code review:", "ai review:"
            ]
            has_review_prefix = any(mr_title.lower().startswith(prefix.lower()) for prefix in review_prefixes)

            # 检查是否是首次创建的MR（新MR通常需要审查）
            is_new_mr = action == "open"

            # 检查是否是向主要分支（如main, master）的合并请求
            is_to_main_branch = target_branch in ["main", "master", "develop", "production", "release"]

            # 决定是否处理此MR
            # 对于允许的用户，总是处理
            should_process = True

            if should_process:
                # 从dispatcher导入TaskRouter
                from bot_agent.dispatcher.router import TaskRouter

                # 创建任务路由器
                task_router = TaskRouter()

                # 准备任务标题 - 如果有前缀，则移除前缀
                task_title = mr_title
                if has_review_prefix:
                    for prefix in review_prefixes:
                        if mr_title.lower().startswith(prefix.lower()):
                            task_title = mr_title[len(prefix):].strip()
                            if task_title.startswith(":"):  # 移除可能的额外冒号
                                task_title = task_title[1:].strip()
                            break

                # 构建更详细的描述，包含分支信息
                detailed_description = f"""
## 合并请求信息
- 源分支: {source_branch}
- 目标分支: {target_branch}
- 操作: {action}

## 描述
{mr_description}
"""

                # 将任务分发到任务分发器
                result = await task_router.route_task(
                    title=f"Code Review: {task_title}",
                    description=detailed_description,
                    source="gitlab",
                    source_id=f"{project_id}:{mr_iid}:mr",
                    labels=labels,
                    metadata={
                        "project_id": project_id,
                        "project_name": project_name,
                        "mr_id": mr_id,
                        "mr_iid": mr_iid,
                        "action": action,
                        "source_branch": source_branch,
                        "target_branch": target_branch,
                        "event_type": "Merge Request Hook",
                        "processing_reason": "ai_review_label" if ai_review_labels else
                                           "ai_review_keyword" if ai_review_keywords else
                                           "review_prefix" if has_review_prefix else
                                           "new_mr_to_main_branch"
                    }
                )

                logger.info(f"MR review task routed: {result}")
                return {
                    "status": "success",
                    "message": f"MR {mr_iid} processed for review and routed to {result.get('target_component')}",
                    "task_id": result.get("task_id")
                }
            else:
                logger.info(f"MR {mr_iid} does not require AI review")
                return {"status": "ignored", "message": f"MR {mr_iid} does not require AI review"}

        # 检查是否是合并 MR
        elif action == "merge":
            logger.info(f"MR {mr_iid} was merged")
            return {"status": "success", "message": f"MR {mr_iid} merge event recorded"}

        return {"status": "ignored", "message": f"Merge Request action {action} not handled"}

    except Exception as e:
        logger.error(f"Error handling Merge Request event: {e}", exc_info=True)
        return {"status": "error", "message": f"Error handling Merge Request event: {str(e)}"}


async def handle_push_event(payload: Dict) -> Dict[str, str]:
    """
    处理代码推送事件

    Args:
        payload: GitLab Webhook 负载

    Returns:
        Dict: 处理结果
    """
    try:
        # 提取相关信息
        project_id = payload.get("project_id")
        project_name = payload.get("project", {}).get("name") or payload.get("repository", {}).get("name")
        ref = payload.get("ref", "")  # 分支或标签引用
        user_name = payload.get("user_name")
        user_id = payload.get("user_id")

        # 严格验证项目ID - 拒绝处理没有明确项目ID的webhook
        if not project_id:
            logger.error(f"Push event missing project ID - webhook rejected for security. Payload keys: {list(payload.keys())}")
            return {"status": "error", "message": "Missing project ID - webhook rejected for security"}

        # 获取提交信息
        commits = payload.get("commits", [])
        commit_count = len(commits)

        # 提取分支名称
        branch_name = ref.replace("refs/heads/", "") if ref.startswith("refs/heads/") else ref

        logger.info(f"Processing Push event: {commit_count} commits to {branch_name} by {user_name} (Project: {project_name})")

        # 从环境变量获取允许的用户列表
        allowed_users_str = os.getenv("ALLOWED_USERS", "aider-worker,aider-worker-wsl1")
        allowed_users = [user.strip() for user in allowed_users_str.split(",")]
        is_allowed_user = user_name in allowed_users

        if not is_allowed_user:
            logger.info(f"Push not created by allowed user (created by {user_name})")
            return {"status": "ignored", "message": f"Push not created by allowed user"}

        # 检查是否包含特定关键词，表明需要 AI 处理
        ai_related_commits = []
        for commit in commits:
            commit_message = commit.get("message", "").lower()
            # 检查提交消息中是否包含AI相关关键词
            ai_keywords = ["ai review", "bot review", "aider", "code review", "review code"]
            # 检查提交消息中是否包含特定指令前缀
            ai_prefixes = ["/ai", "/review", "/bot", "/aider"]

            if (any(keyword in commit_message for keyword in ai_keywords) or
                any(commit_message.startswith(prefix) for prefix in ai_prefixes)):
                ai_related_commits.append(commit)

        # 检查是否有AI相关提交
        has_ai_related_commits = len(ai_related_commits) > 0

        # 检查是否推送到主要分支
        is_main_branch = branch_name in ["main", "master", "develop", "dev", "production", "release"]

        # 检查提交数量是否超过阈值（大量提交可能需要审查）
        has_many_commits = commit_count >= 5

        # 决定是否处理此推送
        # 对于允许的用户，总是处理
        should_process = True

        # 如果是 AI 相关提交或推送到主要分支，可以考虑触发代码审查
        if should_process:
            # 从dispatcher导入TaskRouter
            from bot_agent.dispatcher.router import TaskRouter

            # 创建任务路由器
            task_router = TaskRouter()

            # 构建提交信息摘要
            # 限制为前 10 个提交
            commit_lines = []
            for commit in commits[:10]:
                commit_id = commit.get('id')[:8]
                commit_message = commit.get('message').split('\n')[0]
                commit_lines.append(f"- {commit_id}: {commit_message}")
            commit_summary = "\n".join(commit_lines)

            if len(commits) > 10:
                commit_summary += f"\n- ... and {len(commits) - 10} more commits"

            # 构建更详细的描述
            detailed_description = f"""
## 推送信息
- 分支: {branch_name}
- 提交数量: {commit_count}
- 推送者: {user_name}

## 提交列表
{commit_summary}
"""

            # 将任务分发到任务分发器
            result = await task_router.route_task(
                title=f"Push to {branch_name}: {commit_count} commits",
                description=detailed_description,
                source="gitlab",
                source_id=f"{project_id}:push:{branch_name}",
                labels=["push-event"],
                metadata={
                    "project_id": project_id,
                    "project_name": project_name,
                    "branch": branch_name,
                    "user_name": user_name,
                    "user_id": user_id,
                    "commit_count": commit_count,
                    "event_type": "Push Hook",
                    "processing_reason": "ai_related_commits" if has_ai_related_commits else "main_branch_many_commits",
                    "ai_related_commits": [commit.get("id") for commit in ai_related_commits] if has_ai_related_commits else [],
                    "is_main_branch": is_main_branch,
                    "has_many_commits": has_many_commits
                }
            )

            logger.info(f"Push event task routed: {result}")
            return {
                "status": "success",
                "message": f"Push to {branch_name} processed and routed to {result.get('target_component')}",
                "task_id": result.get("task_id")
            }

        # 记录事件但不处理
        return {"status": "ignored", "message": f"Push to {branch_name} recorded but not processed"}

    except Exception as e:
        logger.error(f"Error handling Push event: {e}", exc_info=True)
        return {"status": "error", "message": f"Error handling Push event: {str(e)}"}


async def handle_tag_push_event(payload: Dict) -> Dict[str, str]:
    """
    处理标签推送事件

    Args:
        payload: GitLab Webhook 负载

    Returns:
        Dict: 处理结果
    """
    try:
        # 提取相关信息
        project_id = payload.get("project_id")
        project_name = payload.get("project", {}).get("name") or payload.get("repository", {}).get("name")
        ref = payload.get("ref", "")  # 标签引用
        user_name = payload.get("user_name")

        # 严格验证项目ID - 拒绝处理没有明确项目ID的webhook
        if not project_id:
            logger.error(f"Tag event missing project ID - webhook rejected for security. Payload keys: {list(payload.keys())}")
            return {"status": "error", "message": "Missing project ID - webhook rejected for security"}

        # 提取标签名称
        tag_name = ref.replace("refs/tags/", "") if ref.startswith("refs/tags/") else ref

        logger.info(f"Processing Tag Push event: {tag_name} by {user_name} (Project: {project_name})")

        # 从环境变量获取允许的用户列表
        allowed_users_str = os.getenv("ALLOWED_USERS", "aider-worker,aider-worker-wsl1")
        allowed_users = [user.strip() for user in allowed_users_str.split(",")]
        is_allowed_user = user_name in allowed_users

        if not is_allowed_user:
            logger.info(f"Tag push not created by allowed user (created by {user_name})")
            return {"status": "ignored", "message": f"Tag push not created by allowed user"}

        # 检查是否是版本标签
        is_version_tag = bool(re.match(r'^v?\d+(\.\d+)*(-\w+)?$', tag_name))

        if is_version_tag:
            logger.info(f"Version tag detected: {tag_name}")
            # 这里可以添加版本发布相关的处理逻辑

        # 记录事件但不处理
        return {"status": "success", "message": f"Tag {tag_name} push recorded"}

    except Exception as e:
        logger.error(f"Error handling Tag Push event: {e}", exc_info=True)
        return {"status": "error", "message": f"Error handling Tag Push event: {str(e)}"}


async def handle_pipeline_event(payload: Dict) -> Dict[str, str]:
    """
    处理流水线事件 - 自动化部署监控和AI集成

    Args:
        payload: GitLab Webhook 负载

    Returns:
        Dict: 处理结果
    """
    try:
        # 提取相关信息
        project_id = payload.get("project", {}).get("id")
        project_name = payload.get("project", {}).get("name")
        pipeline_id = payload.get("object_attributes", {}).get("id")
        status = payload.get("object_attributes", {}).get("status")
        ref = payload.get("object_attributes", {}).get("ref")
        source = payload.get("object_attributes", {}).get("source")
        stages = payload.get("object_attributes", {}).get("stages", [])

        # 严格验证项目ID
        if not project_id:
            logger.error(f"Pipeline event missing project ID - webhook rejected for security. Payload keys: {list(payload.keys())}")
            return {"status": "error", "message": "Missing project ID - webhook rejected for security"}

        # 获取用户信息
        user = payload.get("user", {})
        user_name = user.get("username", "")

        logger.info(f"Processing Pipeline event: Pipeline {pipeline_id} for {ref} is {status} (Project: {project_name}, User: {user_name})")

        # 从环境变量获取允许的用户列表
        allowed_users_str = os.getenv("ALLOWED_USERS", "aider-worker,aider-worker-wsl1")
        allowed_users = [user.strip() for user in allowed_users_str.split(",")]
        is_allowed_user = user_name in allowed_users

        if not is_allowed_user:
            logger.info(f"Pipeline not created by allowed user (created by {user_name})")
            return {"status": "ignored", "message": f"Pipeline not created by allowed user"}

        # 检查是否包含部署阶段
        has_deployment = any(stage.lower() in ['deploy', 'deployment', 'release', 'production', 'staging']
                           for stage in stages)

        # 检查是否是主要分支
        main_branches = ['main', 'master', 'develop', 'production']
        is_main_branch = any(branch in ref.lower() for branch in main_branches)

        # 决定是否需要AI监控
        needs_ai_monitoring = False
        monitoring_reason = ""

        if status == "failed" and (has_deployment or is_main_branch):
            needs_ai_monitoring = True
            monitoring_reason = "deployment_failure"
        elif status == "success" and has_deployment:
            needs_ai_monitoring = True
            monitoring_reason = "deployment_success_verification"
        elif status == "running" and has_deployment:
            needs_ai_monitoring = True
            monitoring_reason = "deployment_monitoring"

        # 如果需要AI监控，创建任务
        if needs_ai_monitoring:
            from bot_agent.dispatcher.router import TaskRouter

            task_router = TaskRouter()

            # 构建任务标题和描述
            if status == "failed":
                task_title = f"部署失败分析 - Pipeline {pipeline_id}"
                task_description = f"""
## 🚨 部署失败自动分析

**项目**: {project_name}
**管道ID**: {pipeline_id}
**分支**: {ref}
**状态**: {status}
**阶段**: {', '.join(stages)}

### 任务要求
1. 分析部署失败原因
2. 收集相关日志和错误信息
3. 提供修复建议
4. 如果可能，尝试自动修复

### 自动化指令
请立即开始分析Pipeline {pipeline_id}的失败原因，收集日志，并提供详细的修复方案。
"""
            elif status == "success":
                task_title = f"部署成功验证 - Pipeline {pipeline_id}"
                task_description = f"""
## ✅ 部署成功验证

**项目**: {project_name}
**管道ID**: {pipeline_id}
**分支**: {ref}
**状态**: {status}

### 任务要求
1. 验证部署是否真正成功
2. 检查服务健康状态
3. 验证关键功能是否正常
4. 生成部署报告

### 自动化指令
请验证Pipeline {pipeline_id}的部署结果，确保所有服务正常运行。
"""
            else:  # running
                task_title = f"部署监控 - Pipeline {pipeline_id}"
                task_description = f"""
## 🔄 实时部署监控

**项目**: {project_name}
**管道ID**: {pipeline_id}
**分支**: {ref}
**状态**: {status}

### 任务要求
1. 实时监控部署进度
2. 检测潜在问题
3. 在出现问题时立即报告
4. 准备回滚方案

### 自动化指令
请持续监控Pipeline {pipeline_id}的部署进度，及时发现和报告问题。
"""

            # 路由任务到AI处理器
            result = await task_router.route_task(
                title=task_title,
                description=task_description,
                source="gitlab",
                source_id=f"{project_id}:pipeline:{pipeline_id}",
                labels=["pipeline-event", "deployment", status],
                metadata={
                    "project_id": project_id,
                    "project_name": project_name,
                    "pipeline_id": pipeline_id,
                    "status": status,
                    "ref": ref,
                    "source": source,
                    "stages": stages,
                    "user_name": user_name,
                    "event_type": "Pipeline Hook",
                    "monitoring_reason": monitoring_reason,
                    "has_deployment": has_deployment,
                    "is_main_branch": is_main_branch,
                    "auto_triggered": True
                }
            )

            logger.info(f"Pipeline event task routed: {result}")
            return {
                "status": "success",
                "message": f"Pipeline {pipeline_id} ({status}) routed to AI for {monitoring_reason}",
                "task_id": result.get("task_id"),
                "monitoring_reason": monitoring_reason
            }

        # 记录事件但不需要AI处理
        logger.info(f"Pipeline {pipeline_id} status {status} recorded (no AI monitoring needed)")
        return {"status": "recorded", "message": f"Pipeline {pipeline_id} status {status} recorded"}

    except Exception as e:
        logger.error(f"Error handling Pipeline event: {e}", exc_info=True)
        return {"status": "error", "message": f"Error handling Pipeline event: {str(e)}"}


async def handle_job_event(payload: Dict) -> Dict[str, str]:
    """
    处理Job事件 - 细粒度的作业监控和自动化响应

    Args:
        payload: GitLab Webhook 负载

    Returns:
        Dict: 处理结果
    """
    try:
        # 提取相关信息
        project_id = payload.get("project_id")
        project_name = payload.get("project", {}).get("name", "unknown")
        build_id = payload.get("build_id")
        build_name = payload.get("build_name")
        build_stage = payload.get("build_stage")
        build_status = payload.get("build_status")
        build_failure_reason = payload.get("build_failure_reason")
        pipeline_id = payload.get("pipeline_id")
        ref = payload.get("ref")

        # 严格验证项目ID
        if not project_id:
            logger.error(f"Job event missing project ID - webhook rejected for security. Payload keys: {list(payload.keys())}")
            return {"status": "error", "message": "Missing project ID - webhook rejected for security"}

        # 获取用户信息
        user = payload.get("user", {})
        user_name = user.get("username", "")

        logger.info(f"Processing Job event: Job {build_name} ({build_id}) in stage {build_stage} is {build_status} (Pipeline: {pipeline_id}, Project: {project_name}, User: {user_name})")

        # 从环境变量获取允许的用户列表
        allowed_users_str = os.getenv("ALLOWED_USERS", "aider-worker,aider-worker-wsl1")
        allowed_users = [user.strip() for user in allowed_users_str.split(",")]
        is_allowed_user = user_name in allowed_users

        if not is_allowed_user:
            logger.info(f"Job not created by allowed user (created by {user_name})")
            return {"status": "ignored", "message": f"Job not created by allowed user"}

        # 检查是否是关键作业
        critical_jobs = ["deploy", "deployment", "release", "production", "staging", "build", "test"]
        is_critical_job = any(keyword in build_name.lower() or keyword in build_stage.lower()
                             for keyword in critical_jobs)

        # 检查是否是主要分支
        main_branches = ['main', 'master', 'develop', 'production']
        is_main_branch = any(branch in ref.lower() for branch in main_branches)

        # 决定是否需要AI处理
        needs_ai_processing = False
        processing_reason = ""

        if build_status == "failed" and is_critical_job:
            needs_ai_processing = True
            processing_reason = "critical_job_failure"
        elif build_status == "failed" and is_main_branch:
            needs_ai_processing = True
            processing_reason = "main_branch_job_failure"
        elif build_status == "failed" and "deploy" in build_name.lower():
            needs_ai_processing = True
            processing_reason = "deployment_job_failure"
        elif build_status == "success" and "deploy" in build_name.lower():
            needs_ai_processing = True
            processing_reason = "deployment_job_success"

        # 如果需要AI处理，创建任务
        if needs_ai_processing:
            from bot_agent.dispatcher.router import TaskRouter

            task_router = TaskRouter()

            # 构建任务标题和描述
            if build_status == "failed":
                task_title = f"作业失败分析 - {build_name} (Job {build_id})"
                task_description = f"""
## 🚨 作业失败自动分析

**项目**: {project_name}
**作业名称**: {build_name}
**作业ID**: {build_id}
**Pipeline ID**: {pipeline_id}
**阶段**: {build_stage}
**分支**: {ref}
**状态**: {build_status}
**失败原因**: {build_failure_reason or '未知'}

### 任务要求
1. 分析作业失败的具体原因
2. 收集作业日志和错误信息
3. 提供针对性的修复建议
4. 如果是部署作业，评估回滚需求

### 自动化指令
请立即分析Job {build_id}的失败原因，收集详细日志，并提供修复方案。
"""
            else:  # success
                task_title = f"作业成功验证 - {build_name} (Job {build_id})"
                task_description = f"""
## ✅ 作业成功验证

**项目**: {project_name}
**作业名称**: {build_name}
**作业ID**: {build_id}
**Pipeline ID**: {pipeline_id}
**阶段**: {build_stage}
**分支**: {ref}
**状态**: {build_status}

### 任务要求
1. 验证作业执行结果
2. 检查输出和产物
3. 如果是部署作业，执行健康检查
4. 生成作业执行报告

### 自动化指令
请验证Job {build_id}的执行结果，确保一切正常。
"""

            # 路由任务到AI处理器
            result = await task_router.route_task(
                title=task_title,
                description=task_description,
                source="gitlab",
                source_id=f"{project_id}:job:{build_id}",
                labels=["job-event", build_stage, build_status],
                metadata={
                    "project_id": project_id,
                    "project_name": project_name,
                    "build_id": build_id,
                    "build_name": build_name,
                    "build_stage": build_stage,
                    "build_status": build_status,
                    "build_failure_reason": build_failure_reason,
                    "pipeline_id": pipeline_id,
                    "ref": ref,
                    "user_name": user_name,
                    "event_type": "Job Hook",
                    "processing_reason": processing_reason,
                    "is_critical_job": is_critical_job,
                    "is_main_branch": is_main_branch,
                    "auto_triggered": True
                }
            )

            logger.info(f"Job event task routed: {result}")
            return {
                "status": "success",
                "message": f"Job {build_name} ({build_status}) routed to AI for {processing_reason}",
                "task_id": result.get("task_id"),
                "processing_reason": processing_reason
            }

        # 记录事件但不需要AI处理
        logger.info(f"Job {build_name} status {build_status} recorded (no AI processing needed)")
        return {"status": "recorded", "message": f"Job {build_name} status {build_status} recorded"}

    except Exception as e:
        logger.error(f"Error handling Job event: {e}", exc_info=True)
        return {"status": "error", "message": f"Error handling Job event: {str(e)}"}
