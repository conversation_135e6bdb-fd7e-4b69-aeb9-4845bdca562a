"""
工具路由器 - 智能工具选择和执行
"""

import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .base_tool import BaseTool, ToolResult, tool_registry
from .terminal_tools import TerminalTools
from .testing_tools import TestingTools
from .log_analysis_tools import LogAnalysisTools
from .database_tools import DatabaseTools
from .dependency_tools import DependencyTools
from .debug_tools import DebugTools
from .frontend_debug_tools import FrontendDebugTools
from .refactor_tools import RefactorTools
from .documentation_tools import DocumentationTools
from .security_tools import SecurityTools
from .code_generation_tools import CodeGenerationTools
from .information_query_tools import InformationQueryTools

logger = logging.getLogger(__name__)


@dataclass
class ToolSuggestion:
    """工具建议"""
    tool_name: str
    confidence: float
    reason: str
    parameters: Dict[str, Any]
    command: Optional[str] = None


class ToolRouter:
    """
    工具路由器

    功能：
    1. 智能分析任务需求
    2. 选择合适的工具
    3. 生成工具调用参数
    4. 执行工具操作
    5. 结果整合和反馈
    """

    def __init__(self):
        self.tools = {}
        self.patterns = self._init_patterns()
        self._register_tools()

    def _register_tools(self):
        """注册所有工具"""
        self.tools = {
            'information_query': InformationQueryTools(),
            'terminal': TerminalTools(),
            'testing': TestingTools(),
            'log_analysis': LogAnalysisTools(),
            'database': DatabaseTools(),
            'dependency': DependencyTools(),
            'debug': DebugTools(),
            'frontend_debug': FrontendDebugTools(),
            'refactor': RefactorTools(),
            'documentation': DocumentationTools(),
            'security': SecurityTools(),
            'code_generation': CodeGenerationTools(),
        }

        # 注册到全局注册表
        for tool in self.tools.values():
            tool_registry.register(tool)

        logger.info(f"注册了 {len(self.tools)} 个工具")

    def _init_patterns(self) -> Dict[str, Dict]:
        """初始化任务模式匹配"""
        return {
            'information_query': {
                'keywords': [
                    '查询', '列出', '显示', '查看', '获取', '哪些', '什么', '怎么',
                    'show', 'list', 'display', 'get', 'what', 'which', 'how',
                    '镜像', 'image', 'docker', '依赖', 'dependency', '配置', 'config',
                    '结构', 'structure', '信息', 'info', '状态', 'status'
                ],
                'patterns': [
                    r'用的哪些',
                    r'列出.*',
                    r'显示.*',
                    r'查看.*',
                    r'有哪些',
                    r'都有什么',
                    r'帮我.*列出',
                    r'show.*',
                    r'list.*'
                ],
                'confidence_boost': 0.9
            },
            'testing': {
                'keywords': [
                    '测试', 'test', 'pytest', '单元测试', '集成测试',
                    '覆盖率', 'coverage', '性能测试', 'benchmark',
                    'api测试', '自动化测试'
                ],
                'patterns': [
                    r'运行.*测试',
                    r'执行.*test',
                    r'测试.*覆盖率',
                    r'performance.*test',
                    r'api.*test'
                ],
                'confidence_boost': 0.8
            },
            'terminal': {
                'keywords': [
                    '命令', 'command', '执行', 'run', 'shell',
                    '脚本', 'script', '进程', 'process', '系统',
                    '安装', 'install', '启动', 'start', '停止', 'stop'
                ],
                'patterns': [
                    r'执行.*命令',
                    r'运行.*脚本',
                    r'安装.*包',
                    r'启动.*服务',
                    r'检查.*进程'
                ],
                'confidence_boost': 0.7
            },
            'log_analysis': {
                'keywords': [
                    '日志', 'log', '错误', 'error', '异常', 'exception',
                    '分析', 'analysis', '诊断', 'debug', '监控', 'monitor'
                ],
                'patterns': [
                    r'分析.*日志',
                    r'查看.*错误',
                    r'诊断.*问题',
                    r'监控.*系统',
                    r'检查.*异常'
                ],
                'confidence_boost': 0.9
            },
            'database': {
                'keywords': [
                    '数据库', 'database', 'sql', 'mysql', 'postgresql',
                    '查询', 'query', '连接', 'connection', '迁移', 'migration'
                ],
                'patterns': [
                    r'数据库.*操作',
                    r'执行.*sql',
                    r'查询.*数据',
                    r'连接.*数据库'
                ],
                'confidence_boost': 0.8
            },
            'dependency': {
                'keywords': [
                    '依赖', 'dependency', 'requirements', 'package',
                    '安装', 'install', 'pip', 'npm', 'yarn', '包管理'
                ],
                'patterns': [
                    r'安装.*依赖',
                    r'更新.*包',
                    r'管理.*依赖',
                    r'requirements.*txt'
                ],
                'confidence_boost': 0.7
            }
        }

    async def analyze_task(self, task_description: str, context: Dict = None) -> List[ToolSuggestion]:
        """
        分析任务并建议工具

        Args:
            task_description: 任务描述
            context: 上下文信息

        Returns:
            List[ToolSuggestion]: 工具建议列表
        """
        try:
            logger.info(f"分析任务: {task_description}")

            suggestions = []
            text = task_description.lower()

            # 为每个工具类型计算匹配度
            for tool_type, config in self.patterns.items():
                confidence = self._calculate_confidence(text, config)

                if confidence > 0.05:  # 进一步降低置信度阈值
                    # 生成参数
                    parameters = self._generate_parameters(
                        tool_type, task_description, context
                    )

                    # 生成命令建议
                    command = self._generate_command(
                        tool_type, task_description, parameters
                    )

                    suggestion = ToolSuggestion(
                        tool_name=tool_type,
                        confidence=confidence,
                        reason=self._generate_reason(tool_type, text, config),
                        parameters=parameters,
                        command=command
                    )

                    suggestions.append(suggestion)

            # 按置信度排序
            suggestions.sort(key=lambda x: x.confidence, reverse=True)

            logger.info(f"生成了 {len(suggestions)} 个工具建议")
            return suggestions

        except Exception as e:
            logger.error(f"任务分析失败: {e}", exc_info=True)
            return []

    def _calculate_confidence(self, text: str, config: Dict) -> float:
        """计算工具匹配置信度"""
        confidence = 0.0

        # 关键词匹配 - 改进计算方式
        keyword_matches = sum(1 for keyword in config['keywords'] if keyword in text)
        if keyword_matches > 0:
            # 给予更高的基础分数，特别是对于多个关键词匹配
            keyword_score = min(keyword_matches * 0.15, 0.7)  # 每个关键词0.15分，最高0.7
            confidence += keyword_score

        # 模式匹配
        pattern_matches = sum(1 for pattern in config['patterns'] if re.search(pattern, text))
        if pattern_matches > 0:
            pattern_score = min(pattern_matches * 0.2, 0.6)  # 每个模式0.2分，最高0.6
            confidence += pattern_score

        # 如果有任何匹配，应用置信度提升
        if confidence > 0:
            confidence = min(confidence * config['confidence_boost'], 1.0)

        # 特殊处理：如果没有匹配但是包含查询相关词汇，给予最低分
        if confidence == 0:
            query_indicators = ['查询', '列出', '显示', '查看', '哪些', '什么', 'list', 'show', 'what']
            if any(indicator in text for indicator in query_indicators):
                confidence = 0.1  # 给予最低置信度

        # 调试输出
        logger.debug(f"工具匹配分析 - 关键词匹配: {keyword_matches}, 模式匹配: {pattern_matches}, 最终置信度: {confidence}")

        return confidence

    def _generate_parameters(self, tool_type: str, description: str, context: Dict = None) -> Dict:
        """生成工具参数"""
        context = context or {}

        if tool_type == 'information_query':
            # 检测查询类型
            query_type = 'auto'
            desc_lower = description.lower()

            if any(keyword in desc_lower for keyword in ['镜像', 'image', 'docker']):
                query_type = 'docker'
            elif any(keyword in desc_lower for keyword in ['依赖', 'dependency', 'requirements']):
                query_type = 'dependencies'
            elif any(keyword in desc_lower for keyword in ['结构', 'structure', '目录']):
                query_type = 'structure'
            elif any(keyword in desc_lower for keyword in ['配置', 'config', 'gitlab']):
                query_type = 'config'
            elif any(keyword in desc_lower for keyword in ['git', '分支', 'branch']):
                query_type = 'git'

            return {
                'query_type': query_type,
                'query_text': description,
                'project_path': context.get('project_path', '.')
            }

        elif tool_type == 'testing':
            return {
                'project_path': context.get('project_path', '.'),
                'verbose': True,
                'parallel': 'parallel' in description.lower() or '并行' in description
            }

        elif tool_type == 'terminal':
            # 尝试从描述中提取命令
            command = self._extract_command(description)
            return {
                'command': command,
                'cwd': context.get('project_path', '.'),
                'timeout': 60
            }

        elif tool_type == 'log_analysis':
            return {
                'project_path': context.get('project_path', '.'),
                'hours': 24  # 默认分析最近24小时
            }

        else:
            return {}

    def _extract_command(self, description: str) -> str:
        """从描述中提取命令"""
        # 查找常见的命令模式
        command_patterns = [
            r'执行[`"\'](.*?)[`"\']',
            r'运行[`"\'](.*?)[`"\']',
            r'命令[：:]\s*[`"\'](.*?)[`"\']',
            r'`([^`]+)`',
            r'"([^"]+)"',
        ]

        for pattern in command_patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(1)

        # 如果没有找到明确的命令，返回描述本身
        return description

    def _generate_command(self, tool_type: str, description: str, parameters: Dict) -> str:
        """生成shell命令建议"""
        if tool_type == 'information_query':
            query_type = parameters.get('query_type', 'auto')

            if query_type == 'docker':
                return "find . -name 'Dockerfile' -o -name 'docker-compose.yml' -o -name '.gitlab-ci.yml' | head -5"
            elif query_type == 'dependencies':
                return "find . -name 'requirements.txt' -o -name 'package.json' -o -name 'pyproject.toml' | head -5"
            elif query_type == 'structure':
                return "tree -L 3 -I '__pycache__|*.pyc|.git'"
            elif query_type == 'config':
                return "find . -name '*.yml' -o -name '*.yaml' -o -name '*.toml' -o -name '*.json' | head -10"
            elif query_type == 'git':
                return "git status && git branch -a"
            else:
                return "ls -la"

        elif tool_type == 'testing':
            if '单元测试' in description or 'unit test' in description.lower():
                return "python -m pytest -v"
            elif '覆盖率' in description or 'coverage' in description.lower():
                return "python -m coverage run -m pytest && python -m coverage report"
            elif '性能测试' in description or 'performance' in description.lower():
                return "python -m pytest --benchmark-only"
            else:
                return "python -m pytest"

        elif tool_type == 'terminal':
            return parameters.get('command', 'echo "请指定具体命令"')

        elif tool_type == 'log_analysis':
            return "find . -name '*.log' -type f | head -5"

        else:
            return ""

    def _generate_reason(self, tool_type: str, text: str, config: Dict) -> str:
        """生成选择理由"""
        matched_keywords = [kw for kw in config['keywords'] if kw in text]
        matched_patterns = [p for p in config['patterns'] if re.search(p, text)]

        reason_parts = []

        if matched_keywords:
            reason_parts.append(f"匹配关键词: {', '.join(matched_keywords[:3])}")

        if matched_patterns:
            reason_parts.append(f"匹配模式: {len(matched_patterns)}个")

        return "; ".join(reason_parts) if reason_parts else f"适合{tool_type}操作"

    async def execute_suggestion(self, suggestion: ToolSuggestion, context: Dict = None) -> ToolResult:
        """
        执行工具建议

        Args:
            suggestion: 工具建议
            context: 执行上下文

        Returns:
            ToolResult: 执行结果
        """
        try:
            tool = self.tools.get(suggestion.tool_name)
            if not tool:
                return ToolResult(
                    success=False,
                    error=f"工具 {suggestion.tool_name} 不存在"
                )

            logger.info(f"执行工具: {suggestion.tool_name}")

            # 合并上下文参数
            if context:
                suggestion.parameters.update(context)

            # 执行工具
            result = await tool.execute(**suggestion.parameters)

            # 添加工具信息到结果
            if result.metadata is None:
                result.metadata = {}

            result.metadata.update({
                'tool_name': suggestion.tool_name,
                'confidence': suggestion.confidence,
                'reason': suggestion.reason,
                'command': suggestion.command
            })

            return result

        except Exception as e:
            logger.error(f"执行工具建议失败: {e}", exc_info=True)
            return ToolResult(
                success=False,
                error=str(e),
                message=f"工具 {suggestion.tool_name} 执行失败"
            )

    async def auto_execute(self, task_description: str, context: Dict = None) -> List[ToolResult]:
        """
        自动分析并执行任务

        Args:
            task_description: 任务描述
            context: 上下文信息

        Returns:
            List[ToolResult]: 执行结果列表
        """
        try:
            # 分析任务
            suggestions = await self.analyze_task(task_description, context)

            if not suggestions:
                return [ToolResult(
                    success=False,
                    error="无法识别合适的工具",
                    message="请提供更具体的任务描述"
                )]

            # 执行最佳建议
            best_suggestion = suggestions[0]

            if best_suggestion.confidence < 0.5:
                return [ToolResult(
                    success=False,
                    error=f"工具匹配置信度过低: {best_suggestion.confidence:.2f}",
                    message="请提供更明确的任务描述"
                )]

            result = await self.execute_suggestion(best_suggestion, context)
            return [result]

        except Exception as e:
            logger.error(f"自动执行失败: {e}", exc_info=True)
            return [ToolResult(
                success=False,
                error=str(e),
                message="自动执行失败"
            )]

    def get_tool_capabilities(self) -> Dict[str, List[str]]:
        """获取所有工具的能力"""
        return {
            name: tool.get_capabilities()
            for name, tool in self.tools.items()
        }

    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return list(self.tools.keys())
