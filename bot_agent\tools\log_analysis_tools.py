"""
日志分析工具 - 智能日志分析和问题诊断
"""

import os
import re
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from collections import defaultdict, Counter

from .base_tool import BaseTool, ToolResult

logger = logging.getLogger(__name__)


class LogAnalysisTools(BaseTool):
    """
    日志分析工具

    功能：
    1. 智能日志解析
    2. 错误模式识别
    3. 性能分析
    4. 异常检测
    5. 修复建议生成
    """

    def __init__(self):
        super().__init__()
        self.log_patterns = self._init_log_patterns()
        self.error_patterns = self._init_error_patterns()
        self.performance_patterns = self._init_performance_patterns()

    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'analyze_logs',
            'get_log_insights',
            'detect_errors',
            'performance_analysis',
            'health_scoring',
            'generate_recommendations',
            'pattern_matching',
            'timeline_analysis'
        ]

    def _init_log_patterns(self) -> Dict[str, re.Pattern]:
        """初始化日志解析模式"""
        return {
            'timestamp': re.compile(r'(\d{4}-\d{2}-\d{2}[\s\\T]\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)', re.IGNORECASE),
            'level': re.compile(r'\b(DEBUG|INFO|WARN|WARNING|ERROR|CRITICAL|FATAL)\b', re.IGNORECASE),
            'python_traceback': re.compile(r'Traceback \(most recent call last\):', re.IGNORECASE),
            'exception': re.compile(r'(\w+Error|\w+Exception):\s*(.+)', re.IGNORECASE),
            'http_status': re.compile(r'\b(1\d{2}|2\d{2}|3\d{2}|4\d{2}|5\d{2})\b'),
            'ip_address': re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b'),
            'memory_usage': re.compile(r'memory[:\s]*(\d+(?:\.\d+)?)\s*(MB|GB|KB)', re.IGNORECASE),
            'response_time': re.compile(r'(?:response|time|duration)[:\s]*(\d+(?:\.\d+)?)\s*(ms|s|seconds?)', re.IGNORECASE),
        }

    def _init_error_patterns(self) -> Dict[str, Dict]:
        """初始化错误模式和解决方案"""
        return {
            'connection_error': {
                'patterns': [
                    r'connection\s+(?:refused|failed|timeout)',
                    r'unable\s+to\s+connect',
                    r'network\s+(?:unreachable|error)',
                ],
                'severity': 'high',
                'category': 'network',
                'solutions': [
                    '检查网络连接',
                    '验证服务是否运行',
                    '检查防火墙设置',
                    '验证端口是否开放'
                ]
            },
            'database_error': {
                'patterns': [
                    r'database\s+(?:connection|error)',
                    r'sql\s+(?:error|exception)',
                    r'table\s+(?:not\s+found|doesn\'t\s+exist)',
                    r'duplicate\s+entry',
                ],
                'severity': 'high',
                'category': 'database',
                'solutions': [
                    '检查数据库连接配置',
                    '验证数据库服务状态',
                    '检查SQL语法',
                    '验证表结构和权限'
                ]
            },
            'memory_error': {
                'patterns': [
                    r'out\s+of\s+memory',
                    r'memory\s+(?:error|exception)',
                    r'heap\s+space',
                    r'cannot\s+allocate\s+memory',
                ],
                'severity': 'critical',
                'category': 'performance',
                'solutions': [
                    '增加内存分配',
                    '优化内存使用',
                    '检查内存泄漏',
                    '调整垃圾回收参数'
                ]
            },
            'permission_error': {
                'patterns': [
                    r'permission\s+denied',
                    r'access\s+denied',
                    r'unauthorized',
                    r'forbidden',
                ],
                'severity': 'medium',
                'category': 'security',
                'solutions': [
                    '检查文件权限',
                    '验证用户权限',
                    '检查认证配置',
                    '更新访问控制列表'
                ]
            },
            'import_error': {
                'patterns': [
                    r'modulenotfounderror',
                    r'importerror',
                    r'no\s+module\s+named',
                    r'cannot\s+import\s+name',
                ],
                'severity': 'medium',
                'category': 'dependency',
                'solutions': [
                    '安装缺失的依赖包',
                    '检查Python路径',
                    '验证虚拟环境',
                    '更新requirements.txt'
                ]
            }
        }

    def _init_performance_patterns(self) -> Dict[str, Dict]:
        """初始化性能模式"""
        return {
            'slow_query': {
                'pattern': r'query\s+took\s+(\d+(?:\.\d+)?)\s*(ms|s)',
                'threshold': 1000,  # ms
                'category': 'database_performance'
            },
            'high_memory': {
                'pattern': r'memory\s+usage[:\s]*(\d+(?:\.\d+)?)\s*%',
                'threshold': 80,  # %
                'category': 'memory_performance'
            },
            'high_cpu': {
                'pattern': r'cpu\s+usage[:\s]*(\d+(?:\.\d+)?)\s*%',
                'threshold': 90,  # %
                'category': 'cpu_performance'
            }
        }

    async def analyze_logs(self, log_paths: List[str], time_range: Optional[Tuple[datetime, datetime]] = None) -> ToolResult:
        """
        分析日志文件

        Args:
            log_paths: 日志文件路径列表
            time_range: 时间范围 (start_time, end_time)

        Returns:
            ToolResult: 分析结果
        """
        try:
            logger.info(f"开始分析日志文件: {log_paths}")

            analysis_result = {
                'summary': {},
                'errors': [],
                'performance_issues': [],
                'recommendations': [],
                'statistics': {},
                'timeline': []
            }

            total_lines = 0
            error_count = 0
            warning_count = 0

            for log_path in log_paths:
                if not os.path.exists(log_path):
                    logger.warning(f"日志文件不存在: {log_path}")
                    continue

                file_analysis = await self._analyze_single_file(log_path, time_range)

                # 合并分析结果
                total_lines += file_analysis['line_count']
                error_count += file_analysis['error_count']
                warning_count += file_analysis['warning_count']

                analysis_result['errors'].extend(file_analysis['errors'])
                analysis_result['performance_issues'].extend(file_analysis['performance_issues'])
                analysis_result['timeline'].extend(file_analysis['timeline'])

            # 生成统计信息
            analysis_result['statistics'] = {
                'total_lines': total_lines,
                'total_errors': error_count,
                'total_warnings': warning_count,
                'error_rate': (error_count / total_lines * 100) if total_lines > 0 else 0,
                'files_analyzed': len([p for p in log_paths if os.path.exists(p)])
            }

            # 生成摘要
            analysis_result['summary'] = await self._generate_summary(analysis_result)

            # 生成建议
            analysis_result['recommendations'] = await self._generate_recommendations(analysis_result)

            return ToolResult(
                success=True,
                data=analysis_result,
                message=f"成功分析了 {len(log_paths)} 个日志文件"
            )

        except Exception as e:
            logger.error(f"日志分析失败: {e}", exc_info=True)
            return ToolResult(
                success=False,
                error=str(e),
                message="日志分析失败"
            )

    async def _analyze_single_file(self, log_path: str, time_range: Optional[Tuple[datetime, datetime]]) -> Dict:
        """分析单个日志文件"""
        result = {
            'file_path': log_path,
            'line_count': 0,
            'error_count': 0,
            'warning_count': 0,
            'errors': [],
            'performance_issues': [],
            'timeline': []
        }

        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    result['line_count'] += 1

                    # 解析时间戳
                    timestamp = self._extract_timestamp(line)
                    if time_range and timestamp:
                        if not (time_range[0] <= timestamp <= time_range[1]):
                            continue

                    # 检查日志级别
                    level = self._extract_log_level(line)
                    if level:
                        if level.upper() in ['ERROR', 'CRITICAL', 'FATAL']:
                            result['error_count'] += 1
                        elif level.upper() in ['WARN', 'WARNING']:
                            result['warning_count'] += 1

                    # 错误模式匹配
                    error_info = self._match_error_patterns(line, line_num, log_path)
                    if error_info:
                        result['errors'].append(error_info)

                    # 性能问题检测
                    perf_issue = self._detect_performance_issues(line, line_num, log_path)
                    if perf_issue:
                        result['performance_issues'].append(perf_issue)

                    # 时间线事件
                    if timestamp and (level or error_info or perf_issue):
                        result['timeline'].append({
                            'timestamp': timestamp.isoformat(),
                            'level': level,
                            'line_number': line_num,
                            'content': line.strip()[:200],
                            'file': log_path
                        })

        except Exception as e:
            logger.error(f"分析文件 {log_path} 失败: {e}")

        return result

    def _extract_timestamp(self, line: str) -> Optional[datetime]:
        """提取时间戳"""
        match = self.log_patterns['timestamp'].search(line)
        if match:
            try:
                timestamp_str = match.group(1)
                # 尝试多种时间格式
                formats = [
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%dT%H:%M:%S.%fZ',
                    '%Y-%m-%dT%H:%M:%SZ',
                    '%Y-%m-%dT%H:%M:%S.%f',
                    '%Y-%m-%dT%H:%M:%S'
                ]

                for fmt in formats:
                    try:
                        return datetime.strptime(timestamp_str, fmt)
                    except ValueError:
                        continue
            except Exception:
                pass
        return None

    def _extract_log_level(self, line: str) -> Optional[str]:
        """提取日志级别"""
        match = self.log_patterns['level'].search(line)
        return match.group(1) if match else None

    def _match_error_patterns(self, line: str, line_num: int, file_path: str) -> Optional[Dict]:
        """匹配错误模式"""
        for error_type, config in self.error_patterns.items():
            for pattern in config['patterns']:
                if re.search(pattern, line, re.IGNORECASE):
                    return {
                        'type': error_type,
                        'severity': config['severity'],
                        'category': config['category'],
                        'line_number': line_num,
                        'file_path': file_path,
                        'content': line.strip(),
                        'solutions': config['solutions'],
                        'timestamp': self._extract_timestamp(line)
                    }
        return None

    def _detect_performance_issues(self, line: str, line_num: int, file_path: str) -> Optional[Dict]:
        """检测性能问题"""
        for issue_type, config in self.performance_patterns.items():
            match = re.search(config['pattern'], line, re.IGNORECASE)
            if match:
                value = float(match.group(1))
                if value > config['threshold']:
                    return {
                        'type': issue_type,
                        'category': config['category'],
                        'value': value,
                        'threshold': config['threshold'],
                        'line_number': line_num,
                        'file_path': file_path,
                        'content': line.strip(),
                        'timestamp': self._extract_timestamp(line)
                    }
        return None

    async def _generate_summary(self, analysis_result: Dict) -> Dict:
        """生成分析摘要"""
        stats = analysis_result['statistics']
        errors = analysis_result['errors']
        perf_issues = analysis_result['performance_issues']

        # 错误分类统计
        error_categories = Counter(error['category'] for error in errors)
        error_severities = Counter(error['severity'] for error in errors)

        # 性能问题统计
        perf_categories = Counter(issue['category'] for issue in perf_issues)

        return {
            'health_score': self._calculate_health_score(stats, errors, perf_issues),
            'top_error_categories': dict(error_categories.most_common(5)),
            'error_severities': dict(error_severities),
            'performance_categories': dict(perf_categories),
            'critical_issues': len([e for e in errors if e['severity'] == 'critical']),
            'recommendations_count': len(set(error['category'] for error in errors))
        }

    def _calculate_health_score(self, stats: Dict, errors: List, perf_issues: List) -> int:
        """计算系统健康评分 (0-100)"""
        base_score = 100

        # 错误率扣分
        error_rate = stats.get('error_rate', 0)
        base_score -= min(error_rate * 2, 50)  # 最多扣50分

        # 严重错误扣分
        critical_errors = len([e for e in errors if e['severity'] == 'critical'])
        base_score -= critical_errors * 10  # 每个严重错误扣10分

        # 性能问题扣分
        base_score -= len(perf_issues) * 5  # 每个性能问题扣5分

        return max(0, min(100, int(base_score)))

    async def _generate_recommendations(self, analysis_result: Dict) -> List[Dict]:
        """生成修复建议"""
        recommendations = []
        errors = analysis_result['errors']
        perf_issues = analysis_result['performance_issues']

        # 基于错误类型生成建议
        error_categories = defaultdict(list)
        for error in errors:
            error_categories[error['category']].append(error)

        for category, category_errors in error_categories.items():
            if category_errors:
                recommendations.append({
                    'type': 'error_fix',
                    'category': category,
                    'priority': 'high' if any(e['severity'] == 'critical' for e in category_errors) else 'medium',
                    'count': len(category_errors),
                    'solutions': category_errors[0]['solutions'],
                    'description': f"发现 {len(category_errors)} 个 {category} 相关错误"
                })

        # 基于性能问题生成建议
        perf_categories = defaultdict(list)
        for issue in perf_issues:
            perf_categories[issue['category']].append(issue)

        for category, category_issues in perf_categories.items():
            if category_issues:
                avg_value = sum(issue['value'] for issue in category_issues) / len(category_issues)
                recommendations.append({
                    'type': 'performance_optimization',
                    'category': category,
                    'priority': 'high' if avg_value > category_issues[0]['threshold'] * 1.5 else 'medium',
                    'count': len(category_issues),
                    'average_value': avg_value,
                    'description': f"发现 {len(category_issues)} 个 {category} 性能问题"
                })

        return recommendations

    async def get_log_insights(self, project_path: str, hours: int = 24) -> ToolResult:
        """
        获取项目日志洞察

        Args:
            project_path: 项目路径
            hours: 分析最近几小时的日志

        Returns:
            ToolResult: 日志洞察
        """
        try:
            # 查找日志文件
            log_files = self._find_log_files(project_path)

            if not log_files:
                return ToolResult(
                    success=False,
                    message="未找到日志文件"
                )

            # 设置时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            # 分析日志
            return await self.analyze_logs(log_files, (start_time, end_time))

        except Exception as e:
            logger.error(f"获取日志洞察失败: {e}", exc_info=True)
            return ToolResult(
                success=False,
                error=str(e),
                message="获取日志洞察失败"
            )

    def _find_log_files(self, project_path: str) -> List[str]:
        """查找项目中的日志文件"""
        log_files = []
        log_extensions = ['.log', '.txt']
        log_dirs = ['logs', 'log', 'var/log', 'tmp']

        # 在项目根目录查找
        for ext in log_extensions:
            log_files.extend(Path(project_path).glob(f'*{ext}'))

        # 在常见日志目录查找
        for log_dir in log_dirs:
            log_dir_path = Path(project_path) / log_dir
            if log_dir_path.exists():
                for ext in log_extensions:
                    log_files.extend(log_dir_path.glob(f'*{ext}'))
                    log_files.extend(log_dir_path.glob(f'**/*{ext}'))

        return [str(f) for f in log_files if f.is_file()]

    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        # 默认执行日志洞察
        if args:
            return await self.get_log_insights(args[0], **kwargs)
        else:
            return ToolResult(
                success=False,
                error="缺少项目路径参数"
            )
