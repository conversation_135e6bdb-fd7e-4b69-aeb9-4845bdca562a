# 本地GitLab实例配置指南

本文档介绍如何在本地GitLab实例（***************）上配置项目以使用Aider-Plus的Webhook功能。

## 前提条件

- 已在本地开发环境启动Aider-Plus服务
- 具有GitLab实例（***************）的访问权限
- 在GitLab上有一个测试项目

## 步骤1: 启动本地Aider-Plus服务

1. 打开命令行终端，进入项目根目录
2. 编辑`start-local-test.bat`文件，将`SERVICE_ADDRESS`设置为您的本机IP地址
3. 运行脚本启动服务：
   ```
   start-local-test.bat
   ```
4. 记下输出的Webhook URL和Token信息

## 步骤2: 在GitLab上配置Webhook

1. 登录GitLab实例（http://***************）
2. 进入您的测试项目
3. 点击左侧菜单的**设置 > Webhooks**
4. 填写以下信息：
   - **URL**：使用步骤1中记下的Webhook URL（例如：http://***************:8000/webhook/gitlab/）
   - **Secret Token**：使用步骤1中记下的Token（默认为：test-webhook-token）
   - **Trigger**：选择以下事件：
     - Issues events
     - Comments
     - Merge request events
     - Push events
     - Tag push events
     - Pipeline events
   - **SSL verification**：禁用（因为我们使用的是HTTP）
5. 点击**添加Webhook**按钮保存配置

## 步骤3: 测试Webhook

### 测试Issue事件

1. 在GitLab项目中创建一个新的Issue
2. 标题中包含"ai"或"bot"关键词，例如："测试AI功能"
3. 或者添加"ai"标签
4. 提交Issue
5. 查看Aider-Plus服务的日志，确认是否收到了事件并正确处理

### 测试评论事件

1. 在任意Issue下添加评论
2. 在评论中提及Bot，例如："@aider-bot 请帮我解决这个问题"
3. 提交评论
4. 查看Aider-Plus服务的日志，确认是否收到了事件并正确处理

### 测试MR事件

1. 创建一个新的分支并提交一些更改
2. 创建一个合并请求（MR）
3. 在MR标题或描述中包含"ai review"关键词
4. 或者添加"ai-review"标签
5. 提交MR
6. 查看Aider-Plus服务的日志，确认是否收到了事件并正确处理

### 测试Push事件

1. 向任意分支推送代码
2. 在提交信息中包含"ai review"关键词
3. 查看Aider-Plus服务的日志，确认是否收到了事件并正确处理

## 故障排除

### 无法连接到Webhook URL

- 确保您的GitLab实例（***************）可以访问您的本机IP
- 检查防火墙设置，确保允许端口8000的入站连接
- 尝试从GitLab服务器ping您的本机IP，确认网络连通性

### Webhook请求失败

- 检查Aider-Plus服务日志中的错误信息
- 确认Webhook URL和Token配置正确
- 在GitLab的Webhook页面查看请求历史和响应状态码

### 事件未被处理

- 确认事件包含了正确的触发关键词或标签
- 检查Aider-Plus服务的日志，查看事件是否被接收但未处理
- 确认TaskRouter组件正常工作
