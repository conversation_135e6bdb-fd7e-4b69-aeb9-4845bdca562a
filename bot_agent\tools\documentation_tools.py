"""
文档生成工具 - AI自动编码的文档生成能力
"""

import os
import ast
import re
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
import subprocess

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class DocumentationTools(BaseTool):
    """
    文档生成工具
    
    功能：
    1. API文档自动生成
    2. README文件生成
    3. 代码注释智能补全
    4. 项目架构文档
    5. 用户手册生成
    6. 变更日志生成
    7. 技术文档生成
    """
    
    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'generate_api_docs',
            'generate_readme',
            'add_code_comments',
            'generate_architecture_docs',
            'generate_user_manual',
            'generate_changelog',
            'analyze_project_structure',
            'generate_technical_docs'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "文档生成工具 - API文档、README、注释等自动生成"
    
    async def generate_api_docs(self, project_path: str, output_format: str = "markdown") -> ToolResult:
        """
        生成API文档
        
        Args:
            project_path: 项目路径
            output_format: 输出格式 (markdown, html, json)
            
        Returns:
            ToolResult: API文档生成结果
        """
        try:
            # 扫描项目中的API端点
            api_endpoints = self._scan_api_endpoints(project_path)
            
            if not api_endpoints:
                return ToolResult(
                    success=True,
                    data={'endpoints': [], 'documentation': ''},
                    message="未发现API端点"
                )
            
            # 生成API文档
            if output_format == "markdown":
                documentation = self._generate_markdown_api_docs(api_endpoints)
            elif output_format == "html":
                documentation = self._generate_html_api_docs(api_endpoints)
            elif output_format == "json":
                documentation = self._generate_json_api_docs(api_endpoints)
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的输出格式: {output_format}"
                )
            
            # 保存文档
            doc_filename = f"api_docs.{output_format}"
            doc_path = os.path.join(project_path, "docs", doc_filename)
            os.makedirs(os.path.dirname(doc_path), exist_ok=True)
            
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write(documentation)
            
            return ToolResult(
                success=True,
                data={
                    'endpoints': api_endpoints,
                    'documentation': documentation,
                    'output_file': doc_path,
                    'format': output_format
                },
                message=f"成功生成API文档: {doc_filename}"
            )
            
        except Exception as e:
            self.log_error(e, f"API文档生成: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def generate_readme(self, project_path: str, template: str = "standard") -> ToolResult:
        """
        生成README文件
        
        Args:
            project_path: 项目路径
            template: 模板类型 (standard, detailed, minimal)
            
        Returns:
            ToolResult: README生成结果
        """
        try:
            # 分析项目结构
            project_analysis = self._analyze_project_for_readme(project_path)
            
            # 根据模板生成README
            if template == "standard":
                readme_content = self._generate_standard_readme(project_analysis)
            elif template == "detailed":
                readme_content = self._generate_detailed_readme(project_analysis)
            elif template == "minimal":
                readme_content = self._generate_minimal_readme(project_analysis)
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的模板类型: {template}"
                )
            
            # 保存README
            readme_path = os.path.join(project_path, "README.md")
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            return ToolResult(
                success=True,
                data={
                    'project_analysis': project_analysis,
                    'readme_content': readme_content,
                    'output_file': readme_path,
                    'template': template
                },
                message=f"成功生成README文件: {template}模板"
            )
            
        except Exception as e:
            self.log_error(e, f"README生成: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def add_code_comments(self, file_path: str, comment_style: str = "docstring") -> ToolResult:
        """
        智能添加代码注释
        
        Args:
            file_path: 文件路径
            comment_style: 注释风格 (docstring, inline, both)
            
        Returns:
            ToolResult: 注释添加结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 分析需要添加注释的地方
            comment_analyzer = CommentAnalyzer(comment_style)
            comment_analyzer.visit(tree)
            
            # 生成带注释的代码
            lines = content.split('\n')
            commented_lines = self._add_comments_to_lines(
                lines, comment_analyzer.suggestions
            )
            
            # 备份原文件
            backup_path = file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 写入带注释的代码
            commented_content = '\n'.join(commented_lines)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(commented_content)
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'backup_path': backup_path,
                    'comment_style': comment_style,
                    'comments_added': len(comment_analyzer.suggestions),
                    'suggestions': comment_analyzer.suggestions
                },
                message=f"成功添加 {len(comment_analyzer.suggestions)} 条注释"
            )
            
        except Exception as e:
            self.log_error(e, f"代码注释: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def generate_architecture_docs(self, project_path: str) -> ToolResult:
        """
        生成项目架构文档
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 架构文档生成结果
        """
        try:
            # 分析项目架构
            architecture_analysis = self._analyze_project_architecture(project_path)
            
            # 生成架构文档
            architecture_doc = self._generate_architecture_markdown(architecture_analysis)
            
            # 保存文档
            doc_path = os.path.join(project_path, "docs", "ARCHITECTURE.md")
            os.makedirs(os.path.dirname(doc_path), exist_ok=True)
            
            with open(doc_path, 'w', encoding='utf-8') as f:
                f.write(architecture_doc)
            
            return ToolResult(
                success=True,
                data={
                    'architecture_analysis': architecture_analysis,
                    'documentation': architecture_doc,
                    'output_file': doc_path
                },
                message="成功生成架构文档"
            )
            
        except Exception as e:
            self.log_error(e, f"架构文档生成: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _scan_api_endpoints(self, project_path: str) -> List[Dict]:
        """扫描API端点"""
        endpoints = []
        
        # 查找Python文件中的API端点
        for py_file in Path(project_path).rglob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找Flask路由
                flask_routes = re.findall(r'@app\.route\([\'"]([^\'"]+)[\'"].*?\)\s*def\s+(\w+)', content, re.DOTALL)
                for route, func_name in flask_routes:
                    endpoints.append({
                        'path': route,
                        'method': 'GET',  # 简化处理
                        'function': func_name,
                        'file': str(py_file),
                        'framework': 'Flask'
                    })
                
                # 查找FastAPI路由
                fastapi_routes = re.findall(r'@app\.(get|post|put|delete)\([\'"]([^\'"]+)[\'"].*?\)\s*def\s+(\w+)', content, re.DOTALL)
                for method, route, func_name in fastapi_routes:
                    endpoints.append({
                        'path': route,
                        'method': method.upper(),
                        'function': func_name,
                        'file': str(py_file),
                        'framework': 'FastAPI'
                    })
                
            except Exception:
                continue
        
        return endpoints
    
    def _generate_markdown_api_docs(self, endpoints: List[Dict]) -> str:
        """生成Markdown格式的API文档"""
        doc_lines = [
            "# API 文档",
            "",
            "本文档描述了项目中的所有API端点。",
            "",
            "## 端点列表",
            ""
        ]
        
        for endpoint in endpoints:
            doc_lines.extend([
                f"### {endpoint['method']} {endpoint['path']}",
                "",
                f"**函数名**: `{endpoint['function']}`",
                f"**文件**: `{endpoint['file']}`",
                f"**框架**: {endpoint['framework']}",
                "",
                "**描述**: 待补充",
                "",
                "**参数**: 待补充",
                "",
                "**返回值**: 待补充",
                "",
                "---",
                ""
            ])
        
        return '\n'.join(doc_lines)
    
    def _analyze_project_for_readme(self, project_path: str) -> Dict:
        """分析项目用于生成README"""
        analysis = {
            'project_name': os.path.basename(project_path),
            'description': '项目描述待补充',
            'main_language': 'Python',
            'dependencies': [],
            'structure': {},
            'features': [],
            'installation_steps': [],
            'usage_examples': []
        }
        
        # 检查依赖文件
        requirements_file = os.path.join(project_path, 'requirements.txt')
        if os.path.exists(requirements_file):
            try:
                with open(requirements_file, 'r', encoding='utf-8') as f:
                    analysis['dependencies'] = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            except Exception:
                pass
        
        # 分析项目结构
        analysis['structure'] = self._get_project_structure(project_path)
        
        # 检测主要功能
        analysis['features'] = self._detect_project_features(project_path)
        
        return analysis
    
    def _generate_standard_readme(self, analysis: Dict) -> str:
        """生成标准README"""
        readme_lines = [
            f"# {analysis['project_name']}",
            "",
            f"{analysis['description']}",
            "",
            "## 功能特性",
            ""
        ]
        
        for feature in analysis['features']:
            readme_lines.append(f"- {feature}")
        
        readme_lines.extend([
            "",
            "## 安装说明",
            "",
            "1. 克隆项目",
            "```bash",
            f"git clone <repository-url>",
            f"cd {analysis['project_name']}",
            "```",
            "",
            "2. 安装依赖",
            "```bash",
            "pip install -r requirements.txt",
            "```",
            "",
            "## 使用方法",
            "",
            "```python",
            "# 使用示例待补充",
            "```",
            "",
            "## 项目结构",
            "",
            "```",
        ])
        
        # 添加项目结构
        for item in analysis['structure']:
            readme_lines.append(item)
        
        readme_lines.extend([
            "```",
            "",
            "## 贡献指南",
            "",
            "欢迎提交Issue和Pull Request！",
            "",
            "## 许可证",
            "",
            "MIT License"
        ])
        
        return '\n'.join(readme_lines)
    
    def _get_project_structure(self, project_path: str, max_depth: int = 3) -> List[str]:
        """获取项目结构"""
        structure = []
        
        def add_structure(path: Path, prefix: str = "", depth: int = 0):
            if depth > max_depth:
                return
            
            items = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name))
            for i, item in enumerate(items):
                if item.name.startswith('.'):
                    continue
                
                is_last = i == len(items) - 1
                current_prefix = "└── " if is_last else "├── "
                structure.append(f"{prefix}{current_prefix}{item.name}")
                
                if item.is_dir() and depth < max_depth:
                    next_prefix = prefix + ("    " if is_last else "│   ")
                    add_structure(item, next_prefix, depth + 1)
        
        try:
            add_structure(Path(project_path))
        except Exception:
            structure = ["项目结构分析失败"]
        
        return structure[:20]  # 限制行数
    
    def _detect_project_features(self, project_path: str) -> List[str]:
        """检测项目功能"""
        features = []
        
        # 检查常见的功能文件
        feature_indicators = {
            'requirements.txt': 'Python依赖管理',
            'Dockerfile': 'Docker容器化支持',
            'docker-compose.yml': 'Docker Compose编排',
            'pytest.ini': '自动化测试',
            'setup.py': 'Python包管理',
            '.github/workflows': 'GitHub Actions CI/CD',
            'README.md': '项目文档',
            'LICENSE': '开源许可证'
        }
        
        for indicator, feature in feature_indicators.items():
            if os.path.exists(os.path.join(project_path, indicator)):
                features.append(feature)
        
        # 检查代码中的功能
        for py_file in Path(project_path).rglob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'flask' in content.lower():
                    features.append('Flask Web框架')
                if 'fastapi' in content.lower():
                    features.append('FastAPI Web框架')
                if 'django' in content.lower():
                    features.append('Django Web框架')
                if 'sqlite' in content.lower() or 'mysql' in content.lower():
                    features.append('数据库集成')
                if 'redis' in content.lower():
                    features.append('Redis缓存')
                
            except Exception:
                continue
        
        return list(set(features))  # 去重
    
    def _analyze_project_architecture(self, project_path: str) -> Dict:
        """分析项目架构"""
        return {
            'modules': self._get_python_modules(project_path),
            'dependencies': self._analyze_dependencies(project_path),
            'patterns': self._detect_design_patterns(project_path),
            'layers': self._identify_architecture_layers(project_path)
        }
    
    def _get_python_modules(self, project_path: str) -> List[Dict]:
        """获取Python模块信息"""
        modules = []
        
        for py_file in Path(project_path).rglob('*.py'):
            if py_file.name == '__init__.py':
                continue
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
                functions = [node.name for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
                
                modules.append({
                    'name': py_file.stem,
                    'path': str(py_file.relative_to(project_path)),
                    'classes': classes,
                    'functions': functions,
                    'lines': len(content.split('\n'))
                })
                
            except Exception:
                continue
        
        return modules
    
    def _generate_architecture_markdown(self, analysis: Dict) -> str:
        """生成架构文档Markdown"""
        doc_lines = [
            "# 项目架构文档",
            "",
            "## 概述",
            "",
            "本文档描述了项目的整体架构和设计模式。",
            "",
            "## 模块结构",
            ""
        ]
        
        for module in analysis['modules']:
            doc_lines.extend([
                f"### {module['name']}",
                "",
                f"**文件路径**: `{module['path']}`",
                f"**代码行数**: {module['lines']}",
                "",
                f"**类**: {', '.join(module['classes']) if module['classes'] else '无'}",
                f"**函数**: {', '.join(module['functions']) if module['functions'] else '无'}",
                "",
            ])
        
        return '\n'.join(doc_lines)
    
    def _add_comments_to_lines(self, lines: List[str], suggestions: List[Dict]) -> List[str]:
        """在代码行中添加注释"""
        commented_lines = lines.copy()
        
        # 按行号倒序处理，避免行号偏移
        for suggestion in sorted(suggestions, key=lambda x: x['line'], reverse=True):
            line_no = suggestion['line'] - 1
            if 0 <= line_no < len(commented_lines):
                if suggestion['type'] == 'docstring':
                    # 在函数定义后添加docstring
                    indent = len(commented_lines[line_no]) - len(commented_lines[line_no].lstrip())
                    docstring = f'{" " * (indent + 4)}"""{suggestion["comment"]}"""'
                    commented_lines.insert(line_no + 1, docstring)
                elif suggestion['type'] == 'inline':
                    # 在行末添加注释
                    commented_lines[line_no] += f"  # {suggestion['comment']}"
        
        return commented_lines
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            project_path = args[0]
            return await self.generate_readme(project_path)
        else:
            return ToolResult(
                success=False,
                error="缺少项目路径参数"
            )


class CommentAnalyzer(ast.NodeVisitor):
    """注释分析器"""
    
    def __init__(self, comment_style: str):
        self.comment_style = comment_style
        self.suggestions = []
    
    def visit_FunctionDef(self, node):
        # 检查是否缺少docstring
        if not ast.get_docstring(node):
            if self.comment_style in ['docstring', 'both']:
                self.suggestions.append({
                    'line': node.lineno,
                    'type': 'docstring',
                    'comment': f'{node.name}函数的功能描述',
                    'target': node.name
                })
        
        self.generic_visit(node)
    
    def visit_ClassDef(self, node):
        # 检查类是否缺少docstring
        if not ast.get_docstring(node):
            if self.comment_style in ['docstring', 'both']:
                self.suggestions.append({
                    'line': node.lineno,
                    'type': 'docstring',
                    'comment': f'{node.name}类的功能描述',
                    'target': node.name
                })
        
        self.generic_visit(node)
