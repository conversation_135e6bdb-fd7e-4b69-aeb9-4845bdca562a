"""
部署任务执行器 - 专门处理Pipeline相关的部署任务
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from bot_agent.clients.gitlab_client import GitLabClient
from bot_agent.handlers.ai_response_handler import AIResponseHandler
from .pipeline_analyzer import PipelineAnalyzer

logger = logging.getLogger(__name__)


class DeploymentTaskExecutor:
    """
    部署任务执行器

    专门处理Pipeline相关的自动化任务：
    1. 部署失败分析
    2. 部署成功验证
    3. 实时部署监控
    4. 自动修复尝试
    """

    def __init__(self):
        """初始化部署任务执行器"""
        self.gitlab_client = GitLabClient()
        self.pipeline_analyzer = PipelineAnalyzer(self.gitlab_client)
        self.response_handler = AIResponseHandler()

        logger.info("DeploymentTaskExecutor initialized")

    async def execute_deployment_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行部署相关任务

        Args:
            task: 任务信息

        Returns:
            Dict: 执行结果
        """
        task_id = task.get("id", "unknown")
        monitoring_reason = task.get("metadata", {}).get("monitoring_reason", "unknown")

        logger.info(f"执行部署任务 {task_id}, 原因: {monitoring_reason}")

        try:
            if monitoring_reason == "deployment_failure":
                return await self._handle_deployment_failure(task)
            elif monitoring_reason == "deployment_success_verification":
                return await self._handle_deployment_success_verification(task)
            elif monitoring_reason == "deployment_monitoring":
                return await self._handle_deployment_monitoring(task)
            elif monitoring_reason in ["critical_job_failure", "main_branch_job_failure", "deployment_job_failure"]:
                return await self._handle_job_failure(task)
            elif monitoring_reason == "deployment_job_success":
                return await self._handle_job_success(task)
            else:
                return await self._handle_general_pipeline_analysis(task)

        except Exception as e:
            logger.error(f"执行部署任务 {task_id} 失败: {e}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "report": f"部署任务执行失败: {str(e)}"
            }

    async def _handle_deployment_failure(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理部署失败"""
        metadata = task.get("metadata", {})
        project_id = metadata.get("project_id")
        pipeline_id = metadata.get("pipeline_id")

        logger.info(f"分析部署失败: Pipeline {pipeline_id}")

        # 分析Pipeline
        analysis = await self.pipeline_analyzer.analyze_pipeline(project_id, pipeline_id)

        # 生成详细报告
        report = self.pipeline_analyzer.generate_analysis_report(analysis)

        # 尝试自动修复（如果可能）
        auto_fix_attempts = await self._attempt_auto_fix(analysis)

        if auto_fix_attempts:
            report += "\n\n### 🔧 自动修复尝试\n"
            for attempt in auto_fix_attempts:
                report += f"- {attempt}\n"

        # 发送分析结果到GitLab
        await self._send_analysis_to_gitlab(task, report, "failure")

        return {
            "status": "success",
            "report": report,
            "analysis": analysis,
            "auto_fix_attempts": auto_fix_attempts
        }

    async def _handle_deployment_success_verification(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理部署成功验证"""
        metadata = task.get("metadata", {})
        project_id = metadata.get("project_id")
        pipeline_id = metadata.get("pipeline_id")

        logger.info(f"验证部署成功: Pipeline {pipeline_id}")

        # 分析Pipeline和健康检查
        analysis = await self.pipeline_analyzer.analyze_pipeline(project_id, pipeline_id)

        # 生成验证报告
        report = f"""
## ✅ 部署成功验证报告

**Pipeline ID**: {pipeline_id}
**项目ID**: {project_id}
**分支**: {analysis.ref}
**验证时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### 🚀 部署状态
- **Pipeline状态**: {analysis.status}
- **部署作业**: {len(analysis.deployment_jobs)} 个
- **所有作业**: 成功完成

### 🏥 健康检查
"""

        if analysis.health_check_results:
            health = analysis.health_check_results
            report += f"**整体状态**: {health['overall_status']}\n\n"

            for check in health['checks']:
                status_emoji = {
                    'success': '✅',
                    'warning': '⚠️',
                    'failed': '❌',
                    'info': 'ℹ️'
                }.get(check['status'], '❓')
                report += f"- {status_emoji} {check['message']}\n"
        else:
            report += "- ℹ️ 未执行详细健康检查\n"

        # 添加后续监控建议
        report += """
### 📊 后续监控建议
1. 🔍 **持续监控**: 关注应用性能指标和错误率
2. 📈 **用户反馈**: 收集用户使用反馈
3. 🚨 **告警设置**: 确保监控告警正常工作
4. 📝 **文档更新**: 更新部署文档和变更记录

### ✅ 验证结论
部署已成功完成，建议继续监控应用运行状态。
"""

        # 发送验证结果到GitLab
        await self._send_analysis_to_gitlab(task, report, "success")

        return {
            "status": "success",
            "report": report,
            "analysis": analysis
        }

    async def _handle_deployment_monitoring(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理实时部署监控"""
        metadata = task.get("metadata", {})
        project_id = metadata.get("project_id")
        pipeline_id = metadata.get("pipeline_id")

        logger.info(f"监控部署进度: Pipeline {pipeline_id}")

        # 持续监控Pipeline状态
        monitoring_report = await self._monitor_pipeline_progress(project_id, pipeline_id)

        # 发送监控结果到GitLab
        await self._send_analysis_to_gitlab(task, monitoring_report, "monitoring")

        return {
            "status": "success",
            "report": monitoring_report
        }

    async def _handle_general_pipeline_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理通用Pipeline分析"""
        metadata = task.get("metadata", {})
        project_id = metadata.get("project_id")
        pipeline_id = metadata.get("pipeline_id")

        logger.info(f"通用Pipeline分析: Pipeline {pipeline_id}")

        # 分析Pipeline
        analysis = await self.pipeline_analyzer.analyze_pipeline(project_id, pipeline_id)

        # 生成报告
        report = self.pipeline_analyzer.generate_analysis_report(analysis)

        # 发送分析结果到GitLab
        await self._send_analysis_to_gitlab(task, report, "analysis")

        return {
            "status": "success",
            "report": report,
            "analysis": analysis
        }

    async def _attempt_auto_fix(self, analysis) -> List[str]:
        """尝试自动修复"""
        auto_fix_attempts = []

        # 基于错误类型尝试自动修复
        error_summary = analysis.error_summary.lower()

        # 依赖问题
        if "no such file" in error_summary or "command not found" in error_summary:
            auto_fix_attempts.append("🔧 检测到文件或命令缺失，建议检查依赖安装和路径配置")

        # 权限问题
        if "permission denied" in error_summary:
            auto_fix_attempts.append("🔐 检测到权限问题，建议检查文件权限和用户配置")

        # Docker问题
        if "docker" in error_summary:
            auto_fix_attempts.append("🐳 检测到Docker相关问题，建议检查Docker配置和镜像")

        # 网络问题
        if "timeout" in error_summary or "connection" in error_summary:
            auto_fix_attempts.append("🌐 检测到网络问题，建议检查网络连接和代理设置")

        # 测试失败
        if "test" in error_summary and "fail" in error_summary:
            auto_fix_attempts.append("🧪 检测到测试失败，建议检查测试用例和测试数据")

        return auto_fix_attempts

    async def _monitor_pipeline_progress(self, project_id: int, pipeline_id: int) -> str:
        """监控Pipeline进度"""
        report = f"""
## 🔄 Pipeline实时监控报告

**Pipeline ID**: {pipeline_id}
**监控开始时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### 📊 监控结果
"""

        try:
            # 获取当前Pipeline状态
            pipeline = self.gitlab_client.get_pipeline(project_id, pipeline_id)
            if pipeline:
                status = pipeline.get('status', 'unknown')
                report += f"- **当前状态**: {status}\n"

                # 获取作业状态
                jobs = self.gitlab_client.get_pipeline_jobs(project_id, pipeline_id)
                if jobs:
                    running_jobs = [job for job in jobs if job.get('status') == 'running']
                    pending_jobs = [job for job in jobs if job.get('status') == 'pending']

                    report += f"- **运行中作业**: {len(running_jobs)}\n"
                    report += f"- **等待中作业**: {len(pending_jobs)}\n"

                    if running_jobs:
                        report += "\n**运行中的作业**:\n"
                        for job in running_jobs:
                            report += f"  - {job.get('name', 'unknown')} (阶段: {job.get('stage', 'unknown')})\n"

                if status in ['success', 'failed', 'canceled']:
                    report += f"\n### ✅ 监控完成\nPipeline已完成，最终状态: {status}"
                else:
                    report += f"\n### ⏳ 继续监控\nPipeline仍在运行中，将持续监控..."
            else:
                report += "- ❌ 无法获取Pipeline信息\n"

        except Exception as e:
            report += f"- ❌ 监控过程中出现错误: {str(e)}\n"

        return report

    async def _send_analysis_to_gitlab(self, task: Dict[str, Any], report: str,
                                     analysis_type: str) -> None:
        """发送分析结果到GitLab"""
        try:
            metadata = task.get("metadata", {})
            project_id = metadata.get("project_id")
            pipeline_id = metadata.get("pipeline_id")

            # 根据分析类型添加不同的标题前缀
            title_prefixes = {
                "failure": "🚨 部署失败分析",
                "success": "✅ 部署成功验证",
                "monitoring": "🔄 部署监控报告",
                "analysis": "🔍 Pipeline分析"
            }

            title_prefix = title_prefixes.get(analysis_type, "📋 Pipeline报告")

            # 格式化最终报告
            final_report = f"""
{title_prefix} - Pipeline {pipeline_id}

{report}

---
*此报告由aider-plus自动生成*
"""

            # 发送到GitLab（通过AI响应处理器）
            await self.response_handler.process_and_send_response(
                ai_response=final_report,
                task=task
            )

            logger.info(f"分析结果已发送到GitLab: {analysis_type}")

        except Exception as e:
            logger.error(f"发送分析结果到GitLab失败: {e}", exc_info=True)

    async def _handle_job_failure(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理作业失败"""
        metadata = task.get("metadata", {})
        project_id = metadata.get("project_id")
        build_id = metadata.get("build_id")
        build_name = metadata.get("build_name")
        build_stage = metadata.get("build_stage")
        build_failure_reason = metadata.get("build_failure_reason")
        pipeline_id = metadata.get("pipeline_id")

        logger.info(f"分析作业失败: Job {build_name} ({build_id})")

        # 获取作业日志
        job_logs = ""
        try:
            job_logs = self.gitlab_client.get_job_log(project_id, build_id)
        except Exception as e:
            logger.error(f"获取作业日志失败: {e}")
            job_logs = f"无法获取日志: {str(e)}"

        # 分析失败原因
        error_analysis = self._analyze_job_failure(job_logs, build_failure_reason)

        # 生成修复建议
        fix_suggestions = self._generate_job_fix_suggestions(
            build_name, build_stage, build_failure_reason, job_logs
        )

        # 生成详细报告
        report = f"""
## 🚨 作业失败分析报告

**作业名称**: {build_name}
**作业ID**: {build_id}
**Pipeline ID**: {pipeline_id}
**阶段**: {build_stage}
**失败原因**: {build_failure_reason or '未知'}
**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### 🔍 错误分析
{error_analysis}

### 💡 修复建议
"""

        for i, suggestion in enumerate(fix_suggestions, 1):
            report += f"{i}. {suggestion}\n"

        # 添加日志摘要
        if job_logs:
            log_preview = job_logs[-1000:] if len(job_logs) > 1000 else job_logs
            report += f"""
### 📋 作业日志摘要
```
{log_preview}
```
"""

        report += f"""
### 🔗 相关链接
- [作业详情](job_url_placeholder)
- [Pipeline详情](pipeline_url_placeholder)

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 发送分析结果到GitLab
        await self._send_analysis_to_gitlab(task, report, "job_failure")

        return {
            "status": "success",
            "report": report,
            "error_analysis": error_analysis,
            "fix_suggestions": fix_suggestions
        }

    async def _handle_job_success(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理作业成功"""
        metadata = task.get("metadata", {})
        project_id = metadata.get("project_id")
        build_id = metadata.get("build_id")
        build_name = metadata.get("build_name")
        build_stage = metadata.get("build_stage")
        pipeline_id = metadata.get("pipeline_id")

        logger.info(f"验证作业成功: Job {build_name} ({build_id})")

        # 获取作业日志
        job_logs = ""
        try:
            job_logs = self.gitlab_client.get_job_log(project_id, build_id)
        except Exception as e:
            logger.error(f"获取作业日志失败: {e}")
            job_logs = f"无法获取日志: {str(e)}"

        # 分析成功结果
        success_analysis = self._analyze_job_success(job_logs, build_name, build_stage)

        # 生成验证报告
        report = f"""
## ✅ 作业成功验证报告

**作业名称**: {build_name}
**作业ID**: {build_id}
**Pipeline ID**: {pipeline_id}
**阶段**: {build_stage}
**验证时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### 📊 执行结果
{success_analysis}

### 🔍 后续建议
"""

        # 根据作业类型提供后续建议
        if "deploy" in build_name.lower():
            report += """
1. 🏥 **健康检查**: 验证部署的服务是否正常运行
2. 📊 **监控**: 关注应用性能指标和错误率
3. 🔍 **功能验证**: 测试关键功能是否正常
4. 📝 **文档更新**: 更新部署记录和变更日志
"""
        elif "test" in build_stage.lower():
            report += """
1. 📋 **测试覆盖率**: 检查测试覆盖率是否达标
2. 🔍 **测试结果**: 确认所有测试用例都通过
3. 📊 **质量指标**: 检查代码质量指标
4. 🚀 **准备部署**: 可以考虑进入下一阶段
"""
        elif "build" in build_stage.lower():
            report += """
1. 📦 **构建产物**: 验证构建产物的完整性
2. 🔍 **依赖检查**: 确认所有依赖都正确安装
3. 📊 **构建时间**: 关注构建性能
4. 🚀 **准备测试**: 可以进入测试阶段
"""
        else:
            report += """
1. ✅ **作业完成**: 作业已成功完成
2. 📊 **监控**: 继续关注后续作业
3. 🔍 **验证**: 确认输出符合预期
4. 🚀 **下一步**: 准备执行后续步骤
"""

        # 添加日志摘要（如果有重要信息）
        if job_logs and len(job_logs) > 100:
            log_preview = job_logs[-500:] if len(job_logs) > 500 else job_logs
            report += f"""
### 📋 作业日志摘要
```
{log_preview}
```
"""

        report += f"""
### 🔗 相关链接
- [作业详情](job_url_placeholder)
- [Pipeline详情](pipeline_url_placeholder)

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 发送验证结果到GitLab
        await self._send_analysis_to_gitlab(task, report, "job_success")

        return {
            "status": "success",
            "report": report,
            "success_analysis": success_analysis
        }

    def _analyze_job_failure(self, logs: str, failure_reason: str) -> str:
        """分析作业失败原因"""
        if not logs:
            return f"作业失败，原因: {failure_reason or '未知'}，但无法获取详细日志。"

        # 提取关键错误信息
        error_patterns = [
            (r'ERROR:\s*(.+)', 'ERROR'),
            (r'Error:\s*(.+)', 'Error'),
            (r'FAILED:\s*(.+)', 'FAILED'),
            (r'Exception:\s*(.+)', 'Exception'),
            (r'Fatal:\s*(.+)', 'Fatal'),
            (r'npm ERR!\s*(.+)', 'NPM Error'),
            (r'docker:\s*(.+)', 'Docker Error'),
            (r'permission denied:\s*(.+)', 'Permission Error'),
        ]

        found_errors = []
        import re

        for pattern, error_type in error_patterns:
            matches = re.findall(pattern, logs, re.IGNORECASE | re.MULTILINE)
            for match in matches[-3:]:  # 最多取最后3个匹配
                found_errors.append(f"- **{error_type}**: {match.strip()}")

        if found_errors:
            return f"检测到以下关键错误:\n" + '\n'.join(found_errors)
        else:
            # 如果没有找到特定错误，返回最后几行
            lines = logs.strip().split('\n')
            last_lines = lines[-5:] if len(lines) > 5 else lines
            return f"作业失败，最后几行日志:\n```\n" + '\n'.join(last_lines) + "\n```"

    def _analyze_job_success(self, logs: str, job_name: str, job_stage: str) -> str:
        """分析作业成功结果"""
        analysis = f"作业 {job_name} 在 {job_stage} 阶段成功完成。"

        if logs:
            # 查找成功指标
            success_indicators = [
                "successfully", "completed", "passed", "success", "done",
                "成功", "完成", "通过"
            ]

            success_count = sum(1 for indicator in success_indicators
                              if indicator in logs.lower())

            if success_count > 0:
                analysis += f" 日志中发现 {success_count} 个成功指标。"

            # 检查特定的成功模式
            if "deploy" in job_name.lower():
                if "deployed" in logs.lower() or "deployment" in logs.lower():
                    analysis += " 部署操作已完成。"
            elif "test" in job_stage.lower():
                if "passed" in logs.lower() or "ok" in logs.lower():
                    analysis += " 测试执行通过。"
            elif "build" in job_stage.lower():
                if "built" in logs.lower() or "compiled" in logs.lower():
                    analysis += " 构建操作成功。"

        return analysis

    def _generate_job_fix_suggestions(self, job_name: str, job_stage: str,
                                    failure_reason: str, logs: str) -> List[str]:
        """生成作业修复建议"""
        suggestions = []

        # 基于失败原因的建议
        if failure_reason == "script_failure":
            suggestions.append("🔍 **脚本失败**: 检查作业脚本的语法和逻辑错误")
        elif failure_reason == "runner_system_failure":
            suggestions.append("🖥️ **运行器故障**: 检查GitLab Runner的状态和资源")
        elif failure_reason == "timeout":
            suggestions.append("⏰ **超时**: 增加作业超时时间或优化执行效率")

        # 基于日志内容的建议
        if logs:
            logs_lower = logs.lower()

            if "permission denied" in logs_lower:
                suggestions.append("🔐 **权限问题**: 检查文件权限和用户配置")

            if "no such file" in logs_lower or "not found" in logs_lower:
                suggestions.append("📁 **文件缺失**: 检查文件路径和依赖是否正确")

            if "docker" in logs_lower and "error" in logs_lower:
                suggestions.append("🐳 **Docker问题**: 检查Docker配置和镜像")

            if "npm err" in logs_lower or "yarn error" in logs_lower:
                suggestions.append("📦 **包管理问题**: 检查Node.js依赖和包管理器配置")

            if "pip" in logs_lower and "error" in logs_lower:
                suggestions.append("🐍 **Python依赖问题**: 检查requirements.txt和Python环境")

        # 基于作业类型的建议
        if "lint" in job_name.lower():
            suggestions.append("📝 **代码规范**: 运行本地lint检查并修复代码规范问题")
        elif "test" in job_name.lower():
            suggestions.append("🧪 **测试失败**: 在本地运行测试，修复失败的测试用例")
        elif "build" in job_name.lower():
            suggestions.append("🔨 **构建失败**: 检查构建配置和依赖版本")
        elif "deploy" in job_name.lower():
            suggestions.append("🚀 **部署失败**: 检查部署配置和目标环境状态")

        # 通用建议
        suggestions.extend([
            "🔄 **重试**: 如果是临时问题，可以重新运行作业",
            "📋 **日志**: 查看完整的作业日志获取更多详细信息",
            "👥 **协作**: 如需帮助，请联系相关开发团队"
        ])

        return suggestions
