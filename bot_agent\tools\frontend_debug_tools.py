"""
前端专用调试工具 - 专门针对前端开发的调试功能
"""

import os
import json
import asyncio
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
import subprocess

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class FrontendDebugTools(BaseTool):
    """
    前端专用调试工具
    
    功能：
    1. JavaScript语法检查
    2. CSS验证和优化建议
    3. HTML结构分析
    4. 前端性能分析
    5. 浏览器兼容性检查
    6. 前端框架特定调试
    """
    
    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'check_javascript_syntax',
            'validate_css',
            'analyze_html_structure',
            'check_browser_compatibility',
            'analyze_bundle_size',
            'detect_unused_code',
            'check_accessibility',
            'validate_responsive_design'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "前端专用调试工具 - JavaScript、CSS、HTML分析和优化"
    
    async def check_javascript_syntax(self, file_path: str) -> ToolResult:
        """
        检查JavaScript语法
        
        Args:
            file_path: JavaScript文件路径
            
        Returns:
            ToolResult: 语法检查结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            # 使用Node.js检查语法
            syntax_result = await self._check_js_with_node(file_path)
            
            if syntax_result:
                return ToolResult(
                    success=True,
                    data=syntax_result,
                    message="JavaScript语法检查完成"
                )
            
            # 如果Node.js不可用，使用Python进行基本检查
            basic_check = await self._basic_js_check(file_path)
            
            return ToolResult(
                success=True,
                data=basic_check,
                message="基本JavaScript检查完成"
            )
            
        except Exception as e:
            self.log_error(e, f"JavaScript语法检查: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def validate_css(self, file_path: str) -> ToolResult:
        """
        CSS验证和分析
        
        Args:
            file_path: CSS文件路径
            
        Returns:
            ToolResult: CSS验证结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            validation_result = {
                'file_path': file_path,
                'issues': [],
                'warnings': [],
                'suggestions': [],
                'stats': {}
            }
            
            # 基本CSS语法检查
            syntax_issues = self._check_css_syntax(css_content)
            validation_result['issues'].extend(syntax_issues)
            
            # CSS最佳实践检查
            best_practices = self._check_css_best_practices(css_content)
            validation_result['warnings'].extend(best_practices)
            
            # CSS统计信息
            stats = self._analyze_css_stats(css_content)
            validation_result['stats'] = stats
            
            # 生成优化建议
            suggestions = self._generate_css_suggestions(validation_result)
            validation_result['suggestions'] = suggestions
            
            total_issues = len(validation_result['issues']) + len(validation_result['warnings'])
            
            return ToolResult(
                success=True,
                data=validation_result,
                message=f"CSS验证完成，发现 {total_issues} 个问题"
            )
            
        except Exception as e:
            self.log_error(e, f"CSS验证: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def analyze_html_structure(self, file_path: str) -> ToolResult:
        """
        HTML结构分析
        
        Args:
            file_path: HTML文件路径
            
        Returns:
            ToolResult: HTML结构分析结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            analysis_result = {
                'file_path': file_path,
                'structure': {},
                'seo_analysis': {},
                'accessibility': {},
                'performance': {},
                'suggestions': []
            }
            
            # HTML结构分析
            structure = self._analyze_html_structure(html_content)
            analysis_result['structure'] = structure
            
            # SEO分析
            seo_analysis = self._analyze_seo(html_content)
            analysis_result['seo_analysis'] = seo_analysis
            
            # 可访问性分析
            accessibility = self._analyze_accessibility(html_content)
            analysis_result['accessibility'] = accessibility
            
            # 性能分析
            performance = self._analyze_html_performance(html_content)
            analysis_result['performance'] = performance
            
            # 生成建议
            suggestions = self._generate_html_suggestions(analysis_result)
            analysis_result['suggestions'] = suggestions
            
            return ToolResult(
                success=True,
                data=analysis_result,
                message="HTML结构分析完成"
            )
            
        except Exception as e:
            self.log_error(e, f"HTML结构分析: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def analyze_bundle_size(self, project_path: str) -> ToolResult:
        """
        分析前端打包大小
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 打包大小分析结果
        """
        try:
            bundle_analysis = {
                'project_path': project_path,
                'javascript_files': [],
                'css_files': [],
                'image_files': [],
                'total_size': 0,
                'recommendations': []
            }
            
            # 分析JavaScript文件
            js_files = list(Path(project_path).rglob('*.js'))
            for js_file in js_files:
                if js_file.is_file():
                    size = js_file.stat().st_size
                    bundle_analysis['javascript_files'].append({
                        'path': str(js_file),
                        'size_kb': size / 1024,
                        'relative_path': str(js_file.relative_to(project_path))
                    })
                    bundle_analysis['total_size'] += size
            
            # 分析CSS文件
            css_files = list(Path(project_path).rglob('*.css'))
            for css_file in css_files:
                if css_file.is_file():
                    size = css_file.stat().st_size
                    bundle_analysis['css_files'].append({
                        'path': str(css_file),
                        'size_kb': size / 1024,
                        'relative_path': str(css_file.relative_to(project_path))
                    })
                    bundle_analysis['total_size'] += size
            
            # 分析图片文件
            image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp']
            for ext in image_extensions:
                image_files = list(Path(project_path).rglob(f'*{ext}'))
                for img_file in image_files:
                    if img_file.is_file():
                        size = img_file.stat().st_size
                        bundle_analysis['image_files'].append({
                            'path': str(img_file),
                            'size_kb': size / 1024,
                            'relative_path': str(img_file.relative_to(project_path))
                        })
                        bundle_analysis['total_size'] += size
            
            # 生成优化建议
            recommendations = self._generate_bundle_recommendations(bundle_analysis)
            bundle_analysis['recommendations'] = recommendations
            
            # 转换总大小为更友好的格式
            total_size_mb = bundle_analysis['total_size'] / (1024 * 1024)
            bundle_analysis['total_size_mb'] = total_size_mb
            
            return ToolResult(
                success=True,
                data=bundle_analysis,
                message=f"打包分析完成，总大小: {total_size_mb:.2f}MB"
            )
            
        except Exception as e:
            self.log_error(e, f"打包大小分析: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def detect_unused_code(self, project_path: str) -> ToolResult:
        """
        检测未使用的代码
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 未使用代码检测结果
        """
        try:
            unused_analysis = {
                'project_path': project_path,
                'unused_css': [],
                'unused_js_functions': [],
                'unused_imports': [],
                'recommendations': []
            }
            
            # 检测未使用的CSS
            css_files = list(Path(project_path).rglob('*.css'))
            html_files = list(Path(project_path).rglob('*.html'))
            
            for css_file in css_files:
                unused_css = self._find_unused_css(css_file, html_files)
                unused_analysis['unused_css'].extend(unused_css)
            
            # 检测未使用的JavaScript函数
            js_files = list(Path(project_path).rglob('*.js'))
            for js_file in js_files:
                unused_functions = self._find_unused_js_functions(js_file, js_files)
                unused_analysis['unused_js_functions'].extend(unused_functions)
            
            # 生成清理建议
            recommendations = self._generate_cleanup_recommendations(unused_analysis)
            unused_analysis['recommendations'] = recommendations
            
            total_unused = (len(unused_analysis['unused_css']) + 
                          len(unused_analysis['unused_js_functions']) + 
                          len(unused_analysis['unused_imports']))
            
            return ToolResult(
                success=True,
                data=unused_analysis,
                message=f"未使用代码检测完成，发现 {total_unused} 个可清理项"
            )
            
        except Exception as e:
            self.log_error(e, f"未使用代码检测: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def _check_js_with_node(self, file_path: str) -> Optional[Dict]:
        """使用Node.js检查JavaScript语法"""
        try:
            # 检查Node.js是否可用
            node_check = await self.terminal.execute_command("node --version")
            if not node_check.success:
                return None
            
            # 创建临时检查脚本
            check_script = f"""
            try {{
                require('{file_path}');
                console.log(JSON.stringify({{success: true, message: 'Syntax OK'}}));
            }} catch (error) {{
                console.log(JSON.stringify({{
                    success: false, 
                    error: error.message,
                    line: error.lineNumber || 'unknown',
                    column: error.columnNumber || 'unknown'
                }}));
            }}
            """
            
            # 执行检查
            result = await self.terminal.execute_command(f"node -e \"{check_script}\"")
            
            if result.success:
                try:
                    return json.loads(result.data['stdout'])
                except json.JSONDecodeError:
                    return None
            
            return None
            
        except Exception:
            return None
    
    async def _basic_js_check(self, file_path: str) -> Dict:
        """基本JavaScript检查"""
        with open(file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        issues = []
        
        # 检查基本语法问题
        if js_content.count('(') != js_content.count(')'):
            issues.append({'type': 'syntax', 'message': '括号不匹配'})
        
        if js_content.count('{') != js_content.count('}'):
            issues.append({'type': 'syntax', 'message': '大括号不匹配'})
        
        if js_content.count('[') != js_content.count(']'):
            issues.append({'type': 'syntax', 'message': '方括号不匹配'})
        
        # 检查常见错误模式
        if re.search(r'console\.log\(.*\);?', js_content):
            issues.append({'type': 'warning', 'message': '包含console.log语句，建议在生产环境中移除'})
        
        return {
            'file_path': file_path,
            'issues': issues,
            'method': 'basic_check'
        }
    
    def _check_css_syntax(self, css_content: str) -> List[Dict]:
        """检查CSS语法"""
        issues = []
        
        # 检查大括号匹配
        if css_content.count('{') != css_content.count('}'):
            issues.append({
                'type': 'syntax_error',
                'message': 'CSS大括号不匹配',
                'severity': 'high'
            })
        
        # 检查分号
        lines = css_content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line and ':' in line and not line.endswith((';', '{', '}')):
                if not line.startswith(('@', '/*', '//')):
                    issues.append({
                        'type': 'syntax_warning',
                        'message': f'第{i}行可能缺少分号',
                        'line': i,
                        'severity': 'medium'
                    })
        
        return issues
    
    def _check_css_best_practices(self, css_content: str) -> List[Dict]:
        """检查CSS最佳实践"""
        warnings = []
        
        # 检查!important的使用
        important_count = len(re.findall(r'!important', css_content, re.IGNORECASE))
        if important_count > 5:
            warnings.append({
                'type': 'best_practice',
                'message': f'过度使用!important ({important_count}次)，建议重构CSS优先级',
                'severity': 'medium'
            })
        
        # 检查内联样式
        if 'style=' in css_content:
            warnings.append({
                'type': 'best_practice',
                'message': '发现内联样式，建议使用外部CSS',
                'severity': 'low'
            })
        
        return warnings
    
    def _analyze_css_stats(self, css_content: str) -> Dict:
        """分析CSS统计信息"""
        return {
            'total_lines': len(css_content.split('\n')),
            'total_chars': len(css_content),
            'selectors_count': len(re.findall(r'[^{}]+\s*{', css_content)),
            'properties_count': len(re.findall(r'[^:]+:\s*[^;]+;', css_content)),
            'comments_count': len(re.findall(r'/\*.*?\*/', css_content, re.DOTALL))
        }
    
    def _generate_css_suggestions(self, validation_result: Dict) -> List[str]:
        """生成CSS优化建议"""
        suggestions = []
        
        stats = validation_result['stats']
        
        if stats['total_lines'] > 1000:
            suggestions.append('CSS文件较大，考虑拆分为多个文件')
        
        if len(validation_result['issues']) > 0:
            suggestions.append('修复语法错误以确保CSS正常工作')
        
        if stats['comments_count'] == 0:
            suggestions.append('添加注释以提高代码可维护性')
        
        return suggestions
    
    def _analyze_html_structure(self, html_content: str) -> Dict:
        """分析HTML结构"""
        return {
            'has_doctype': bool(re.search(r'<!DOCTYPE\s+html>', html_content, re.IGNORECASE)),
            'has_html_tag': bool(re.search(r'<html[^>]*>', html_content, re.IGNORECASE)),
            'has_head': bool(re.search(r'<head[^>]*>.*</head>', html_content, re.DOTALL | re.IGNORECASE)),
            'has_body': bool(re.search(r'<body[^>]*>.*</body>', html_content, re.DOTALL | re.IGNORECASE)),
            'title_count': len(re.findall(r'<title[^>]*>.*</title>', html_content, re.DOTALL | re.IGNORECASE)),
            'meta_count': len(re.findall(r'<meta[^>]*>', html_content, re.IGNORECASE)),
            'script_count': len(re.findall(r'<script[^>]*>', html_content, re.IGNORECASE)),
            'link_count': len(re.findall(r'<link[^>]*>', html_content, re.IGNORECASE))
        }
    
    def _analyze_seo(self, html_content: str) -> Dict:
        """SEO分析"""
        return {
            'has_title': bool(re.search(r'<title[^>]*>.*</title>', html_content, re.DOTALL | re.IGNORECASE)),
            'has_meta_description': bool(re.search(r'<meta[^>]*name=["\']description["\'][^>]*>', html_content, re.IGNORECASE)),
            'has_meta_keywords': bool(re.search(r'<meta[^>]*name=["\']keywords["\'][^>]*>', html_content, re.IGNORECASE)),
            'h1_count': len(re.findall(r'<h1[^>]*>.*</h1>', html_content, re.DOTALL | re.IGNORECASE)),
            'img_without_alt': len(re.findall(r'<img(?![^>]*alt=)[^>]*>', html_content, re.IGNORECASE))
        }
    
    def _analyze_accessibility(self, html_content: str) -> Dict:
        """可访问性分析"""
        return {
            'images_without_alt': len(re.findall(r'<img(?![^>]*alt=)[^>]*>', html_content, re.IGNORECASE)),
            'links_without_title': len(re.findall(r'<a(?![^>]*title=)[^>]*>', html_content, re.IGNORECASE)),
            'forms_without_labels': len(re.findall(r'<input(?![^>]*aria-label=)(?![^>]*id=)[^>]*>', html_content, re.IGNORECASE))
        }
    
    def _analyze_html_performance(self, html_content: str) -> Dict:
        """HTML性能分析"""
        return {
            'inline_styles': len(re.findall(r'style\s*=', html_content, re.IGNORECASE)),
            'inline_scripts': len(re.findall(r'<script[^>]*>(?!</script>)', html_content, re.IGNORECASE)),
            'external_scripts': len(re.findall(r'<script[^>]*src=', html_content, re.IGNORECASE)),
            'external_stylesheets': len(re.findall(r'<link[^>]*rel=["\']stylesheet["\']', html_content, re.IGNORECASE))
        }
    
    def _generate_html_suggestions(self, analysis_result: Dict) -> List[str]:
        """生成HTML优化建议"""
        suggestions = []
        
        structure = analysis_result['structure']
        seo = analysis_result['seo_analysis']
        accessibility = analysis_result['accessibility']
        performance = analysis_result['performance']
        
        if not structure['has_doctype']:
            suggestions.append('添加HTML5 DOCTYPE声明')
        
        if not seo['has_title']:
            suggestions.append('添加页面标题')
        
        if not seo['has_meta_description']:
            suggestions.append('添加meta description以改善SEO')
        
        if accessibility['images_without_alt'] > 0:
            suggestions.append(f'为{accessibility["images_without_alt"]}个图片添加alt属性')
        
        if performance['inline_styles'] > 5:
            suggestions.append('减少内联样式，使用外部CSS文件')
        
        return suggestions
    
    def _generate_bundle_recommendations(self, bundle_analysis: Dict) -> List[str]:
        """生成打包优化建议"""
        recommendations = []
        
        total_size_mb = bundle_analysis['total_size'] / (1024 * 1024)
        
        if total_size_mb > 5:
            recommendations.append('总文件大小较大，考虑代码分割和懒加载')
        
        # 检查大文件
        large_js_files = [f for f in bundle_analysis['javascript_files'] if f['size_kb'] > 500]
        if large_js_files:
            recommendations.append(f'发现{len(large_js_files)}个大型JavaScript文件，考虑拆分')
        
        large_images = [f for f in bundle_analysis['image_files'] if f['size_kb'] > 1000]
        if large_images:
            recommendations.append(f'发现{len(large_images)}个大型图片文件，考虑压缩或使用WebP格式')
        
        return recommendations
    
    def _find_unused_css(self, css_file: Path, html_files: List[Path]) -> List[Dict]:
        """查找未使用的CSS"""
        # 简化实现
        unused_css = []
        
        try:
            with open(css_file, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # 提取CSS选择器
            selectors = re.findall(r'([^{}]+)\s*{', css_content)
            
            # 读取所有HTML内容
            all_html_content = ""
            for html_file in html_files:
                try:
                    with open(html_file, 'r', encoding='utf-8') as f:
                        all_html_content += f.read()
                except:
                    continue
            
            # 检查选择器是否在HTML中使用
            for selector in selectors:
                selector = selector.strip()
                if selector and not self._is_selector_used(selector, all_html_content):
                    unused_css.append({
                        'file': str(css_file),
                        'selector': selector,
                        'type': 'unused_css_rule'
                    })
        
        except Exception:
            pass
        
        return unused_css
    
    def _find_unused_js_functions(self, js_file: Path, all_js_files: List[Path]) -> List[Dict]:
        """查找未使用的JavaScript函数"""
        unused_functions = []
        
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # 提取函数定义
            functions = re.findall(r'function\s+(\w+)\s*\(', js_content)
            functions.extend(re.findall(r'(\w+)\s*=\s*function', js_content))
            
            # 读取所有JavaScript内容
            all_js_content = ""
            for other_js_file in all_js_files:
                if other_js_file != js_file:
                    try:
                        with open(other_js_file, 'r', encoding='utf-8') as f:
                            all_js_content += f.read()
                    except:
                        continue
            
            # 检查函数是否被调用
            for func_name in functions:
                if func_name and not re.search(rf'\b{func_name}\s*\(', all_js_content):
                    unused_functions.append({
                        'file': str(js_file),
                        'function': func_name,
                        'type': 'unused_function'
                    })
        
        except Exception:
            pass
        
        return unused_functions
    
    def _is_selector_used(self, selector: str, html_content: str) -> bool:
        """检查CSS选择器是否在HTML中使用"""
        # 简化检查：只检查类名和ID
        if selector.startswith('.'):
            class_name = selector[1:].split(':')[0].split(' ')[0]
            return f'class="{class_name}"' in html_content or f"class='{class_name}'" in html_content
        elif selector.startswith('#'):
            id_name = selector[1:].split(':')[0].split(' ')[0]
            return f'id="{id_name}"' in html_content or f"id='{id_name}'" in html_content
        else:
            # 标签选择器
            tag_name = selector.split(':')[0].split(' ')[0]
            return f'<{tag_name}' in html_content
    
    def _generate_cleanup_recommendations(self, unused_analysis: Dict) -> List[str]:
        """生成清理建议"""
        recommendations = []
        
        if unused_analysis['unused_css']:
            recommendations.append(f"移除{len(unused_analysis['unused_css'])}个未使用的CSS规则")
        
        if unused_analysis['unused_js_functions']:
            recommendations.append(f"移除{len(unused_analysis['unused_js_functions'])}个未使用的JavaScript函数")
        
        if not recommendations:
            recommendations.append("未发现明显的未使用代码")
        
        return recommendations
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            file_path = args[0]
            if file_path.endswith('.js'):
                return await self.check_javascript_syntax(file_path)
            elif file_path.endswith('.css'):
                return await self.validate_css(file_path)
            elif file_path.endswith('.html'):
                return await self.analyze_html_structure(file_path)
            else:
                return await self.analyze_bundle_size(file_path)
        else:
            return ToolResult(
                success=False,
                error="缺少文件路径参数"
            )
