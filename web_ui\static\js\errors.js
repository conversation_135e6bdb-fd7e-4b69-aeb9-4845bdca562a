/**
 * 错误日志监控JavaScript
 * 提供实时错误监控和分析功能
 */

class ErrorMonitor {
    constructor() {
        this.errors = [];
        this.statistics = {};
        this.chart = null;
        this.currentFilters = {
            hours: 24,
            level: '',
            exception_type: '',
            logger_name: '',
            query: ''
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadErrors();
        this.setupAutoRefresh();
    }

    setupEventListeners() {
        // 过滤器事件
        document.getElementById('hoursFilter').addEventListener('change', (e) => {
            this.currentFilters.hours = parseInt(e.target.value);
            this.loadErrors();
        });

        document.getElementById('levelFilter').addEventListener('change', (e) => {
            this.currentFilters.level = e.target.value;
            this.loadErrors();
        });

        document.getElementById('exceptionFilter').addEventListener('change', (e) => {
            this.currentFilters.exception_type = e.target.value;
            this.loadErrors();
        });

        document.getElementById('loggerFilter').addEventListener('change', (e) => {
            this.currentFilters.logger_name = e.target.value;
            this.loadErrors();
        });

        // 搜索事件
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.currentFilters.query = document.getElementById('searchQuery').value;
            this.loadErrors();
        });

        document.getElementById('searchQuery').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.currentFilters.query = e.target.value;
                this.loadErrors();
            }
        });

        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadErrors();
        });

        // 导出按钮
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportErrors();
        });

        // 复制错误信息
        document.getElementById('copyErrorBtn').addEventListener('click', () => {
            this.copyErrorToClipboard();
        });
    }

    async loadErrors() {
        try {
            this.showLoading();

            // 构建查询参数
            const params = new URLSearchParams();
            Object.keys(this.currentFilters).forEach(key => {
                if (this.currentFilters[key]) {
                    params.append(key, this.currentFilters[key]);
                }
            });
            params.append('limit', '200');

            // 获取错误数据
            const response = await fetch(`/api/errors?${params}`);
            const result = await response.json();

            if (result.success) {
                this.errors = result.data;
                this.renderErrors();
                this.updateFilterOptions();

                // 加载统计信息
                await this.loadStatistics();
            } else {
                this.showError('加载错误日志失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        }
    }

    async loadStatistics() {
        try {
            const response = await fetch(`/api/errors/statistics?hours=${this.currentFilters.hours}`);
            const result = await response.json();

            if (result.success) {
                this.statistics = result.data;
                this.updateStatistics();
                this.updateChart();
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    updateStatistics() {
        const stats = this.statistics;

        document.getElementById('totalErrors').textContent = stats.total_errors || 0;
        document.getElementById('criticalErrors').textContent =
            (stats.by_level && stats.by_level.CRITICAL) || 0;

        // 计算最近1小时的错误
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const recentErrors = this.errors.filter(error =>
            new Date(error.timestamp) >= oneHourAgo
        ).length;
        document.getElementById('recentErrors').textContent = recentErrors;

        // 计算错误率（这里简化为错误数/总数的百分比）
        const errorRate = stats.total_errors > 0 ?
            ((stats.by_level?.ERROR || 0) / stats.total_errors * 100).toFixed(1) + '%' : '0%';
        document.getElementById('errorRate').textContent = errorRate;
    }

    updateChart() {
        const ctx = document.getElementById('errorTrendChart').getContext('2d');

        if (this.chart) {
            this.chart.destroy();
        }

        const trendData = this.statistics.error_trend || [];

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(item => item.hour),
                datasets: [{
                    label: '错误数量',
                    data: trendData.map(item => item.count),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    renderErrors() {
        const container = document.getElementById('errorsList');

        if (this.errors.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <div>太棒了！没有发现错误</div>
                    <small>在指定的时间范围内没有找到任何错误日志</small>
                </div>
            `;
            return;
        }

        const html = this.errors.map(error => this.renderErrorItem(error)).join('');
        container.innerHTML = html;

        // 添加点击事件
        container.querySelectorAll('.error-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.showErrorDetail(this.errors[index]);
            });
        });

        this.hideLoading();
        this.updateErrorCount();
    }

    renderErrorItem(error) {
        const timestamp = new Date(error.timestamp).toLocaleString('zh-CN');
        const levelClass = `error-level-${error.level.toLowerCase()}`;

        return `
            <div class="error-item p-3 border-bottom ${levelClass}">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-${this.getLevelColor(error.level)} me-2">${error.level}</span>
                            <span class="badge bg-light text-dark me-2">${error.logger_name}</span>
                            ${error.session_id ? `<span class="badge bg-info">会话: ${error.session_id.slice(-8)}</span>` : ''}
                        </div>
                        <h6 class="mb-1">${this.escapeHtml(error.message.substring(0, 100))}${error.message.length > 100 ? '...' : ''}</h6>
                        ${error.exception_type ? `
                            <div class="text-danger small">
                                <strong>${error.exception_type}:</strong> ${this.escapeHtml(error.exception_message.substring(0, 80))}${error.exception_message.length > 80 ? '...' : ''}
                            </div>
                        ` : ''}
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            ${timestamp}
                        </small>
                        <br>
                        ${error.file_path ? `
                            <small class="text-muted">
                                <i class="fas fa-file-code"></i>
                                ${error.file_path.split('/').pop()}:${error.line_number}
                            </small>
                        ` : ''}
                    </div>
                    <div class="col-md-3 text-end">
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" onclick="event.stopPropagation();">
                                <i class="fas fa-eye"></i> 查看
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="event.stopPropagation(); this.copyErrorToClipboard()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    showErrorDetail(error) {
        try {
            const modalElement = document.getElementById('errorDetailModal');
            const content = document.getElementById('errorDetailContent');

            if (!modalElement || !content) {
                console.error('模态框元素未找到');
                return;
            }

            // 清理之前的模态框实例
            const existingModal = bootstrap.Modal.getInstance(modalElement);
            if (existingModal) {
                existingModal.dispose();
            }

            const timestamp = new Date(error.timestamp).toLocaleString('zh-CN');

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>时间:</strong></td><td>${timestamp}</td></tr>
                            <tr><td><strong>级别:</strong></td><td><span class="badge bg-${this.getLevelColor(error.level)}">${error.level}</span></td></tr>
                            <tr><td><strong>日志器:</strong></td><td>${error.logger_name}</td></tr>
                            <tr><td><strong>文件:</strong></td><td>${error.file_path || 'N/A'}</td></tr>
                            <tr><td><strong>行号:</strong></td><td>${error.line_number || 'N/A'}</td></tr>
                            <tr><td><strong>函数:</strong></td><td>${error.function_name || 'N/A'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>上下文信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>会话ID:</strong></td><td>${error.session_id || 'N/A'}</td></tr>
                            <tr><td><strong>任务ID:</strong></td><td>${error.task_id || 'N/A'}</td></tr>
                            <tr><td><strong>异常类型:</strong></td><td>${error.exception_type || 'N/A'}</td></tr>
                            <tr><td><strong>日志文件:</strong></td><td>${error.source_file}</td></tr>
                        </table>
                    </div>
                </div>

                <div class="mt-3">
                    <h6>错误消息</h6>
                    <div class="alert alert-danger">
                        ${this.escapeHtml(error.message)}
                    </div>
                </div>

                ${error.exception_message ? `
                    <div class="mt-3">
                        <h6>异常消息</h6>
                        <div class="alert alert-warning">
                            <strong>${error.exception_type}:</strong> ${this.escapeHtml(error.exception_message)}
                        </div>
                    </div>
                ` : ''}

                ${error.traceback ? `
                    <div class="mt-3">
                        <h6>堆栈跟踪</h6>
                        <div class="traceback-container">
                            <pre><code class="language-python">${this.escapeHtml(error.traceback)}</code></pre>
                        </div>
                    </div>
                ` : ''}
            `;

            // 存储当前错误用于复制
            this.currentError = error;

            // 创建新的模态框实例
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',  // 禁用点击背景关闭
                keyboard: true,
                focus: true
            });

            // 添加事件监听器
            modalElement.addEventListener('hidden.bs.modal', () => {
                // 模态框关闭后清理
                this.currentError = null;
            }, { once: true });

            // 显示模态框
            modal.show();

            // 彻底修复z-index和堆叠上下文问题
            setTimeout(() => {
                // 禁用可能影响堆叠上下文的CSS属性
                const contentWrapper = document.querySelector('.content-wrapper');
                const mainContent = document.querySelector('.main-content');
                const cards = document.querySelectorAll('.card');

                if (contentWrapper) {
                    contentWrapper.style.backdropFilter = 'none';
                    contentWrapper.style.transform = 'none';
                }
                if (mainContent) {
                    mainContent.style.backdropFilter = 'none';
                    mainContent.style.transform = 'none';
                }
                cards.forEach(card => {
                    card.style.backdropFilter = 'none';
                    card.style.transform = 'none';
                });

                // 设置极高的z-index
                modalElement.style.zIndex = '999999';
                modalElement.style.position = 'fixed';

                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.style.zIndex = '999998';
                    backdrop.style.position = 'fixed';
                }

                const dialog = modalElement.querySelector('.modal-dialog');
                if (dialog) {
                    dialog.style.zIndex = '999999';
                    dialog.style.position = 'relative';
                }

                const content = modalElement.querySelector('.modal-content');
                if (content) {
                    content.style.zIndex = '999999';
                    content.style.position = 'relative';
                    content.style.background = 'white';
                }

                // 确保所有按钮可点击
                const buttons = modalElement.querySelectorAll('.btn, .btn-close');
                buttons.forEach(btn => {
                    btn.style.zIndex = '999999';
                    btn.style.position = 'relative';
                    btn.style.pointerEvents = 'auto';
                });

                console.log('模态框z-index已强制修复');
            }, 50);

            // 高亮代码
            if (window.Prism) {
                Prism.highlightAll();
            }

            console.log('模态框已显示');
        } catch (error) {
            console.error('显示错误详情失败:', error);
            alert('显示错误详情失败，请刷新页面重试');
        }
    }

    updateFilterOptions() {
        // 更新异常类型选项
        const exceptionTypes = [...new Set(this.errors
            .map(error => error.exception_type)
            .filter(type => type)
        )].sort();

        const exceptionSelect = document.getElementById('exceptionFilter');
        const currentException = exceptionSelect.value;
        exceptionSelect.innerHTML = '<option value="">全部类型</option>';
        exceptionTypes.forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = type;
            if (type === currentException) option.selected = true;
            exceptionSelect.appendChild(option);
        });

        // 更新日志器选项
        const loggers = [...new Set(this.errors
            .map(error => error.logger_name)
        )].sort();

        const loggerSelect = document.getElementById('loggerFilter');
        const currentLogger = loggerSelect.value;
        loggerSelect.innerHTML = '<option value="">全部日志器</option>';
        loggers.forEach(logger => {
            const option = document.createElement('option');
            option.value = logger;
            option.textContent = logger;
            if (logger === currentLogger) option.selected = true;
            loggerSelect.appendChild(option);
        });
    }

    getLevelColor(level) {
        const colors = {
            'CRITICAL': 'danger',
            'ERROR': 'warning',
            'WARNING': 'info',
            'INFO': 'secondary'
        };
        return colors[level] || 'secondary';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    updateErrorCount() {
        const countElement = document.getElementById('errorCount');
        if (countElement) {
            countElement.textContent = `${this.errors.length} 个错误`;
        }
    }

    showLoading() {
        const container = document.getElementById('errorsList');
        container.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
                <div>正在收集错误日志...</div>
            </div>
        `;
    }

    hideLoading() {
        // Loading is handled in renderErrors
    }

    showError(message) {
        this.hideLoading();
        const container = document.getElementById('errorsList');
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                ${message}
            </div>
        `;
        container.style.display = 'block';
    }

    exportErrors() {
        const data = this.errors.map(error => ({
            时间: new Date(error.timestamp).toLocaleString('zh-CN'),
            级别: error.level,
            日志器: error.logger_name,
            消息: error.message,
            异常类型: error.exception_type || '',
            异常消息: error.exception_message || '',
            文件: error.file_path || '',
            行号: error.line_number || '',
            函数: error.function_name || '',
            会话ID: error.session_id || '',
            任务ID: error.task_id || ''
        }));

        const csv = this.convertToCSV(data);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `errors_${new Date().toISOString().slice(0, 10)}.csv`;
        link.click();
    }

    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row =>
                headers.map(header =>
                    `"${(row[header] || '').toString().replace(/"/g, '""')}"`
                ).join(',')
            )
        ].join('\n');

        return '\ufeff' + csvContent; // 添加BOM以支持中文
    }

    copyErrorToClipboard() {
        if (!this.currentError) return;

        const error = this.currentError;
        const text = `
错误时间: ${new Date(error.timestamp).toLocaleString('zh-CN')}
错误级别: ${error.level}
日志器: ${error.logger_name}
错误消息: ${error.message}
异常类型: ${error.exception_type || 'N/A'}
异常消息: ${error.exception_message || 'N/A'}
文件位置: ${error.file_path || 'N/A'}:${error.line_number || 'N/A'}
函数名称: ${error.function_name || 'N/A'}
会话ID: ${error.session_id || 'N/A'}
任务ID: ${error.task_id || 'N/A'}

堆栈跟踪:
${error.traceback || 'N/A'}
        `.trim();

        navigator.clipboard.writeText(text).then(() => {
            // 显示复制成功提示
            const btn = document.getElementById('copyErrorBtn');
            const originalText = btn.textContent;
            btn.textContent = '已复制!';
            btn.classList.add('btn-success');
            btn.classList.remove('btn-primary');

            setTimeout(() => {
                btn.textContent = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-primary');
            }, 2000);
        });
    }

    setupAutoRefresh() {
        // 每30秒自动刷新
        setInterval(() => {
            this.loadErrors();
        }, 30000);
    }
}

// 全局错误监控实例
let errorMonitor;

// 初始化错误监控
document.addEventListener('DOMContentLoaded', () => {
    errorMonitor = new ErrorMonitor();

    // 添加全局错误处理
    window.addEventListener('error', (event) => {
        console.error('页面错误:', event.error);
    });

    // 添加调试信息
    console.log('错误监控系统已初始化');
});
