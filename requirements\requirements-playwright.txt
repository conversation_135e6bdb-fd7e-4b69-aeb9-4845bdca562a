# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --constraint=requirements/common-constraints.txt --output-file=requirements/requirements-playwright.txt requirements/requirements-playwright.in
greenlet==3.1.1
    # via
    #   -c requirements/common-constraints.txt
    #   playwright
playwright==1.51.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-playwright.in
pyee==12.1.1
    # via
    #   -c requirements/common-constraints.txt
    #   playwright
typing-extensions==4.13.2
    # via
    #   -c requirements/common-constraints.txt
    #   pyee
