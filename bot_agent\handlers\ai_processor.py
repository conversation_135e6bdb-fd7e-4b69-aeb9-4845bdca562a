"""
AI 处理器 - 处理任务并生成 AI 响应
"""

import logging
import os
import re
import importlib
import subprocess
from pathlib import Path
from typing import Dict, Optional, Tuple, List, Any

# 设置日志记录器
logger = logging.getLogger(__name__)

# 延迟导入 Aider 模块，避免在启动时就加载
# from aider.main import run_aider

# 导入 GitLab 分支管理模块
from bot_agent.utils.gitlab_branch_manager import GitLabBranchManager

from bot_agent.handlers.ai_response_handler import AIResponseHandler
from bot_agent.handlers.information_query_handler import InformationQueryHandler
from bot_agent.deployment.deployment_task_executor import DeploymentTaskExecutor

logger = logging.getLogger(__name__)


class AIProcessor:
    """
    AI 处理器，负责处理任务并生成 AI 响应
    """

    def __init__(self):
        """初始化 AI 处理器"""
        self.response_handler = AIResponseHandler()
        self.query_handler = InformationQueryHandler()
        self.deployment_executor = DeploymentTaskExecutor()

        # 初始化 GitLab 客户端
        from bot_agent.clients.gitlab_client import GitLabClient
        self.gitlab_client = GitLabClient()

        # 初始化 GitLab 分支管理器 - 使用环境变量中的项目下载目录
        projects_dir = os.environ.get("PROJECTS_DIR")
        if projects_dir:
            logger.info(f"使用环境变量中的项目下载目录: {projects_dir}")
        else:
            # 如果没有设置环境变量，使用默认目录
            parent_dir = os.path.dirname(os.getcwd())
            projects_dir = os.path.join(parent_dir, "git-repos")
            logger.info(f"使用默认的Git仓库目录: {projects_dir}")

            # 设置环境变量，确保其他组件能够使用相同的目录
            os.environ["PROJECTS_DIR"] = projects_dir

        # 确保目录存在
        os.makedirs(projects_dir, exist_ok=True)

        # 初始化分支管理器
        self.branch_manager = GitLabBranchManager(self.gitlab_client, projects_dir)

        logger.info(f"AI processor initialized with Git repository directory: {projects_dir}")

    async def process_task(self, task: Dict) -> Dict:
        """
        处理任务并生成 AI 响应

        Args:
            task: 任务对象

        Returns:
            Dict: 处理结果
        """
        try:
            # 获取任务信息
            task_id = task.get("id", "unknown")
            target_component = task.get("target_component")
            title = task.get("title", "")
            description = task.get("description", "")

            logger.info(f"Processing task {task_id} with {target_component}")

            # 根据任务类型和目标组件选择处理方式
            task_type = task.get("analysis", {}).get("task_type", "unknown")
            metadata = task.get("metadata", {})
            event_type = metadata.get("event_type", "")

            # 检查是否是Pipeline事件触发的任务
            if event_type == "Pipeline Hook" and metadata.get("auto_triggered"):
                # 处理Pipeline自动触发的任务
                ai_response = await self._process_pipeline_task(task)
            elif task_type == "information_query":
                # 处理信息查询任务
                ai_response = await self._process_information_query(task)
            elif target_component == "aider":
                ai_response = await self._process_with_aider(task)
            else:
                # 默认使用简单的回复
                ai_response = self._generate_simple_response(task)

            # 发送响应
            result = await self.response_handler.process_and_send_response(task, ai_response)

            return {
                "status": "success",
                "message": f"Task {task_id} processed successfully",
                "result": result,
            }
        except Exception as e:
            logger.error(f"Error processing task: {e}", exc_info=True)
            return {
                "status": "error",
                "message": f"Error processing task: {str(e)}",
            }

    async def _process_with_aider(self, task: Dict) -> str:
        """
        使用基于Aider的任务执行引擎处理任务

        Args:
            task: 任务对象

        Returns:
            str: AI 生成的响应
        """
        try:
            # 导入新的任务执行引擎和进度跟踪器
            from bot_agent.engines.task_executor import AiderBasedTaskExecutor
            from bot_agent.engines.simple_progress_tracker import progress_tracker

            title = task.get("title", "")
            description = task.get("description", "")
            task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")

            logger.info(f"使用AiderBasedTaskExecutor处理任务: {title}")

            # 发送开始通知
            await progress_tracker.send_start_notification(task)

            # 创建任务执行引擎
            executor = AiderBasedTaskExecutor()

            # 检查任务类型，决定处理方式
            if "目录结构" in description or "项目结构" in description or "directory structure" in description.lower():
                # 对于目录结构分析，使用原有的集成方式
                from bot_agent.handlers.aider_integration import AiderIntegration
                aider = AiderIntegration()

                metadata = task.get("metadata", {})
                project_id = metadata.get("project_id")
                project_path = None

                if project_id:
                    try:
                        project = self.gitlab_client.get_project(project_id)
                        if project:
                            project_name = project.get("name")
                            projects_dir = os.environ.get("PROJECTS_DIR")
                            if not projects_dir:
                                parent_dir = os.path.dirname(os.getcwd())
                                projects_dir = os.path.join(parent_dir, "git-repos")
                            project_path = os.path.join(projects_dir, project_name)
                    except Exception as e:
                        logger.error(f"获取项目路径时出错: {e}")

                if aider.aider_available:
                    response = await aider.analyze_project_structure(project_path)
                    return response
                else:
                    return "Aider AI 模块当前不可用，无法分析项目结构。"

            # 所有任务都使用Aider执行引擎处理
            logger.info(f"使用Aider执行引擎处理 {task_type} 类型任务")

            # 执行任务
            result = await executor.execute_task(task)

            # 不再通过进度跟踪器发送完成通知，因为AI响应处理器会发送详细的执行报告
            # 这样避免重复发送两个最终结果
            # await progress_tracker.send_completion_notification(task, result)

            if result["status"] == "success":
                return result["report"]
            else:
                return f"任务执行失败: {result.get('error', '未知错误')}"

        except Exception as e:
            logger.error(f"处理 Aider 任务时出错: {e}", exc_info=True)
            return f"处理任务时出错: {str(e)}"

    async def _process_information_query(self, task: Dict) -> str:
        """
        处理信息查询任务

        Args:
            task: 任务对象

        Returns:
            str: 查询结果
        """
        try:
            title = task.get("title", "")
            logger.info(f"处理信息查询任务: {title}")

            # 获取项目路径
            metadata = task.get("metadata", {})
            project_id = metadata.get("project_id")
            project_path = None

            if project_id:
                try:
                    project = self.gitlab_client.get_project(project_id)
                    if project:
                        project_name = project.get("name")
                        projects_dir = os.environ.get("PROJECTS_DIR")
                        if not projects_dir:
                            parent_dir = os.path.dirname(os.getcwd())
                            projects_dir = os.path.join(parent_dir, "git-repos")
                        project_path = os.path.join(projects_dir, project_name)

                        # 确保项目存在
                        if not os.path.exists(project_path):
                            # 尝试设置仓库
                            success, repo_path = self.branch_manager.setup_repository(project_id)
                            if success and repo_path:
                                project_path = repo_path
                            else:
                                return f"❌ 无法访问项目 {project_name}，请确保项目已克隆到本地"

                except Exception as e:
                    logger.error(f"获取项目信息时出错: {e}")
                    return f"❌ 获取项目信息失败: {str(e)}"

            if not project_path:
                return "❌ 无法确定项目路径，请确保在GitLab Issue中提出查询请求"

            # 使用信息查询处理器处理查询
            result = await self.query_handler.handle_query(task, project_path)

            return result

        except Exception as e:
            logger.error(f"处理信息查询任务时出错: {e}", exc_info=True)
            return f"❌ 查询失败: {str(e)}"

    async def _process_pipeline_task(self, task: Dict) -> str:
        """
        处理Pipeline自动触发的任务

        Args:
            task: 任务对象

        Returns:
            str: 处理结果
        """
        try:
            metadata = task.get("metadata", {})
            pipeline_id = metadata.get("pipeline_id")
            monitoring_reason = metadata.get("monitoring_reason", "unknown")

            logger.info(f"处理Pipeline任务: Pipeline {pipeline_id}, 原因: {monitoring_reason}")

            # 使用部署任务执行器处理
            result = await self.deployment_executor.execute_deployment_task(task)

            if result.get("status") == "success":
                return result.get("report", "Pipeline任务处理完成")
            else:
                error_msg = result.get("error", "未知错误")
                return f"❌ Pipeline任务处理失败: {error_msg}"

        except Exception as e:
            logger.error(f"处理Pipeline任务时出错: {e}", exc_info=True)
            return f"❌ Pipeline任务处理失败: {str(e)}"

    def _format_aider_result(self, result: Dict, action: str) -> str:
        """
        格式化 Aider 结果

        Args:
            result: Aider 结果
            action: 执行的操作

        Returns:
            str: 格式化后的结果
        """
        if action == "分析":
            if "issues" in result and result["issues"]:
                issues = result["issues"]
                issues_text = "\n".join([f"- 第 {issue.get('line', '?')} 行: {issue.get('message', '未知问题')}" for issue in issues])
                return f"""
分析结果：

发现了 {len(issues)} 个问题：

{issues_text}
"""
            else:
                return "分析完成，未发现问题。"

        elif action == "优化":
            if "optimized_code" in result and result["optimized_code"]:
                return "优化完成，已生成优化后的代码。"
            else:
                return "代码已经是最优的，无需进一步优化。"

        elif action == "生成测试":
            if "test_code" in result and result["test_code"]:
                return "测试生成完成，已创建测试文件。"
            else:
                return "无法为当前代码生成测试。"

        return "操作完成。"

    def _setup_gitlab_repo(self, project_id: int, branch_name: str) -> Tuple[bool, str]:
        """
        设置 GitLab 仓库，包括创建分支和克隆仓库

        Args:
            project_id: 项目 ID
            branch_name: 分支名称

        Returns:
            Tuple[bool, str]: 是否成功，以及仓库路径
        """
        try:
            logger.info(f"Setting up GitLab repo for project {project_id}, branch {branch_name}")

            # 获取项目信息
            project = self.gitlab_client.get_project(project_id)
            if not project:
                logger.error(f"Failed to get project info for project {project_id}")
                return False, ""

            project_name = project.get("name", "unknown")
            project_url = project.get("http_url_to_repo")

            if not project_url:
                logger.error(f"No repository URL found for project {project_id}")
                return False, ""

            logger.info(f"Project info: {project_name}, URL: {project_url}")

            # 确定项目目录
            projects_dir = os.environ.get("PROJECTS_DIR")
            if not projects_dir:
                # 如果没有设置环境变量，使用默认目录
                parent_dir = os.path.dirname(os.getcwd())
                projects_dir = os.path.join(parent_dir, "git-repos")
                logger.info(f"Using default Git repos directory: {projects_dir}")

                # 设置环境变量
                os.environ["PROJECTS_DIR"] = projects_dir

            # 确保目录存在
            os.makedirs(projects_dir, exist_ok=True)

            # 确定仓库路径
            repo_path = os.path.join(projects_dir, project_name)
            logger.info(f"Repository path: {repo_path}")

            # 检查仓库是否已存在
            if os.path.exists(os.path.join(repo_path, ".git")):
                logger.info(f"Repository already exists at {repo_path}, pulling latest changes")

                # 切换到仓库目录
                os.chdir(repo_path)

                # 获取当前分支
                try:
                    # 使用系统默认编码
                    import locale
                    system_encoding = locale.getpreferredencoding()
                    logger.info(f"使用系统编码: {system_encoding}")

                    current_branch = subprocess.check_output(
                        ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                        text=True,
                        encoding=system_encoding,
                        errors="replace"
                    ).strip()
                    logger.info(f"Current branch: {current_branch}")

                    # 如果当前不在main分支，切换到main分支
                    if current_branch != "main":
                        logger.info("Switching to main branch")
                        subprocess.run(
                            ["git", "checkout", "main"],
                            check=True,
                            encoding=system_encoding,
                            errors="replace"
                        )

                    # 拉取最新代码
                    logger.info("Pulling latest changes")
                    subprocess.run(
                        ["git", "pull"],
                        check=True,
                        encoding=system_encoding,
                        errors="replace"
                    )

                    # 检查分支是否存在
                    branches = subprocess.check_output(
                        ["git", "branch", "--list", branch_name],
                        text=True,
                        encoding=system_encoding,
                        errors="replace"
                    ).strip()

                    if branches:
                        # 分支已存在，切换到该分支
                        logger.info(f"Branch {branch_name} already exists, checking out")
                        subprocess.run(
                            ["git", "checkout", branch_name],
                            check=True,
                            encoding=system_encoding,
                            errors="replace"
                        )

                        # 拉取最新代码
                        logger.info(f"Pulling latest changes for branch {branch_name}")
                        subprocess.run(
                            ["git", "pull"],
                            check=True,
                            encoding=system_encoding,
                            errors="replace"
                        )
                    else:
                        # 创建新分支
                        logger.info(f"Creating new branch {branch_name}")
                        subprocess.run(
                            ["git", "checkout", "-b", branch_name],
                            check=True,
                            encoding=system_encoding,
                            errors="replace"
                        )
                except subprocess.CalledProcessError as e:
                    logger.error(f"Git command failed: {e}")
                    # 尝试使用分支管理器作为备选方案
                    logger.info("Falling back to branch manager")
                    # 使用我们的新方法设置仓库
                    logger.info(f"使用_setup_gitlab_repo方法设置仓库")
                    success, repo_path = self._setup_gitlab_repo(
                        project_id=project_id,
                        branch_name=branch_name
                    )
                    if not success:
                        logger.error(f"Repository setup failed")
                        return False, ""
            else:
                # 仓库不存在，克隆仓库
                logger.info(f"Repository does not exist, cloning from {project_url}")
                try:
                    # 使用系统默认编码
                    import locale
                    system_encoding = locale.getpreferredencoding()
                    logger.info(f"使用系统编码: {system_encoding}")

                    # 克隆仓库
                    subprocess.run(
                        ["git", "clone", project_url, repo_path],
                        check=True,
                        encoding=system_encoding,
                        errors="replace"
                    )

                    # 切换到仓库目录
                    os.chdir(repo_path)

                    # 创建新分支
                    logger.info(f"Creating new branch {branch_name}")
                    subprocess.run(
                        ["git", "checkout", "-b", branch_name],
                        check=True,
                        encoding=system_encoding,
                        errors="replace"
                    )
                except subprocess.CalledProcessError as e:
                    logger.error(f"Git command failed: {e}")
                    # 尝试使用分支管理器作为备选方案
                    logger.info("Falling back to branch manager")
                    # 使用我们的新方法设置仓库
                    logger.info(f"使用_setup_gitlab_repo方法设置仓库")
                    success, repo_path = self._setup_gitlab_repo(
                        project_id=project_id,
                        branch_name=branch_name
                    )
                    if not success:
                        logger.error(f"Repository setup failed")
                        return False, ""

            # 设置Git用户信息（如果尚未设置）
            try:
                # 检查是否已设置用户名和邮箱
                # 使用系统默认编码
                import locale
                system_encoding = locale.getpreferredencoding()
                logger.info(f"使用系统编码: {system_encoding}")

                user_name = subprocess.check_output(
                    ["git", "config", "user.name"],
                    text=True,
                    encoding=system_encoding,
                    errors="replace",
                    stderr=subprocess.DEVNULL
                ).strip()
                user_email = subprocess.check_output(
                    ["git", "config", "user.email"],
                    text=True,
                    encoding=system_encoding,
                    errors="replace",
                    stderr=subprocess.DEVNULL
                ).strip()

                # 如果未设置，使用默认值
                if not user_name:
                    subprocess.run(
                        ["git", "config", "user.name", "AI Assistant"],
                        check=True,
                        encoding=system_encoding,
                        errors="replace"
                    )
                    logger.info("Set Git user.name to 'AI Assistant'")

                if not user_email:
                    subprocess.run(
                        ["git", "config", "user.email", "<EMAIL>"],
                        check=True,
                        encoding=system_encoding,
                        errors="replace"
                    )
                    logger.info("Set Git user.email to '<EMAIL>'")
            except (subprocess.CalledProcessError, subprocess.SubprocessError) as e:
                logger.warning(f"Failed to set Git user info: {e}")

            # 设置环境变量，确保其他组件能够使用相同的工作空间
            # 确保repo_path是Git仓库的实际路径，而不是父目录
            if repo_path and os.path.exists(repo_path) and os.path.exists(os.path.join(repo_path, ".git")):
                logger.info(f"Git仓库设置成功: {repo_path}")
            else:
                logger.warning(f"仓库路径不是有效的Git仓库: {repo_path}")

            # 设置PROJECTS_DIR环境变量
            os.environ["PROJECTS_DIR"] = projects_dir
            logger.info(f"已设置PROJECTS_DIR环境变量: {projects_dir}")

            return True, repo_path
        except Exception as e:
            logger.error(f"Error setting up GitLab repo: {e}", exc_info=True)
            return False, ""

    def _generate_simple_response(self, task: Dict) -> str:
        """
        生成简单的响应

        Args:
            task: 任务对象

        Returns:
            str: 简单的响应
        """
        title = task.get("title", "")
        description = task.get("description", "")

        response = f"""
您好！我是AI助手，已收到您的请求："{title}"

我将尽快处理这个任务。请问您有什么具体的要求或者需要我注意的事项吗？

如果您需要更多帮助，请随时告诉我。
"""
        return response
