2025-05-28 10:56:10,835 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 10:56:10,837 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 10:56:10,837 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 10:56:10,840 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:51 - __init__ - GitLab API token not provided, some operations may fail
2025-05-28 10:56:10,840 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://192.168.123.103/api/v4
2025-05-28 10:56:10,841 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://192.168.123.103/api/v4/version
2025-05-28 10:56:11,113 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/version
2025-05-28 10:56:11,113 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 10:56:11,113 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:53 - __init__ - EnhancedJobFailureAnalyzer initialized
2025-05-28 10:56:11,114 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:66 - analyze_and_fix_job_failure - 🚀 开始增强作业失败分析: Job 728
2025-05-28 10:56:11,114 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:71 - analyze_and_fix_job_failure - 📋 第1步：获取作业信息
2025-05-28 10:56:11,115 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:132 - _get_real_job_info - 正在从GitLab获取作业信息: Project 3, Job 728
2025-05-28 10:56:11,116 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:431 - get_job - Getting job 728 in project 3
2025-05-28 10:56:11,116 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:56:11,524 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 10:56:11,525 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 10:56:11,525 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:56:11,525 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:179 - _make_request - Error response status code: 401
2025-05-28 10:56:11,526 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:183 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 10:56:11,526 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:193 - _make_request - Retrying request to projects/3/jobs/728...
2025-05-28 10:56:11,728 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 10:56:11,728 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 10:56:11,728 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 2/2): 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:56:11,728 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:179 - _make_request - Error response status code: 401
2025-05-28 10:56:11,729 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:183 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 10:56:11,729 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:202 - _make_request - 客户端错误，API仍然可用: 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:56:11,729 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:207 - _make_request - Request to projects/3/jobs/728 failed, returning None due to fallback mode
2025-05-28 10:56:11,730 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:441 - get_job - 未能获取作业 728 的信息
2025-05-28 10:56:11,730 - bot_agent.handlers.enhanced_job_failure_analyzer - ERROR - enhanced_job_failure_analyzer.py:144 - _get_real_job_info - ❌ 无法获取作业 728 的信息
2025-05-28 10:59:23,324 - bot_agent - INFO - logging_config.py:119 - setup_logging - 日志系统初始化完成 - bot_agent
2025-05-28 10:59:23,324 - bot_agent - INFO - logging_config.py:120 - setup_logging - 日志目录: E:\Projects\aider-plus\logs
2025-05-28 10:59:23,325 - bot_agent - INFO - logging_config.py:121 - setup_logging - 日志文件: app.log, error.log, daily_*.log, task_execution.log
2025-05-28 10:59:23,328 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:53 - __init__ - Initialized GitLab client with base URL: http://192.168.123.103/api/v4
2025-05-28 10:59:23,328 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:85 - _check_connection - 检查GitLab API连接: http://192.168.123.103/api/v4/version
2025-05-28 10:59:23,509 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:95 - _check_connection - Failed to connect to GitLab API: 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/version
2025-05-28 10:59:23,510 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:96 - _check_connection - API可用性设置为未知状态，后续请求将尝试连接
2025-05-28 10:59:23,510 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:53 - __init__ - EnhancedJobFailureAnalyzer initialized
2025-05-28 10:59:23,511 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:66 - analyze_and_fix_job_failure - 🚀 开始增强作业失败分析: Job 728
2025-05-28 10:59:23,511 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:71 - analyze_and_fix_job_failure - 📋 第1步：获取作业信息
2025-05-28 10:59:23,511 - bot_agent.handlers.enhanced_job_failure_analyzer - INFO - enhanced_job_failure_analyzer.py:132 - _get_real_job_info - 正在从GitLab获取作业信息: Project 3, Job 728
2025-05-28 10:59:23,511 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:431 - get_job - Getting job 728 in project 3
2025-05-28 10:59:23,511 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:136 - _make_request - Making GET request to http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:59:23,702 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 10:59:23,702 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 10:59:23,702 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 1/2): 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:59:23,702 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:179 - _make_request - Error response status code: 401
2025-05-28 10:59:23,703 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:183 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 10:59:23,703 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:193 - _make_request - Retrying request to projects/3/jobs/728...
2025-05-28 10:59:23,946 - bot_agent.clients.gitlab_client - INFO - gitlab_client.py:155 - _make_request - Response status code: 401
2025-05-28 10:59:23,946 - bot_agent.clients.gitlab_client - DEBUG - gitlab_client.py:160 - _make_request - Response content preview: b'{"message":"401 Unauthorized"}'
2025-05-28 10:59:23,946 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:174 - _make_request - GitLab API request failed (attempt 2/2): 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:59:23,947 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:179 - _make_request - Error response status code: 401
2025-05-28 10:59:23,947 - bot_agent.clients.gitlab_client - ERROR - gitlab_client.py:183 - _make_request - Error response data: {'message': '401 Unauthorized'}
2025-05-28 10:59:23,947 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:202 - _make_request - 客户端错误，API仍然可用: 401 Client Error: Unauthorized for url: http://192.168.123.103/api/v4/projects/3/jobs/728
2025-05-28 10:59:23,948 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:207 - _make_request - Request to projects/3/jobs/728 failed, returning None due to fallback mode
2025-05-28 10:59:23,948 - bot_agent.clients.gitlab_client - WARNING - gitlab_client.py:441 - get_job - 未能获取作业 728 的信息
2025-05-28 10:59:23,948 - bot_agent.handlers.enhanced_job_failure_analyzer - ERROR - enhanced_job_failure_analyzer.py:144 - _get_real_job_info - ❌ 无法获取作业 728 的信息
