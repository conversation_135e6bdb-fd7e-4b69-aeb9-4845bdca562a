# 基础镜像：只包含依赖
FROM ***************:5050/docker/ci-images/python:3.9-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app

# 安装最小化的构建依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    curl \
    # 只为需要编译的包安装编译工具
    python3-dev \
    gcc \
    libc6-dev \
    # 音频处理库的依赖
    libsndfile1 \
    # 运行时需要的包
    vim \
    neovim \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt /app/requirements.txt

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 清理构建依赖，只保留运行时需要的包
RUN apt-get update && apt-get remove -y gcc python3-dev libc6-dev \
    && apt-get autoremove -y \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
