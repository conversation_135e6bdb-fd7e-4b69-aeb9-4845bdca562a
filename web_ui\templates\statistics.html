{% extends "base.html" %}

{% block title %}统计分析 - 对话分析系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar text-primary"></i>
        统计分析
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportStats()">
                <i class="fas fa-download"></i> 导出报告
            </button>
        </div>
    </div>
</div>

<!-- 时间范围选择 -->
<div class="search-filters mb-4">
    <div class="row">
        <div class="col-md-3">
            <label for="daysRange" class="form-label">统计时间范围</label>
            <select class="form-select" id="daysRange" onchange="loadStatistics()">
                <option value="1">最近1天</option>
                <option value="3">最近3天</option>
                <option value="7" selected>最近7天</option>
                <option value="14">最近14天</option>
                <option value="30">最近30天</option>
                <option value="90">最近90天</option>
            </select>
        </div>
        <div class="col-md-9 d-flex align-items-end">
            <div class="text-muted">
                <i class="fas fa-info-circle"></i>
                统计数据基于选定时间范围内的所有会话记录
            </div>
        </div>
    </div>
</div>

<!-- 概览统计 -->
<div class="row mb-4" id="overviewStats">
    <div class="col-12">
        <div class="loading">
            <div class="spinner"></div>
            <div>加载统计数据...</div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    每日会话趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyTrendChart" height="150"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart"></i>
                    状态分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks"></i>
                    任务类型分析
                </h5>
            </div>
            <div class="card-body">
                <canvas id="taskTypeChart" height="150"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock"></i>
                    执行时长分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="durationChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 详细分析表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table"></i>
                    任务类型详细分析
                </h5>
            </div>
            <div class="card-body">
                <div id="taskTypeTable">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>加载详细数据...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentStats = null;
let charts = {};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
});

// 加载统计数据
async function loadStatistics() {
    const days = document.getElementById('daysRange').value;

    try {
        showLoading('overviewStats');
        showLoading('taskTypeTable');

        const response = await fetch(`/api/statistics?days=${days}`);
        const data = await response.json();

        if (data.success) {
            currentStats = data.data;
            updateOverviewStats(currentStats.summary);
            updateCharts(currentStats);
            updateTaskTypeTable(currentStats.task_types);
        } else {
            showError('overviewStats', data.error || '加载统计数据失败');
            showError('taskTypeTable', '加载失败');
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showError('overviewStats', '网络错误，请稍后重试');
        showError('taskTypeTable', '网络错误，请稍后重试');
    }
}

// 更新概览统计
function updateOverviewStats(summary) {
    const container = document.getElementById('overviewStats');

    let html = `
        <div class="col-md-3 mb-3">
            <div class="card metric-card bg-primary text-white">
                <div class="metric-value">${summary.total_sessions}</div>
                <div class="metric-label">总会话数</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card bg-success text-white">
                <div class="metric-value">${summary.success_rate.toFixed(1)}%</div>
                <div class="metric-label">成功率</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card bg-info text-white">
                <div class="metric-value">${summary.avg_rounds}</div>
                <div class="metric-label">平均轮次</div>
            </div>
        </div>
    `;

    // 如果有平均执行时长数据，显示执行时长而不是失败会话数
    if (summary.avg_duration !== undefined && summary.sessions_with_duration > 0) {
        const avgDurationMinutes = (summary.avg_duration / 60).toFixed(1);
        html += `
        <div class="col-md-3 mb-3">
            <div class="card metric-card bg-secondary text-white">
                <div class="metric-value">${avgDurationMinutes}分钟</div>
                <div class="metric-label">平均执行时长</div>
            </div>
        </div>
        `;
    } else {
        // 如果没有时长数据，显示失败会话数
        html += `
        <div class="col-md-3 mb-3">
            <div class="card metric-card bg-warning text-white">
                <div class="metric-value">${summary.failed_sessions}</div>
                <div class="metric-label">失败会话</div>
            </div>
        </div>
        `;
    }

    container.innerHTML = html;
}

// 更新图表
function updateCharts(data) {
    updateDailyTrendChart(data.daily_stats);
    updateStatusChart(data.summary);
    updateTaskTypeChart(data.task_types);
    updateDurationChart(data.duration_distribution);
}

// 更新每日趋势图表
function updateDailyTrendChart(dailyStats) {
    const ctx = document.getElementById('dailyTrendChart').getContext('2d');

    const dates = Object.keys(dailyStats).sort();
    const totalData = dates.map(date => dailyStats[date].total);
    const successData = dates.map(date => dailyStats[date].success);
    const failedData = dates.map(date => dailyStats[date].failed);

    if (charts.dailyTrend) {
        charts.dailyTrend.destroy();
    }

    charts.dailyTrend = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '总数',
                    data: totalData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: '成功',
                    data: successData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: '失败',
                    data: failedData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                }
            },
            plugins: {
                legend: { position: 'top' }
            }
        }
    });
}

// 更新状态分布图表
function updateStatusChart(summary) {
    const ctx = document.getElementById('statusChart').getContext('2d');

    if (charts.status) {
        charts.status.destroy();
    }

    charts.status = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['成功', '失败', '进行中'],
            datasets: [{
                data: [summary.success_sessions, summary.failed_sessions, summary.in_progress_sessions],
                backgroundColor: ['#28a745', '#dc3545', '#17a2b8'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'bottom' }
            }
        }
    });
}

// 更新任务类型图表
function updateTaskTypeChart(taskTypes) {
    const ctx = document.getElementById('taskTypeChart').getContext('2d');

    const labels = Object.keys(taskTypes);
    const successData = labels.map(type => taskTypes[type].success);
    const failedData = labels.map(type => taskTypes[type].failed);

    if (charts.taskType) {
        charts.taskType.destroy();
    }

    charts.taskType = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '成功',
                    data: successData,
                    backgroundColor: '#28a745',
                    borderColor: '#1e7e34',
                    borderWidth: 1
                },
                {
                    label: '失败',
                    data: failedData,
                    backgroundColor: '#dc3545',
                    borderColor: '#c82333',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: { stacked: true },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                }
            },
            plugins: {
                legend: { position: 'top' }
            }
        }
    });
}

// 更新执行时长分布图表
function updateDurationChart(durationDistribution) {
    const ctx = document.getElementById('durationChart').getContext('2d');

    // 使用真实的执行时长分布数据
    const durationRanges = Object.keys(durationDistribution);
    const durationData = Object.values(durationDistribution);

    if (charts.duration) {
        charts.duration.destroy();
    }

    charts.duration = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: durationRanges,
            datasets: [{
                label: '会话数量',
                data: durationData,
                backgroundColor: [
                    '#28a745',  // 0-30秒 - 绿色（快速）
                    '#17a2b8',  // 30秒-2分钟 - 青色（正常）
                    '#ffc107',  // 2-5分钟 - 黄色（较慢）
                    '#fd7e14',  // 5-10分钟 - 橙色（慢）
                    '#dc3545'   // 10分钟以上 - 红色（很慢）
                ],
                borderColor: [
                    '#1e7e34',
                    '#138496',
                    '#e0a800',
                    '#e55a00',
                    '#c82333'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                }
            },
            plugins: {
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = durationData.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((context.parsed.y / total) * 100).toFixed(1) : 0;
                            return `${context.parsed.y} 个会话 (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// 更新任务类型详细表格
function updateTaskTypeTable(taskTypes) {
    const container = document.getElementById('taskTypeTable');

    if (Object.keys(taskTypes).length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-table fa-3x mb-3"></i>
                <div>暂无数据</div>
            </div>
        `;
        return;
    }

    const tableRows = Object.entries(taskTypes).map(([type, stats]) => {
        const successRate = stats.total > 0 ? (stats.success / stats.total * 100).toFixed(1) : 0;
        const failureRate = stats.total > 0 ? (stats.failed / stats.total * 100).toFixed(1) : 0;

        return `
            <tr>
                <td>${type}</td>
                <td><span class="badge bg-primary">${stats.total}</span></td>
                <td><span class="badge bg-success">${stats.success}</span></td>
                <td><span class="badge bg-danger">${stats.failed}</span></td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: ${successRate}%">${successRate}%</div>
                    </div>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-danger" style="width: ${failureRate}%">${failureRate}%</div>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    const html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>任务类型</th>
                        <th>总数</th>
                        <th>成功</th>
                        <th>失败</th>
                        <th>成功率</th>
                        <th>失败率</th>
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

// 刷新统计数据
function refreshStats() {
    loadStatistics();
}

// 导出统计报告
function exportStats() {
    if (!currentStats) return;

    const report = {
        generated_at: new Date().toISOString(),
        date_range: currentStats.date_range,
        summary: currentStats.summary,
        task_types: currentStats.task_types,
        daily_stats: currentStats.daily_stats
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `conversation_stats_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    // 显示成功提示
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">统计报告已导出</div>
        </div>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
