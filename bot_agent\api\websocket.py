"""
WebSocket 模块 - 提供实时通信功能
"""

import asyncio
import json
import logging
from typing import Dict, List, Set

from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect, status

from bot_agent.api.auth import get_api_key

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(tags=["websocket"])

# 活跃连接管理
class ConnectionManager:
    """WebSocket 连接管理器"""
    
    def __init__(self):
        """初始化连接管理器"""
        # 所有活跃连接
        self.active_connections: List[WebSocket] = []
        # 按任务 ID 分组的连接
        self.task_connections: Dict[str, List[WebSocket]] = {}
        # 按用户 ID 分组的连接
        self.user_connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(
        self, 
        websocket: WebSocket, 
        task_id: str = None, 
        user_id: str = None
    ) -> None:
        """
        建立 WebSocket 连接

        Args:
            websocket: WebSocket 连接
            task_id: 任务 ID
            user_id: 用户 ID
        """
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # 存储连接的元数据
        websocket.scope["task_id"] = task_id
        websocket.scope["user_id"] = user_id
        
        # 按任务 ID 分组
        if task_id:
            if task_id not in self.task_connections:
                self.task_connections[task_id] = []
            self.task_connections[task_id].append(websocket)
        
        # 按用户 ID 分组
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(websocket)
        
        logger.info(f"WebSocket connected: task_id={task_id}, user_id={user_id}")
    
    def disconnect(self, websocket: WebSocket) -> None:
        """
        断开 WebSocket 连接

        Args:
            websocket: WebSocket 连接
        """
        # 从活跃连接中移除
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # 从任务连接中移除
        task_id = websocket.scope.get("task_id")
        if task_id and task_id in self.task_connections:
            if websocket in self.task_connections[task_id]:
                self.task_connections[task_id].remove(websocket)
            # 如果没有连接了，删除任务条目
            if not self.task_connections[task_id]:
                del self.task_connections[task_id]
        
        # 从用户连接中移除
        user_id = websocket.scope.get("user_id")
        if user_id and user_id in self.user_connections:
            if websocket in self.user_connections[user_id]:
                self.user_connections[user_id].remove(websocket)
            # 如果没有连接了，删除用户条目
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        logger.info(f"WebSocket disconnected: task_id={task_id}, user_id={user_id}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket) -> None:
        """
        发送个人消息

        Args:
            message: 消息内容
            websocket: WebSocket 连接
        """
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str) -> None:
        """
        广播消息给所有连接

        Args:
            message: 消息内容
        """
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected.append(connection)
        
        # 断开失败的连接
        for connection in disconnected:
            self.disconnect(connection)
    
    async def broadcast_to_task(self, message: str, task_id: str) -> None:
        """
        广播消息给特定任务的所有连接

        Args:
            message: 消息内容
            task_id: 任务 ID
        """
        if task_id not in self.task_connections:
            return
        
        disconnected = []
        for connection in self.task_connections[task_id]:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to task {task_id}: {e}")
                disconnected.append(connection)
        
        # 断开失败的连接
        for connection in disconnected:
            self.disconnect(connection)
    
    async def broadcast_to_user(self, message: str, user_id: str) -> None:
        """
        广播消息给特定用户的所有连接

        Args:
            message: 消息内容
            user_id: 用户 ID
        """
        if user_id not in self.user_connections:
            return
        
        disconnected = []
        for connection in self.user_connections[user_id]:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to user {user_id}: {e}")
                disconnected.append(connection)
        
        # 断开失败的连接
        for connection in disconnected:
            self.disconnect(connection)


# 创建连接管理器
manager = ConnectionManager()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    task_id: str = None,
    user_id: str = None,
    api_key: str = None,
) -> None:
    """
    WebSocket 端点

    Args:
        websocket: WebSocket 连接
        task_id: 任务 ID
        user_id: 用户 ID
        api_key: API 密钥
    """
    # 验证 API 密钥
    if api_key:
        try:
            # 使用与 HTTP 相同的验证逻辑
            from bot_agent.api.auth import get_api_key
            await get_api_key(websocket, api_key)
        except Exception as e:
            logger.warning(f"WebSocket authentication failed: {e}")
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
    
    # 建立连接
    await manager.connect(websocket, task_id, user_id)
    
    try:
        # 发送欢迎消息
        await manager.send_personal_message(
            json.dumps({
                "type": "welcome",
                "message": "Connected to Bot Agent WebSocket",
                "task_id": task_id,
                "user_id": user_id,
            }),
            websocket,
        )
        
        # 处理消息
        while True:
            data = await websocket.receive_text()
            
            try:
                # 解析消息
                message = json.loads(data)
                
                # 处理不同类型的消息
                if message.get("type") == "ping":
                    # 心跳消息
                    await manager.send_personal_message(
                        json.dumps({
                            "type": "pong",
                            "timestamp": message.get("timestamp"),
                        }),
                        websocket,
                    )
                elif message.get("type") == "subscribe":
                    # 订阅任务
                    new_task_id = message.get("task_id")
                    if new_task_id:
                        old_task_id = websocket.scope.get("task_id")
                        
                        # 从旧任务中移除
                        if old_task_id and old_task_id in manager.task_connections:
                            if websocket in manager.task_connections[old_task_id]:
                                manager.task_connections[old_task_id].remove(websocket)
                        
                        # 添加到新任务
                        if new_task_id not in manager.task_connections:
                            manager.task_connections[new_task_id] = []
                        manager.task_connections[new_task_id].append(websocket)
                        
                        # 更新连接元数据
                        websocket.scope["task_id"] = new_task_id
                        
                        await manager.send_personal_message(
                            json.dumps({
                                "type": "subscribed",
                                "task_id": new_task_id,
                            }),
                            websocket,
                        )
                else:
                    # 未知消息类型
                    logger.warning(f"Unknown message type: {message.get('type')}")
            
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON message: {data}")
            except Exception as e:
                logger.error(f"Error processing message: {e}")
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)


# 导出用于发送任务更新的函数
async def send_task_update(task_id: str, update: Dict) -> None:
    """
    发送任务更新

    Args:
        task_id: 任务 ID
        update: 更新内容
    """
    message = json.dumps({
        "type": "task_update",
        "task_id": task_id,
        "data": update,
    })
    
    await manager.broadcast_to_task(message, task_id)
