"""
任务路由器 - 将任务路由到相应的处理组件
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from bot_agent.dispatcher.task_analyzer import AITaskAnalyzer, TaskType

logger = logging.getLogger(__name__)


class TaskRouter:
    """
    任务路由器，负责将任务路由到相应的处理组件
    """

    def __init__(self):
        """初始化任务路由器"""
        self.task_analyzer = AITaskAnalyzer()
        logger.info("Task router initialized")

    async def route_task(
        self,
        title: str,
        description: str,
        source: str,
        source_id: str,
        labels: Optional[list] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        路由任务到相应的处理组件

        Args:
            title: 任务标题
            description: 任务描述
            source: 任务来源 (gitlab, dify, etc.)
            source_id: 来源系统中的任务 ID
            labels: 任务标签
            metadata: 额外的元数据

        Returns:
            Dict: 包含路由结果的字典
        """
        # 生成任务 ID
        task_id = str(uuid.uuid4())

        # 分析任务
        analysis = self.task_analyzer.analyze_task(title, description, labels)

        # 确定目标组件
        target_component = analysis["component"]

        # 创建任务对象
        task = {
            "id": task_id,
            "title": title,
            "description": description,
            "source": source,
            "source_id": source_id,
            "labels": labels or [],
            "created_at": datetime.utcnow().isoformat(),
            "status": "pending",
            "analysis": analysis,
            "target_component": target_component,
            "metadata": metadata or {},
        }

        # 记录任务信息
        logger.info(
            f"Task {task_id} routed to {target_component}: "
            f"{title} (type: {analysis['task_type']}, priority: {analysis['priority']})"
        )

        # 分发任务到目标组件
        result = await self._dispatch_to_component(task)

        return {
            "task_id": task_id,
            "target_component": target_component,
            "status": result["status"],
            "message": result["message"],
        }

    async def _dispatch_to_component(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        将任务分发到目标组件

        Args:
            task: 任务对象

        Returns:
            Dict: 分发结果
        """
        component = task["target_component"]

        try:
            # 导入 AI 处理器
            from bot_agent.handlers.ai_processor import AIProcessor

            # 创建 AI 处理器
            ai_processor = AIProcessor()

            # 处理任务并生成响应
            result = await ai_processor.process_task(task)

            logger.info(f"Task {task['id']} processed by AI processor: {result['status']}")

            return {
                "status": "accepted",
                "message": f"Task {task['id']} accepted and processed",
            }
        except Exception as e:
            logger.error(f"Error dispatching task to {component}: {e}", exc_info=True)
            return {
                "status": "error",
                "message": f"Error dispatching task: {str(e)}",
            }

    async def _dispatch_to_aider(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        将任务分发到 Aider

        Args:
            task: 任务对象

        Returns:
            Dict: 分发结果
        """
        # 这个方法现在由 AIProcessor 处理
        logger.info(f"Dispatching task {task['id']} to Aider")

        return {
            "status": "accepted",
            "message": f"Task {task['id']} accepted by Aider",
        }
