# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --output-file=requirements/common-constraints.txt requirements/requirements.in requirements/requirements-browser.in requirements/requirements-dev.in requirements/requirements-help.in requirements/requirements-playwright.in
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.16
    # via
    #   huggingface-hub
    #   litellm
    #   llama-index-core
aiosignal==1.3.2
    # via aiohttp
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
    #   watchfiles
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
backoff==2.2.1
    # via
    #   -r requirements/requirements.in
    #   posthog
banks==2.1.1
    # via llama-index-core
beautifulsoup4==4.13.3
    # via -r requirements/requirements.in
blinker==1.9.0
    # via streamlit
build==1.2.2.post1
    # via pip-tools
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   sounddevice
    #   soundfile
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   litellm
    #   nltk
    #   pip-tools
    #   streamlit
    #   typer
codespell==2.4.1
    # via -r requirements/requirements-dev.in
cogapp==3.4.1
    # via -r requirements/requirements-dev.in
colorama==0.4.6
    # via griffe
configargparse==1.7
    # via -r requirements/requirements.in
contourpy==1.3.1
    # via matplotlib
cycler==0.12.1
    # via matplotlib
dataclasses-json==0.6.7
    # via llama-index-core
deprecated==1.2.18
    # via
    #   banks
    #   llama-index-core
diff-match-patch==********
    # via -r requirements/requirements.in
dill==0.3.9
    # via
    #   multiprocess
    #   pathos
dirtyjson==1.0.8
    # via llama-index-core
diskcache==5.6.3
    # via -r requirements/requirements.in
distlib==0.3.9
    # via virtualenv
distro==1.9.0
    # via
    #   openai
    #   posthog
filelock==3.18.0
    # via
    #   huggingface-hub
    #   torch
    #   transformers
    #   virtualenv
filetype==1.2.0
    # via llama-index-core
flake8==7.2.0
    # via -r requirements/requirements.in
fonttools==4.57.0
    # via matplotlib
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via
    #   huggingface-hub
    #   llama-index-core
    #   torch
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   -r requirements/requirements.in
    #   streamlit
google-api-core[grpc]==2.24.2
    # via
    #   google-cloud-bigquery
    #   google-cloud-core
google-auth==2.38.0
    # via
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-core
google-cloud-bigquery==3.31.0
    # via -r requirements/requirements-dev.in
google-cloud-core==2.4.3
    # via google-cloud-bigquery
google-crc32c==1.7.1
    # via google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-bigquery
googleapis-common-protos==1.69.2
    # via
    #   google-api-core
    #   grpcio-status
greenlet==3.1.1
    # via
    #   playwright
    #   sqlalchemy
grep-ast==0.8.1
    # via -r requirements/requirements.in
griffe==1.7.2
    # via banks
grpcio==1.71.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via google-api-core
h11==0.14.0
    # via httpcore
httpcore==1.0.8
    # via httpx
httpx==0.28.1
    # via
    #   litellm
    #   llama-index-core
    #   openai
huggingface-hub[inference]==0.30.2
    # via
    #   llama-index-embeddings-huggingface
    #   sentence-transformers
    #   tokenizers
    #   transformers
identify==2.6.9
    # via pre-commit
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
imgcat==0.6.0
    # via -r requirements/requirements-dev.in
importlib-metadata==7.2.1
    # via
    #   -r requirements/requirements.in
    #   litellm
importlib-resources==6.5.2
    # via -r requirements/requirements.in
iniconfig==2.1.0
    # via pytest
jinja2==3.1.6
    # via
    #   altair
    #   banks
    #   litellm
    #   pydeck
    #   torch
jiter==0.9.0
    # via openai
joblib==1.4.2
    # via
    #   nltk
    #   scikit-learn
json5==0.12.0
    # via -r requirements/requirements.in
jsonschema==4.23.0
    # via
    #   -r requirements/requirements.in
    #   altair
    #   litellm
jsonschema-specifications==2024.10.1
    # via jsonschema
kiwisolver==1.4.8
    # via matplotlib
litellm==1.65.7
    # via -r requirements/requirements.in
llama-index-core==0.12.26
    # via
    #   -r requirements/requirements-help.in
    #   llama-index-embeddings-huggingface
llama-index-embeddings-huggingface==0.5.3
    # via -r requirements/requirements-help.in
lox==0.13.0
    # via -r requirements/requirements-dev.in
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
marshmallow==3.26.1
    # via dataclasses-json
matplotlib==3.10.1
    # via -r requirements/requirements-dev.in
mccabe==0.7.0
    # via flake8
mdurl==0.1.2
    # via markdown-it-py
mixpanel==4.10.1
    # via -r requirements/requirements.in
monotonic==1.6
    # via posthog
mpmath==1.3.0
    # via sympy
multidict==6.4.3
    # via
    #   aiohttp
    #   yarl
multiprocess==0.70.17
    # via pathos
mypy-extensions==1.0.0
    # via typing-inspect
narwhals==1.34.1
    # via altair
nest-asyncio==1.6.0
    # via llama-index-core
networkx==3.2.1
    # via
    #   -r requirements/requirements.in
    #   llama-index-core
    #   torch
nltk==3.9.1
    # via llama-index-core
nodeenv==1.9.1
    # via pre-commit
numpy==1.26.4
    # via
    #   -r requirements/requirements-help.in
    #   contourpy
    #   llama-index-core
    #   matplotlib
    #   pandas
    #   pydeck
    #   scikit-learn
    #   scipy
    #   soundfile
    #   streamlit
    #   transformers
openai==1.73.0
    # via litellm
packaging==24.2
    # via
    #   -r requirements/requirements.in
    #   altair
    #   build
    #   google-cloud-bigquery
    #   huggingface-hub
    #   marshmallow
    #   matplotlib
    #   pytest
    #   streamlit
    #   transformers
pandas==2.2.3
    # via
    #   -r requirements/requirements-dev.in
    #   streamlit
pathos==0.3.3
    # via lox
pathspec==0.12.1
    # via
    #   -r requirements/requirements.in
    #   grep-ast
pexpect==4.9.0
    # via -r requirements/requirements.in
pillow==11.1.0
    # via
    #   -r requirements/requirements.in
    #   llama-index-core
    #   matplotlib
    #   sentence-transformers
    #   streamlit
pip==25.0.1
    # via
    #   -r requirements/requirements.in
    #   pip-tools
pip-tools==7.4.1
    # via -r requirements/requirements-dev.in
platformdirs==4.3.7
    # via
    #   banks
    #   virtualenv
playwright==1.51.0
    # via -r requirements/requirements-playwright.in
pluggy==1.5.0
    # via pytest
posthog==3.24.1
    # via -r requirements/requirements.in
pox==0.3.5
    # via pathos
ppft==*******
    # via pathos
pre-commit==4.2.0
    # via -r requirements/requirements-dev.in
prompt-toolkit==3.0.50
    # via -r requirements/requirements.in
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via google-api-core
protobuf==5.29.4
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
    #   streamlit
psutil==7.0.0
    # via -r requirements/requirements.in
ptyprocess==0.7.0
    # via pexpect
pyarrow==19.0.1
    # via streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycodestyle==2.13.0
    # via flake8
pycparser==2.22
    # via cffi
pydantic==2.11.3
    # via
    #   banks
    #   litellm
    #   llama-index-core
    #   openai
pydantic-core==2.33.1
    # via pydantic
pydeck==0.9.1
    # via streamlit
pydub==0.25.1
    # via -r requirements/requirements.in
pyee==12.1.1
    # via playwright
pyflakes==3.3.2
    # via flake8
pygments==2.19.1
    # via rich
pypandoc==1.15
    # via -r requirements/requirements.in
pyparsing==3.2.3
    # via matplotlib
pyperclip==1.9.0
    # via -r requirements/requirements.in
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   -r requirements/requirements-dev.in
    #   pytest-env
pytest-env==1.1.5
    # via -r requirements/requirements-dev.in
python-dateutil==2.9.0.post0
    # via
    #   google-cloud-bigquery
    #   matplotlib
    #   pandas
    #   posthog
python-dotenv==1.1.0
    # via litellm
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   -r requirements/requirements.in
    #   huggingface-hub
    #   llama-index-core
    #   pre-commit
    #   transformers
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   nltk
    #   tiktoken
    #   transformers
requests==2.32.3
    # via
    #   google-api-core
    #   google-cloud-bigquery
    #   huggingface-hub
    #   llama-index-core
    #   mixpanel
    #   posthog
    #   streamlit
    #   tiktoken
    #   transformers
rich==14.0.0
    # via
    #   -r requirements/requirements.in
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
safetensors==0.5.3
    # via transformers
scikit-learn==1.6.1
    # via sentence-transformers
scipy==1.13.1
    # via
    #   -r requirements/requirements.in
    #   scikit-learn
    #   sentence-transformers
semver==3.0.4
    # via -r requirements/requirements-dev.in
sentence-transformers==4.0.2
    # via llama-index-embeddings-huggingface
setuptools==78.1.0
    # via pip-tools
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   mixpanel
    #   posthog
    #   python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anyio
    #   openai
socksio==1.0.0
    # via -r requirements/requirements.in
sounddevice==0.5.1
    # via -r requirements/requirements.in
soundfile==0.13.1
    # via -r requirements/requirements.in
soupsieve==2.6
    # via beautifulsoup4
sqlalchemy[asyncio]==2.0.40
    # via llama-index-core
streamlit==1.44.1
    # via -r requirements/requirements-browser.in
sympy==1.13.3
    # via torch
tenacity==9.1.2
    # via
    #   llama-index-core
    #   streamlit
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.9.0
    # via
    #   litellm
    #   llama-index-core
tokenizers==0.21.1
    # via
    #   litellm
    #   transformers
toml==0.10.2
    # via streamlit
torch==2.2.2
    # via
    #   -r requirements/requirements-help.in
    #   sentence-transformers
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via
    #   huggingface-hub
    #   llama-index-core
    #   nltk
    #   openai
    #   sentence-transformers
    #   transformers
transformers==4.51.2
    # via sentence-transformers
tree-sitter==0.24.0
    # via tree-sitter-language-pack
tree-sitter-c-sharp==0.23.1
    # via tree-sitter-language-pack
tree-sitter-embedded-template==0.23.2
    # via tree-sitter-language-pack
tree-sitter-language-pack==0.7.1
    # via grep-ast
tree-sitter-yaml==0.7.0
    # via tree-sitter-language-pack
typer==0.15.2
    # via -r requirements/requirements-dev.in
typing-extensions==4.13.2
    # via
    #   altair
    #   anyio
    #   beautifulsoup4
    #   huggingface-hub
    #   llama-index-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   pyee
    #   referencing
    #   sentence-transformers
    #   sqlalchemy
    #   streamlit
    #   torch
    #   typer
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via
    #   dataclasses-json
    #   llama-index-core
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via
    #   mixpanel
    #   requests
uv==0.6.14
    # via -r requirements/requirements-dev.in
virtualenv==20.30.0
    # via pre-commit
watchfiles==1.0.5
    # via -r requirements/requirements.in
wcwidth==0.2.13
    # via prompt-toolkit
wheel==0.45.1
    # via pip-tools
wrapt==1.17.2
    # via
    #   deprecated
    #   llama-index-core
yarl==1.19.0
    # via aiohttp
zipp==3.21.0
    # via importlib-metadata
