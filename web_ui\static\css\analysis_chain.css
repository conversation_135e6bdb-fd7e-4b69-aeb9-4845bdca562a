/* 分析链路样式 */

.analysis-chain {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    padding: 24px;
    margin: 20px 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chain-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #dee2e6;
}

.chain-header h3 {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
}

.chain-summary {
    display: flex;
    gap: 8px;
}

.chain-summary .badge {
    font-size: 0.85em;
    padding: 6px 12px;
}

/* 链路步骤 */
.chain-steps {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
}

.chain-step {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.chain-step:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.step-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.step-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2em;
    margin-right: 16px;
}

.step-title {
    flex: 1;
    font-size: 1.1em;
    font-weight: 600;
}

.step-title i {
    margin-right: 8px;
}

.step-toggle {
    transition: transform 0.3s ease;
}

.step-content {
    padding: 20px;
    display: none;
}

.step-content.active {
    display: block;
}

/* 内容区域样式 */
.content-box {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    border-left: 4px solid #007bff;
}

.meta-info {
    margin-top: 12px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.confidence-bar {
    margin-top: 12px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    transition: width 0.3s ease;
}

/* 关键词和标签 */
.keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.keyword-tag {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.85em;
    font-weight: 500;
}

.tool-tag {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 0.9em;
    margin: 2px;
    display: inline-block;
}

/* 风险列表 */
.risk-list {
    list-style: none;
    padding: 0;
}

.risk-list li {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 4px 0;
    position: relative;
    padding-left: 32px;
}

.risk-list li::before {
    content: '⚠️';
    position: absolute;
    left: 8px;
    top: 8px;
}

/* 时间线 */
.timeline {
    position: relative;
    padding-left: 30px;
    min-height: 50px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #28a745);
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.timeline-item.success .timeline-marker {
    background: #28a745;
}

.timeline-item.failed .timeline-marker {
    background: #dc3545;
}

.timeline-item.unknown .timeline-marker {
    background: #6c757d;
}

.timeline-content {
    background: white;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.timeline-time {
    font-size: 0.85em;
    color: #6c757d;
}

.timeline-duration {
    font-size: 0.8em;
    color: #007bff;
    font-weight: 500;
}

.timeline-empty {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-style: italic;
}

/* 执行步骤概览 */
.execution-summary {
    margin-top: 24px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
}

.steps-overview {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.step-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    font-weight: bold;
}

.step-item.success .step-number {
    background: #28a745;
}

.step-item.failed .step-number {
    background: #dc3545;
}

.step-item.unknown .step-number {
    background: #6c757d;
}

.step-name {
    flex: 1;
    font-weight: 500;
    color: #2c3e50;
}

.step-duration {
    font-size: 0.85em;
    color: #007bff;
    font-weight: 500;
    min-width: 50px;
    text-align: right;
}

.step-status-badge {
    font-size: 0.75em;
    padding: 2px 8px;
}

/* 挑战和解决方案 */
.challenges-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.challenge-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #ffc107;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.challenge-problem {
    font-weight: 600;
    color: #856404;
    margin-bottom: 8px;
}

.challenge-solution {
    color: #155724;
    font-style: italic;
}

/* 结果评估 */
.result-summary {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
}

.result-status {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 16px;
}

.result-status.success {
    color: #28a745;
}

.result-status.failed {
    color: #dc3545;
}

.result-status i {
    margin-right: 8px;
}

.result-metrics {
    display: flex;
    justify-content: space-around;
    margin-top: 16px;
}

.metric {
    text-align: center;
}

.metric-label {
    display: block;
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 4px;
}

.metric-value {
    display: block;
    font-size: 1.2em;
    font-weight: bold;
    color: #007bff;
}

/* 经验和成果 */
.lessons-learned {
    background: #e8f4fd;
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #007bff;
}

.lesson-item {
    margin-bottom: 8px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 123, 255, 0.1);
}

.lesson-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.achievements-list,
.improvements-list {
    list-style: none;
    padding: 0;
}

.achievements-list li {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 4px 0;
    position: relative;
    padding-left: 32px;
}

.achievements-list li::before {
    content: '✅';
    position: absolute;
    left: 8px;
    top: 8px;
}

.improvements-list li {
    background: #cce7ff;
    border: 1px solid #99d6ff;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 4px 0;
    position: relative;
    padding-left: 32px;
}

.improvements-list li::before {
    content: '💡';
    position: absolute;
    left: 8px;
    top: 8px;
}

/* 洞察面板 */
.insights-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-top: 24px;
}

.insights-panel h6 {
    color: white;
    margin-bottom: 16px;
    font-weight: 600;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.insight-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.insight-title {
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.insight-content {
    font-size: 1.1em;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chain-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .chain-summary {
        flex-wrap: wrap;
    }

    .step-header {
        padding: 12px 16px;
    }

    .step-number {
        width: 32px;
        height: 32px;
        font-size: 1em;
        margin-right: 12px;
    }

    .step-title {
        font-size: 1em;
    }

    .step-content {
        padding: 16px;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline-marker {
        left: -18px;
        width: 12px;
        height: 12px;
    }

    .result-metrics {
        flex-direction: column;
        gap: 12px;
    }

    .insights-grid {
        grid-template-columns: 1fr;
    }
}
