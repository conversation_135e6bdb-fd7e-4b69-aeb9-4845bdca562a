"""
API 路由 - 定义 API 端点
"""

import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status

from bot_agent.api.auth import get_api_key, get_current_user, require_permission
from bot_agent.api.app import consul_client  # 导入 app.py 中已创建的 consul_client
from bot_agent.dispatcher.router import TaskRouter
from bot_agent.models.task import TaskCreate, TaskResponse, TaskStatus

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api", tags=["api"])

# 创建任务路由器
task_router = TaskRouter()


@router.get("/tasks", response_model=List[TaskResponse])
async def list_tasks(
    status: Optional[TaskStatus] = None,
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    _: str = Depends(get_api_key),
) -> List[TaskResponse]:
    """
    获取任务列表

    Args:
        status: 任务状态过滤
        limit: 返回结果数量限制
        offset: 返回结果偏移量
        _: API 密钥依赖

    Returns:
        List[TaskResponse]: 任务列表
    """
    # TODO: 实现从数据库获取任务列表
    # 这里是一个模拟实现
    tasks = [
        TaskResponse(
            id="task-1",
            title="示例任务 1",
            status=TaskStatus.PENDING,
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z",
        ),
        TaskResponse(
            id="task-2",
            title="示例任务 2",
            status=TaskStatus.IN_PROGRESS,
            created_at="2023-01-02T00:00:00Z",
            updated_at="2023-01-02T00:00:00Z",
        ),
        TaskResponse(
            id="task-3",
            title="示例任务 3",
            status=TaskStatus.COMPLETED,
            created_at="2023-01-03T00:00:00Z",
            updated_at="2023-01-03T00:00:00Z",
        ),
    ]

    # 应用状态过滤
    if status:
        tasks = [task for task in tasks if task.status == status]

    # 应用分页
    tasks = tasks[offset:offset + limit]

    return tasks


@router.post("/tasks", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    task: TaskCreate,
    _: str = Depends(get_api_key),
) -> TaskResponse:
    """
    创建新任务

    Args:
        task: 任务创建请求
        _: API 密钥依赖

    Returns:
        TaskResponse: 创建的任务
    """
    try:
        # 路由任务
        result = await task_router.route_task(
            title=task.title,
            description=task.description,
            source=task.source,
            source_id=task.source_id,
            labels=task.labels,
            metadata=task.metadata,
        )

        # 构建响应
        response = TaskResponse(
            id=result["task_id"],
            title=task.title,
            description=task.description,
            status=TaskStatus.PENDING,
            source=task.source,
            source_id=task.source_id,
            labels=task.labels,
            target_component=result["target_component"],
            created_at="2023-01-01T00:00:00Z",  # 应该使用实际时间
            updated_at="2023-01-01T00:00:00Z",  # 应该使用实际时间
        )

        return response

    except Exception as e:
        logger.error(f"Error creating task: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating task: {str(e)}",
        )


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    _: str = Depends(get_api_key),
) -> TaskResponse:
    """
    获取任务详情

    Args:
        task_id: 任务 ID
        _: API 密钥依赖

    Returns:
        TaskResponse: 任务详情
    """
    # TODO: 实现从数据库获取任务详情
    # 这里是一个模拟实现
    if task_id == "task-1":
        return TaskResponse(
            id="task-1",
            title="示例任务 1",
            description="这是一个示例任务",
            status=TaskStatus.PENDING,
            source="gitlab",
            source_id="123",
            labels=["bug", "high"],
            target_component="aider",
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z",
        )

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Task {task_id} not found",
    )


@router.put("/tasks/{task_id}/status", response_model=TaskResponse)
async def update_task_status(
    task_id: str,
    status: TaskStatus,
    user: Dict = Depends(require_permission("update:task")),
) -> TaskResponse:
    """
    更新任务状态

    Args:
        task_id: 任务 ID
        status: 新状态
        user: 当前用户（具有更新任务权限）

    Returns:
        TaskResponse: 更新后的任务
    """
    # TODO: 实现更新任务状态
    # 这里是一个模拟实现
    if task_id == "task-1":
        return TaskResponse(
            id="task-1",
            title="示例任务 1",
            description="这是一个示例任务",
            status=status,
            source="gitlab",
            source_id="123",
            labels=["bug", "high"],
            target_component="aider",
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z",
        )

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Task {task_id} not found",
    )


@router.get("/services", response_model=List[Dict])
async def list_services(
    service_name: Optional[str] = None,
    _: str = Depends(get_api_key),
) -> List[Dict]:
    """
    获取服务列表

    Args:
        service_name: 服务名称过滤
        _: API 密钥依赖

    Returns:
        List[Dict]: 服务列表
    """
    try:
        if service_name:
            # 获取指定服务的实例
            services = consul_client.discover_service(service_name)
        else:
            # 获取所有服务
            # 注意：这是一个简化实现，实际上需要从 Consul 获取所有服务
            services = []
            for name in ["bot-agent", "aider"]:
                services.extend(consul_client.discover_service(name))

        return services

    except Exception as e:
        logger.error(f"Error listing services: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing services: {str(e)}",
        )


@router.get("/services/{service_name}/health", response_model=Dict)
async def get_service_health(
    service_name: str,
    _: str = Depends(get_api_key),
) -> Dict:
    """
    获取服务健康状态

    Args:
        service_name: 服务名称
        _: API 密钥依赖

    Returns:
        Dict: 服务健康状态
    """
    try:
        health = consul_client.get_service_health(service_name)
        return health

    except Exception as e:
        logger.error(f"Error getting service health: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting service health: {str(e)}",
        )


@router.post("/auth/token", response_model=Dict)
async def create_token(
    username: str,
    password: str,
) -> Dict:
    """
    创建认证令牌

    Args:
        username: 用户名
        password: 密码

    Returns:
        Dict: 包含令牌的响应
    """
    # TODO: 实现用户认证
    # 这里是一个模拟实现
    if username == "admin" and password == "password":
        # 导入这里以避免循环导入
        from bot_agent.api.auth import create_access_token

        # 创建令牌
        token = create_access_token(
            data={
                "sub": username,
                "permissions": ["read:task", "create:task", "update:task"],
            }
        )

        return {
            "access_token": token,
            "token_type": "bearer",
        }

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid credentials",
    )
