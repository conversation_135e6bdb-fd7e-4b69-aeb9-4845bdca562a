"""
简单进度跟踪系统 - 跟踪任务执行进度并实时更新到GitLab
"""

import logging
from datetime import datetime
from typing import Dict
from enum import Enum

from bot_agent.clients.gitlab_client import GitLabClient

logger = logging.getLogger(__name__)


class ProgressStatus(Enum):
    """进度状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class SimpleProgressTracker:
    """
    简单的进度跟踪器，负责跟踪任务执行进度并实时更新到GitLab
    """
    
    def __init__(self):
        """初始化进度跟踪器"""
        self.gitlab_client = GitLabClient()
        logger.info("SimpleProgressTracker initialized")
    
    async def send_progress_update(self, task: Dict, status: ProgressStatus, 
                                 message: str, details: str = "") -> None:
        """
        发送进度更新到GitLab
        
        Args:
            task: 任务对象
            status: 进度状态
            message: 进度消息
            details: 详细信息
        """
        try:
            metadata = task.get("metadata", {})
            project_id = metadata.get("project_id")
            issue_iid = metadata.get("issue_iid")
            
            if not project_id or not issue_iid:
                logger.warning("缺少项目ID或Issue IID，跳过进度更新")
                return
            
            # 构建进度更新消息
            status_emoji = {
                ProgressStatus.PENDING: "⏳",
                ProgressStatus.RUNNING: "🔄",
                ProgressStatus.COMPLETED: "✅",
                ProgressStatus.FAILED: "❌"
            }
            
            emoji = status_emoji.get(status, "❓")
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            comment_body = f"""🤖 **任务进度更新**

{emoji} **状态**: {status.value}
⏰ **时间**: {timestamp}
📝 **消息**: {message}

{details}
"""
            
            # 发送评论
            self.gitlab_client.create_issue_comment(
                project_id=project_id,
                issue_iid=issue_iid,
                body=comment_body
            )
            
            logger.info(f"进度更新已发送到GitLab Issue {project_id}:{issue_iid}")
            
        except Exception as e:
            logger.error(f"发送进度更新失败: {e}")
    
    async def send_start_notification(self, task: Dict) -> None:
        """
        发送任务开始通知
        
        Args:
            task: 任务对象
        """
        title = task.get("title", "Unknown")
        task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")
        
        message = f"开始处理任务: {title}"
        details = f"""
**任务详情**:
- 类型: {task_type}
- 处理方式: 基于Aider的AI代码助手
- 预计功能: 代码分析、修改、测试

正在准备工作环境...
"""
        
        await self.send_progress_update(task, ProgressStatus.RUNNING, message, details)
    
    async def send_completion_notification(self, task: Dict, result: Dict) -> None:
        """
        发送任务完成通知
        
        Args:
            task: 任务对象
            result: 执行结果
        """
        title = task.get("title", "Unknown")
        
        if result["status"] == "success":
            message = f"任务完成: {title}"
            details = f"""
**执行结果**:
- 状态: 成功 ✅
- 处理方式: Aider AI
- 项目路径: {result.get('project_path', 'N/A')}

{result.get('report', '任务已完成')}
"""
            status = ProgressStatus.COMPLETED
        else:
            message = f"任务失败: {title}"
            details = f"""
**执行结果**:
- 状态: 失败 ❌
- 错误信息: {result.get('error', '未知错误')}

{result.get('report', '任务执行失败')}
"""
            status = ProgressStatus.FAILED
        
        await self.send_progress_update(task, status, message, details)


# 创建全局进度跟踪器实例
progress_tracker = SimpleProgressTracker()
