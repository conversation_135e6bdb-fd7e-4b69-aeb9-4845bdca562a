{% extends "base.html" %}

{% block title %}实时监控 - Aider行为分析系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="fas fa-chart-line text-primary"></i>
        Aider实时监控
    </h1>
    <p class="text-muted mb-0">AI编程助手行为分析和性能监控</p>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" id="statsCards">
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="metric-value" id="totalSessions">-</div>
            <div class="metric-label">执行任务</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="metric-value" id="successRate">-</div>
            <div class="metric-label">成功率</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
            <div class="metric-value" id="avgRounds">-</div>
            <div class="metric-label">平均交互</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="metric-value" id="totalRounds">-</div>
            <div class="metric-label">总交互数</div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    Aider执行趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyChart" height="150"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart"></i>
                    任务类型分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="taskTypeChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最近会话 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i>
                    最近执行记录
                </h5>
                <a href="/sessions" class="btn btn-sm btn-outline-primary">
                    查看全部 <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                <div id="recentSessions">
                    <div class="loading">
                        <div class="spinner"></div>
                        <div>加载最近会话...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let dailyChart = null;
let taskTypeChart = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

// 加载仪表板数据
async function loadDashboardData() {
    try {
        // 加载统计数据
        const statsResponse = await fetch('/api/statistics?days=7');
        const statsData = await statsResponse.json();

        if (statsData.success) {
            updateStatsCards(statsData.data);
            updateCharts(statsData.data);
        }

        // 加载最近会话
        const sessionsResponse = await fetch('/api/sessions?limit=10');
        const sessionsData = await sessionsResponse.json();

        if (sessionsData.success) {
            updateRecentSessions(sessionsData.data);
        }

    } catch (error) {
        console.error('加载仪表板数据失败:', error);
        showError('recentSessions', '加载数据失败，请稍后重试');
    }
}

// 更新统计卡片
function updateStatsCards(data) {
    const summary = data.summary;

    document.getElementById('totalSessions').textContent = summary.total_sessions;
    document.getElementById('successRate').textContent = `${summary.success_rate.toFixed(1)}%`;
    document.getElementById('avgRounds').textContent = summary.avg_rounds;
    document.getElementById('totalRounds').textContent = summary.total_rounds;
}

// 更新图表
function updateCharts(data) {
    updateDailyChart(data.daily_stats);
    updateTaskTypeChart(data.task_types);
}

// 更新每日趋势图表
function updateDailyChart(dailyStats) {
    const ctx = document.getElementById('dailyChart').getContext('2d');

    // 准备数据
    const dates = Object.keys(dailyStats).sort();
    const totalData = dates.map(date => dailyStats[date].total);
    const successData = dates.map(date => dailyStats[date].success);
    const failedData = dates.map(date => dailyStats[date].failed);

    if (dailyChart) {
        dailyChart.destroy();
    }

    dailyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '总数',
                    data: totalData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                },
                {
                    label: '成功',
                    data: successData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: '失败',
                    data: failedData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// 更新任务类型饼图
function updateTaskTypeChart(taskTypes) {
    const ctx = document.getElementById('taskTypeChart').getContext('2d');

    // 准备数据
    const labels = Object.keys(taskTypes);
    const data = labels.map(type => taskTypes[type].total);
    const colors = [
        '#007bff', '#28a745', '#dc3545', '#ffc107',
        '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14'
    ];

    if (taskTypeChart) {
        taskTypeChart.destroy();
    }

    taskTypeChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 更新最近会话列表
function updateRecentSessions(sessions) {
    const container = document.getElementById('recentSessions');

    if (sessions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <div>暂无会话记录</div>
            </div>
        `;
        return;
    }

    const html = sessions.map(session => `
        <div class="session-item p-3 border-bottom" onclick="viewSession('${session.session_id}')">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${session.task_title}</h6>
                    <p class="mb-1 text-muted small">${session.task_type}</p>
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        ${formatTime(session.started_at)}
                        <span class="ms-2">
                            <i class="fas fa-comments"></i>
                            ${session.rounds_count} 轮
                        </span>
                    </small>
                </div>
                <div class="text-end">
                    ${getStatusBadge(session.status)}
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// 查看会话详情
function viewSession(sessionId) {
    window.location.href = `/sessions/${sessionId}`;
}

// 刷新数据
function refreshData() {
    showLoading('recentSessions');
    loadDashboardData();
}
</script>
{% endblock %}
