"""
API 文档 - 提供 API 文档页面
"""

import os
from pathlib import Path

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

# 创建路由器
router = APIRouter(tags=["docs"])

# 创建模板引擎
templates_dir = Path(__file__).parent / "templates"
os.makedirs(templates_dir, exist_ok=True)
templates = Jinja2Templates(directory=str(templates_dir))

# 创建 API 文档 HTML 模板
api_docs_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Bot Agent API 文档</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #0066cc;
        }
        .endpoint {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .method {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background-color: #61affe; }
        .post { background-color: #49cc90; }
        .put { background-color: #fca130; }
        .delete { background-color: #f93e3e; }
        .path {
            font-family: monospace;
            font-size: 1.1em;
        }
        .description {
            margin: 10px 0;
        }
        .params {
            margin-top: 15px;
        }
        .param {
            margin-bottom: 5px;
        }
        .param-name {
            font-weight: bold;
            font-family: monospace;
        }
        .param-type {
            color: #6c757d;
            font-style: italic;
        }
        .param-required {
            color: #e91e63;
        }
        .response {
            margin-top: 15px;
        }
        .response-code {
            font-weight: bold;
            color: #0066cc;
        }
        pre {
            background-color: #272822;
            color: #f8f8f2;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .websocket {
            background-color: #9012fe;
        }
        .nav {
            position: sticky;
            top: 0;
            background-color: white;
            padding: 10px 0;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .nav ul {
            list-style-type: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
        }
        .nav li {
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .nav a {
            text-decoration: none;
            color: #0066cc;
            font-weight: bold;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>Bot Agent API 文档</h1>
    <p>Bot Agent API 提供了与 Bot 代理服务交互的接口，包括任务管理、服务发现和实时通信功能。</p>
    
    <div class="nav">
        <ul>
            <li><a href="#tasks">任务管理</a></li>
            <li><a href="#services">服务发现</a></li>
            <li><a href="#auth">认证</a></li>
            <li><a href="#websocket">WebSocket</a></li>
            <li><a href="#webhook">Webhook</a></li>
        </ul>
    </div>
    
    <h2 id="tasks">任务管理</h2>
    
    <div class="endpoint">
        <span class="method get">GET</span>
        <span class="path">/api/tasks</span>
        <div class="description">获取任务列表</div>
        <div class="params">
            <div class="param">
                <span class="param-name">status</span>
                <span class="param-type">(string, optional)</span>
                - 任务状态过滤
            </div>
            <div class="param">
                <span class="param-name">limit</span>
                <span class="param-type">(integer, default: 10)</span>
                - 返回结果数量限制
            </div>
            <div class="param">
                <span class="param-name">offset</span>
                <span class="param-type">(integer, default: 0)</span>
                - 返回结果偏移量
            </div>
        </div>
        <div class="response">
            <div><span class="response-code">200</span> - 成功</div>
            <pre>
[
  {
    "id": "task-1",
    "title": "示例任务 1",
    "status": "pending",
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  },
  ...
]</pre>
        </div>
    </div>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="path">/api/tasks</span>
        <div class="description">创建新任务</div>
        <div class="params">
            <div class="param">
                <span class="param-name">title</span>
                <span class="param-type">(string)</span>
                <span class="param-required">required</span>
                - 任务标题
            </div>
            <div class="param">
                <span class="param-name">description</span>
                <span class="param-type">(string)</span>
                <span class="param-required">required</span>
                - 任务描述
            </div>
            <div class="param">
                <span class="param-name">source</span>
                <span class="param-type">(string)</span>
                <span class="param-required">required</span>
                - 任务来源
            </div>
            <div class="param">
                <span class="param-name">source_id</span>
                <span class="param-type">(string)</span>
                <span class="param-required">required</span>
                - 来源系统中的任务 ID
            </div>
            <div class="param">
                <span class="param-name">labels</span>
                <span class="param-type">(array, optional)</span>
                - 任务标签
            </div>
            <div class="param">
                <span class="param-name">metadata</span>
                <span class="param-type">(object, optional)</span>
                - 额外的元数据
            </div>
        </div>
        <div class="response">
            <div><span class="response-code">201</span> - 创建成功</div>
            <pre>
{
  "id": "task-123",
  "title": "示例任务",
  "description": "这是一个示例任务",
  "status": "pending",
  "source": "gitlab",
  "source_id": "123",
  "labels": ["bug", "high"],
  "target_component": "aider",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}</pre>
        </div>
    </div>
    
    <h2 id="websocket">WebSocket</h2>
    
    <div class="endpoint">
        <span class="method websocket">WebSocket</span>
        <span class="path">/ws</span>
        <div class="description">WebSocket 连接，用于实时通信</div>
        <div class="params">
            <div class="param">
                <span class="param-name">task_id</span>
                <span class="param-type">(string, optional)</span>
                - 任务 ID
            </div>
            <div class="param">
                <span class="param-name">user_id</span>
                <span class="param-type">(string, optional)</span>
                - 用户 ID
            </div>
            <div class="param">
                <span class="param-name">api_key</span>
                <span class="param-type">(string, optional)</span>
                - API 密钥
            </div>
        </div>
        <div class="description">
            <p>WebSocket 消息格式：</p>
            <pre>
// 欢迎消息
{
  "type": "welcome",
  "message": "Connected to Bot Agent WebSocket",
  "task_id": "task-123",
  "user_id": "user-456"
}

// 任务更新
{
  "type": "task_update",
  "task_id": "task-123",
  "data": {
    "status": "in_progress",
    "progress": 50,
    "message": "正在生成代码..."
  }
}

// 心跳请求
{
  "type": "ping",
  "timestamp": 1625097600000
}

// 心跳响应
{
  "type": "pong",
  "timestamp": 1625097600000
}</pre>
        </div>
    </div>
    
    <h2 id="webhook">Webhook</h2>
    
    <div class="endpoint">
        <span class="method post">POST</span>
        <span class="path">/webhook/gitlab</span>
        <div class="description">GitLab Webhook 端点，接收 GitLab 事件</div>
        <div class="params">
            <div class="param">
                <span class="param-name">X-Gitlab-Event</span>
                <span class="param-type">(header)</span>
                <span class="param-required">required</span>
                - GitLab 事件类型
            </div>
            <div class="param">
                <span class="param-name">X-Gitlab-Token</span>
                <span class="param-type">(header)</span>
                <span class="param-required">required</span>
                - GitLab Webhook 令牌
            </div>
        </div>
        <div class="response">
            <div><span class="response-code">200</span> - 成功</div>
            <pre>
{
  "status": "success",
  "message": "Webhook processed",
  "task_id": "task-123"
}</pre>
        </div>
    </div>
    
    <footer>
        <p>© 2023 Bot Agent API. 版本 0.1.0</p>
    </footer>
</body>
</html>
"""

# 保存模板文件
with open(templates_dir / "api_docs.html", "w", encoding="utf-8") as f:
    f.write(api_docs_template)


@router.get("/docs/api", response_class=HTMLResponse, include_in_schema=False)
async def api_docs(request: Request):
    """
    API 文档页面

    Args:
        request: FastAPI 请求对象

    Returns:
        HTMLResponse: API 文档 HTML 页面
    """
    return templates.TemplateResponse("api_docs.html", {"request": request})
