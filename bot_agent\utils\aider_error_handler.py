"""
Aider错误处理器 - 处理Aider执行过程中的各种错误
"""

import logging
import os
import subprocess
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

logger = logging.getLogger(__name__)


class AiderErrorHandler:
    """
    Aider错误处理器

    功能：
    1. 检测和分类Aider错误
    2. 自动恢复Git状态
    3. 重试机制
    4. 错误报告和建议
    """

    def __init__(self, project_path: str = None):
        """初始化错误处理器"""
        self.project_path = project_path or os.getcwd()
        self.error_history: List[Dict[str, Any]] = []

        logger.info(f"AiderErrorHandler initialized for {self.project_path}")

    def analyze_error(self, error_message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        分析错误类型和原因

        Args:
            error_message: 错误信息
            context: 上下文信息

        Returns:
            Dict: 错误分析结果
        """
        error_info = {
            "timestamp": time.time(),
            "message": error_message,
            "context": context or {},
            "error_type": "unknown",
            "severity": "medium",
            "recoverable": True,
            "suggestions": []
        }

        error_lower = error_message.lower()

        # Git相关错误
        if ("'nonetype' object is not iterable" in error_lower or
            "unable to commit" in error_lower or
            "git commit" in error_lower):
            error_info.update({
                "error_type": "git_commit_failure",
                "severity": "high",
                "recoverable": True,
                "suggestions": [
                    "检查Git仓库状态",
                    "重置Git索引",
                    "手动提交更改",
                    "重新初始化Aider"
                ]
            })

        # Token相关错误
        elif "empty response" in error_lower or "token" in error_lower:
            error_info.update({
                "error_type": "llm_response_failure",
                "severity": "medium",
                "recoverable": True,
                "suggestions": [
                    "检查API配置",
                    "重试请求",
                    "减少上下文长度",
                    "切换模型"
                ]
            })

        # 文件权限错误
        elif "permission denied" in error_lower:
            error_info.update({
                "error_type": "permission_error",
                "severity": "high",
                "recoverable": True,
                "suggestions": [
                    "检查文件权限",
                    "以管理员身份运行",
                    "检查文件是否被占用"
                ]
            })

        # 网络错误
        elif "connection" in error_lower or "timeout" in error_lower:
            error_info.update({
                "error_type": "network_error",
                "severity": "medium",
                "recoverable": True,
                "suggestions": [
                    "检查网络连接",
                    "重试请求",
                    "检查代理设置"
                ]
            })

        self.error_history.append(error_info)
        return error_info

    def recover_git_state(self) -> Tuple[bool, str]:
        """
        恢复Git状态

        Returns:
            Tuple[bool, str]: (是否成功, 操作信息)
        """
        try:
            os.chdir(self.project_path)

            # 检查Git状态
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                return False, f"Git状态检查失败: {result.stderr}"

            # 如果有未提交的更改
            if result.stdout.strip():
                logger.info("发现未提交的更改，尝试提交...")

                # 添加所有更改
                add_result = subprocess.run(
                    ["git", "add", "."],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if add_result.returncode != 0:
                    return False, f"Git add失败: {add_result.stderr}"

                # 提交更改
                commit_result = subprocess.run(
                    ["git", "commit", "-m", "Auto-commit: Aider error recovery"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if commit_result.returncode != 0:
                    # 如果提交失败，尝试重置
                    logger.warning("提交失败，尝试重置...")
                    reset_result = subprocess.run(
                        ["git", "reset", "--hard", "HEAD"],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )

                    if reset_result.returncode == 0:
                        return True, "Git状态已重置到HEAD"
                    else:
                        return False, f"Git重置失败: {reset_result.stderr}"
                else:
                    return True, "未提交的更改已自动提交"
            else:
                return True, "Git状态正常，无需恢复"

        except subprocess.TimeoutExpired:
            return False, "Git操作超时"
        except Exception as e:
            return False, f"Git恢复失败: {str(e)}"

    def check_aider_environment(self) -> Dict[str, Any]:
        """
        检查Aider运行环境

        Returns:
            Dict: 环境检查结果
        """
        checks = {
            "git_available": False,
            "git_repo": False,
            "aider_installed": False,
            "python_version": None,
            "project_path_exists": False,
            "write_permissions": False
        }

        try:
            # 检查Git
            git_result = subprocess.run(
                ["git", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            checks["git_available"] = git_result.returncode == 0

            # 检查Git仓库
            if checks["git_available"]:
                os.chdir(self.project_path)
                repo_result = subprocess.run(
                    ["git", "rev-parse", "--git-dir"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                checks["git_repo"] = repo_result.returncode == 0

            # 检查Aider
            try:
                import aider
                checks["aider_installed"] = True
            except ImportError:
                checks["aider_installed"] = False

            # 检查Python版本
            import sys
            checks["python_version"] = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"

            # 检查项目路径
            checks["project_path_exists"] = os.path.exists(self.project_path)

            # 检查写权限
            if checks["project_path_exists"]:
                test_file = os.path.join(self.project_path, ".aider_test_write")
                try:
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    checks["write_permissions"] = True
                except:
                    checks["write_permissions"] = False

        except Exception as e:
            logger.error(f"环境检查失败: {e}")

        return checks

    def generate_recovery_plan(self, error_info: Dict[str, Any]) -> List[str]:
        """
        生成恢复计划

        Args:
            error_info: 错误信息

        Returns:
            List[str]: 恢复步骤
        """
        error_type = error_info.get("error_type", "unknown")

        if error_type == "git_commit_failure":
            return [
                "1. 检查Git仓库状态",
                "2. 备份当前更改",
                "3. 重置Git索引",
                "4. 重新应用更改",
                "5. 手动提交",
                "6. 重新启动Aider"
            ]

        elif error_type == "llm_response_failure":
            return [
                "1. 检查API配置和密钥",
                "2. 验证网络连接",
                "3. 减少上下文长度",
                "4. 重试请求",
                "5. 考虑切换模型"
            ]

        elif error_type == "permission_error":
            return [
                "1. 检查文件和目录权限",
                "2. 确保没有文件被其他程序占用",
                "3. 以管理员权限运行",
                "4. 检查防病毒软件设置"
            ]

        elif error_type == "network_error":
            return [
                "1. 检查网络连接",
                "2. 验证代理设置",
                "3. 检查防火墙配置",
                "4. 重试连接"
            ]

        else:
            return [
                "1. 检查错误日志",
                "2. 验证环境配置",
                "3. 重启应用程序",
                "4. 联系技术支持"
            ]

    def auto_recover(self, error_message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        自动恢复

        Args:
            error_message: 错误信息
            context: 上下文信息

        Returns:
            Dict: 恢复结果
        """
        logger.info(f"开始自动恢复: {error_message}")

        # 分析错误
        error_info = self.analyze_error(error_message, context)

        recovery_result = {
            "success": False,
            "error_info": error_info,
            "actions_taken": [],
            "recommendations": []
        }

        # 根据错误类型执行恢复操作
        if error_info["error_type"] == "git_commit_failure":
            # Git恢复
            git_success, git_message = self.recover_git_state()
            recovery_result["actions_taken"].append(f"Git恢复: {git_message}")

            if git_success:
                recovery_result["success"] = True
                recovery_result["recommendations"].append("Git状态已恢复，可以重新尝试操作")
            else:
                recovery_result["recommendations"].extend([
                    "手动检查Git状态",
                    "备份重要更改",
                    "考虑重新克隆仓库"
                ])

        # 环境检查
        env_checks = self.check_aider_environment()
        recovery_result["environment_checks"] = env_checks

        # 生成恢复计划
        recovery_plan = self.generate_recovery_plan(error_info)
        recovery_result["recovery_plan"] = recovery_plan

        logger.info(f"自动恢复完成，成功: {recovery_result['success']}")
        return recovery_result

    def generate_error_report(self) -> str:
        """生成错误报告"""
        if not self.error_history:
            return "无错误记录"

        report = "# Aider错误报告\n\n"

        # 统计信息
        total_errors = len(self.error_history)
        error_types = {}
        for error in self.error_history:
            error_type = error.get("error_type", "unknown")
            error_types[error_type] = error_types.get(error_type, 0) + 1

        report += f"## 📊 统计信息\n"
        report += f"- 总错误数: {total_errors}\n"
        report += f"- 错误类型分布:\n"
        for error_type, count in error_types.items():
            report += f"  - {error_type}: {count}\n"
        report += "\n"

        # 最近的错误
        report += f"## 🚨 最近的错误\n"
        for i, error in enumerate(self.error_history[-5:], 1):
            report += f"### 错误 {i}\n"
            report += f"- **类型**: {error.get('error_type')}\n"
            report += f"- **严重程度**: {error.get('severity')}\n"
            report += f"- **时间**: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(error.get('timestamp')))}\n"
            report += f"- **消息**: {error.get('message')}\n"
            report += f"- **建议**: {', '.join(error.get('suggestions', []))}\n\n"

        return report


# 全局错误处理器实例
global_aider_error_handler = AiderErrorHandler()
