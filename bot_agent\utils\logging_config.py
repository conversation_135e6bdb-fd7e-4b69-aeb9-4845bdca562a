"""
日志配置模块 - 确保所有错误都被正确记录和监控
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path


class EnhancedLogger:
    """增强的日志记录器，确保所有错误都被捕获"""
    
    def __init__(self, name: str = "bot_agent"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.setup_logging()
    
    def setup_logging(self):
        """设置完整的日志配置"""
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 设置日志级别为DEBUG，确保所有信息都被记录
        self.logger.setLevel(logging.DEBUG)
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 创建格式化器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # 1. 控制台处理器 - 显示INFO及以上级别
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        self.logger.addHandler(console_handler)
        
        # 2. 应用程序日志文件 - 记录所有级别
        app_log_file = log_dir / "app.log"
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        app_handler.setLevel(logging.DEBUG)
        app_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(app_handler)
        
        # 3. 错误日志文件 - 只记录ERROR及以上级别
        error_log_file = log_dir / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(error_handler)
        
        # 4. 每日日志文件 - 按日期分割
        daily_log_file = log_dir / f"daily_{datetime.now().strftime('%Y%m%d')}.log"
        daily_handler = logging.handlers.TimedRotatingFileHandler(
            daily_log_file,
            when='midnight',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        daily_handler.setLevel(logging.INFO)
        daily_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(daily_handler)
        
        # 5. 任务执行日志 - 专门记录任务相关的日志
        task_log_file = log_dir / "task_execution.log"
        task_handler = logging.handlers.RotatingFileHandler(
            task_log_file,
            maxBytes=20*1024*1024,  # 20MB
            backupCount=3,
            encoding='utf-8'
        )
        task_handler.setLevel(logging.INFO)
        task_handler.setFormatter(detailed_formatter)
        
        # 为任务相关的日志器添加特殊处理器
        task_logger = logging.getLogger("bot_agent.engines.task_executor")
        task_logger.addHandler(task_handler)
        
        analyzer_logger = logging.getLogger("bot_agent.handlers.job_failure_analyzer")
        analyzer_logger.addHandler(task_handler)
        
        terminal_logger = logging.getLogger("bot_agent.tools.terminal_tools")
        terminal_logger.addHandler(task_handler)
        
        # 设置根日志器，捕获所有未处理的日志
        root_logger = logging.getLogger()
        if not root_logger.handlers:
            root_logger.setLevel(logging.WARNING)
            root_handler = logging.handlers.RotatingFileHandler(
                log_dir / "root.log",
                maxBytes=5*1024*1024,
                backupCount=3,
                encoding='utf-8'
            )
            root_handler.setFormatter(detailed_formatter)
            root_logger.addHandler(root_handler)
        
        self.logger.info(f"日志系统初始化完成 - {self.name}")
        self.logger.info(f"日志目录: {log_dir.absolute()}")
        self.logger.info(f"日志文件: app.log, error.log, daily_*.log, task_execution.log")
    
    def log_exception(self, message: str, exc_info=True):
        """记录异常信息"""
        self.logger.error(message, exc_info=exc_info)
    
    def log_task_start(self, task_id: str, task_type: str):
        """记录任务开始"""
        self.logger.info(f"任务开始 - ID: {task_id}, 类型: {task_type}")
    
    def log_task_end(self, task_id: str, success: bool, duration: float):
        """记录任务结束"""
        status = "成功" if success else "失败"
        self.logger.info(f"任务结束 - ID: {task_id}, 状态: {status}, 耗时: {duration:.2f}秒")
    
    def log_command_execution(self, command: str, success: bool, output: str = "", error: str = ""):
        """记录命令执行"""
        if success:
            self.logger.info(f"命令执行成功: {command}")
            if output:
                self.logger.debug(f"命令输出: {output[:500]}...")
        else:
            self.logger.error(f"命令执行失败: {command}")
            if error:
                self.logger.error(f"错误信息: {error}")
    
    def log_api_call(self, api: str, success: bool, response_time: float = None):
        """记录API调用"""
        if success:
            msg = f"API调用成功: {api}"
            if response_time:
                msg += f" (耗时: {response_time:.2f}秒)"
            self.logger.info(msg)
        else:
            self.logger.error(f"API调用失败: {api}")


def setup_global_logging():
    """设置全局日志配置"""
    # 创建全局日志器
    global_logger = EnhancedLogger("bot_agent")
    
    # 设置未捕获异常的处理器
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        global_logger.logger.critical(
            "未捕获的异常",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    sys.excepthook = handle_exception
    
    return global_logger


def get_logger(name: str = None) -> logging.Logger:
    """获取日志器"""
    if name:
        return logging.getLogger(name)
    else:
        return logging.getLogger("bot_agent")


# 创建全局日志器实例
global_enhanced_logger = setup_global_logging()


# 为向后兼容性提供的函数
def log_error(message: str, exc_info=True):
    """记录错误"""
    global_enhanced_logger.log_exception(message, exc_info)


def log_info(message: str):
    """记录信息"""
    global_enhanced_logger.logger.info(message)


def log_warning(message: str):
    """记录警告"""
    global_enhanced_logger.logger.warning(message)


def log_debug(message: str):
    """记录调试信息"""
    global_enhanced_logger.logger.debug(message)
