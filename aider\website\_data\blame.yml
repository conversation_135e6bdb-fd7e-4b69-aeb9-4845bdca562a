- aider_percentage: 29.08
  aider_total: 41
  end_date: '2023-06-15'
  end_tag: v0.6.0
  file_counts:
    aider/coder.py:
      <PERSON>: 32
      <PERSON> (aider): 4
    aider/commands.py:
      <PERSON>: 2
    aider/main.py:
      <PERSON>: 4
      <PERSON> (aider): 5
    aider/models.py:
      <PERSON>: 27
    aider/repomap.py:
      <PERSON>: 6
      <PERSON> (aider): 1
    aider/utils.py:
      <PERSON>: 22
      <PERSON> (aider): 17
    setup.py:
      <PERSON>: 7
      <PERSON> (aider): 7
    tests/test_utils.py:
      <PERSON> (aider): 7
  grand_total:
    <PERSON>: 100
    <PERSON> (aider): 41
  start_tag: v0.5.0
  total_lines: 141
- aider_percentage: 15.99
  aider_total: 224
  end_date: '2023-06-25'
  end_tag: v0.7.0
  file_counts:
    .github/workflows/release.yml:
      <PERSON>: 2
      <PERSON> (aider): 25
    aider/__init__.py:
      <PERSON>: 1
    aider/coders/__init__.py:
      <PERSON>: 6
    aider/coders/base_coder.py:
      <PERSON>: 305
    aider/coders/edit<PERSON>_coder.py:
      <PERSON> Gauthier: 32
    aider/coders/wholefile_coder.py:
      <PERSON> Gauthier: 86
      <PERSON>authier (aider): 3
    aider/coders/wholefile_func_coder.py:
      <PERSON> Gauthier: 116
      <PERSON> Gauthier (aider): 11
    aider/commands.py:
      <PERSON> <PERSON>authier: 28
    aider/diffs.py:
      Paul Gauthier: 17
    aider/io.py:
      Paul Gauthier: 16
    aider/main.py:
      Paul Gauthier: 51
      Paul Gauthier (aider): 8
    aider/models.py:
      Paul Gauthier: 52
    scripts/benchmark.py:
      Paul Gauthier: 312
      Paul Gauthier (aider): 22
    scripts/versionbump.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 44
    setup.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 2
    tests/test_coder.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 18
    tests/test_commands.py:
      Paul Gauthier: 3
    tests/test_editblock.py:
      Paul Gauthier: 24
    tests/test_main.py:
      Paul Gauthier: 8
    tests/test_models.py:
      Paul Gauthier: 21
      Paul Gauthier (aider): 7
    tests/test_wholefile.py:
      Paul Gauthier: 67
      Paul Gauthier (aider): 84
  grand_total:
    Paul Gauthier: 1177
    Paul Gauthier (aider): 224
  start_tag: v0.6.0
  total_lines: 1401
- aider_percentage: 8.21
  aider_total: 142
  end_date: '2023-07-06'
  end_tag: v0.8.0
  file_counts:
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 5
    .github/workflows/windows-tests.yml:
      Paul Gauthier: 13
      Paul Gauthier (aider): 25
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/__init__.py:
      Paul Gauthier: 10
    aider/coders/base_coder.py:
      Paul Gauthier: 159
      Paul Gauthier (aider): 5
    aider/coders/editblock_coder.py:
      Paul Gauthier: 14
    aider/coders/editblock_func_coder.py:
      Paul Gauthier: 123
      Paul Gauthier (aider): 8
    aider/coders/single_wholefile_func_coder.py:
      Paul Gauthier: 45
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 24
    aider/coders/wholefile_func_coder.py:
      Paul Gauthier: 14
    aider/commands.py:
      Paul Gauthier: 18
    aider/diffs.py:
      Paul Gauthier: 25
    aider/io.py:
      Paul Gauthier: 38
      Paul Gauthier (aider): 9
    aider/main.py:
      Paul Gauthier: 14
      Paul Gauthier (aider): 5
      kwmiebach: 5
    aider/repomap.py:
      Paul Gauthier: 30
    aider/utils.py:
      Paul Gauthier: 11
    benchmark/Dockerfile:
      Paul Gauthier: 7
    benchmark/benchmark.py:
      Paul Gauthier: 446
      Paul Gauthier (aider): 29
    benchmark/docker.sh:
      Paul Gauthier: 11
      Paul Gauthier (aider): 1
    benchmark/docker_build.sh:
      Paul Gauthier: 8
    benchmark/plot.sh:
      Paul Gauthier: 29
    benchmark/rungrid.py:
      Paul Gauthier: 60
    benchmark/test_benchmark.py:
      Paul Gauthier: 34
      Paul Gauthier (aider): 13
    tests/test_coder.py:
      Paul Gauthier: 87
      Paul Gauthier (aider): 24
    tests/test_commands.py:
      Paul Gauthier: 14
      Paul Gauthier (aider): 17
    tests/test_editblock.py:
      Paul Gauthier: 94
    tests/test_io.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 6
    tests/test_main.py:
      Paul Gauthier: 26
    tests/test_repomap.py:
      Paul Gauthier: 26
    tests/test_wholefile.py:
      Paul Gauthier: 193
  grand_total:
    Paul Gauthier: 1582
    Paul Gauthier (aider): 142
    kwmiebach: 5
  start_tag: v0.7.0
  total_lines: 1729
- aider_percentage: 17.67
  aider_total: 144
  end_date: '2023-07-16'
  end_tag: v0.9.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 74
    aider/coders/editblock_coder.py:
      Paul Gauthier: 8
    aider/coders/single_wholefile_func_coder.py:
      Paul Gauthier: 1
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 47
    aider/coders/wholefile_func_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 86
      Paul Gauthier (aider): 4
    aider/io.py:
      Paul Gauthier: 7
    aider/main.py:
      Paul Gauthier: 126
      Paul Gauthier (aider): 59
    aider/repomap.py:
      Paul Gauthier: 36
    aider/utils.py:
      Paul Gauthier: 8
    benchmark/benchmark.py:
      Paul Gauthier: 3
    scripts/versionbump.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 10
    setup.py:
      Paul Gauthier (aider): 1
    tests/test_coder.py:
      Paul Gauthier: 110
      Paul Gauthier (aider): 23
    tests/test_commands.py:
      Paul Gauthier: 36
      Paul Gauthier (aider): 43
    tests/test_editblock.py:
      Paul Gauthier: 1
    tests/test_io.py:
      Paul Gauthier: 1
    tests/test_main.py:
      Paul Gauthier: 12
    tests/test_repomap.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 1
    tests/test_wholefile.py:
      Paul Gauthier: 42
    tests/utils.py:
      Paul Gauthier: 43
      Paul Gauthier (aider): 3
  grand_total:
    Paul Gauthier: 671
    Paul Gauthier (aider): 144
  start_tag: v0.8.0
  total_lines: 815
- aider_percentage: 11.34
  aider_total: 33
  end_date: '2023-07-22'
  end_tag: v0.10.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 23
    aider/coders/editblock_coder.py:
      Paul Gauthier: 11
    aider/coders/single_wholefile_func_coder.py:
      Paul Gauthier: 1
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 1
    aider/coders/wholefile_func_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Amer Amayreh: 8
      Paul Gauthier (aider): 4
    aider/io.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 6
    aider/main.py:
      Paul Gauthier: 62
    aider/versioncheck.py:
      Paul Gauthier: 22
      Paul Gauthier (aider): 5
    benchmark/benchmark.py:
      Paul Gauthier: 1
    scripts/versionbump.py:
      Paul Gauthier (aider): 2
    tests/test_coder.py:
      Paul Gauthier: 35
    tests/test_commands.py:
      Paul Gauthier: 22
      Paul Gauthier (aider): 13
    tests/test_editblock.py:
      Paul Gauthier: 17
    tests/test_main.py:
      Paul Gauthier: 44
      Paul Gauthier (aider): 3
    tests/utils.py:
      Paul Gauthier: 6
  grand_total:
    Amer Amayreh: 8
    Paul Gauthier: 250
    Paul Gauthier (aider): 33
  start_tag: v0.9.0
  total_lines: 291
- aider_percentage: 6.62
  aider_total: 49
  end_date: '2023-08-02'
  end_tag: v0.11.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 104
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 2
    aider/commands.py:
      Paul Gauthier: 38
      Paul Gauthier (aider): 2
    aider/history.py:
      Paul Gauthier: 103
      Paul Gauthier (aider): 25
    aider/main.py:
      Paul Gauthier: 86
      Paul Gauthier (aider): 3
    aider/repo.py:
      Paul Gauthier: 113
      Paul Gauthier (aider): 7
    aider/sendchat.py:
      Paul Gauthier: 64
    scripts/versionbump.py:
      Paul Gauthier: 4
    tests/test_coder.py:
      Paul Gauthier: 26
    tests/test_commands.py:
      Paul Gauthier: 53
      Paul Gauthier (aider): 6
    tests/test_main.py:
      Paul Gauthier: 24
      Paul Gauthier (aider): 1
    tests/test_repo.py:
      Paul Gauthier: 56
      Paul Gauthier (aider): 5
    tests/test_sendchat.py:
      Paul Gauthier: 11
    tests/utils.py:
      Paul Gauthier: 6
  grand_total:
    Paul Gauthier: 691
    Paul Gauthier (aider): 49
  start_tag: v0.10.0
  total_lines: 740
- aider_percentage: 4.71
  aider_total: 24
  end_date: '2023-08-11'
  end_tag: v0.12.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Arseniy Pavlenko: 3
      Paul Gauthier: 4
    aider/coders/editblock_coder.py:
      Paul Gauthier: 123
    aider/commands.py:
      Joshua Vial: 2
      Paul Gauthier: 17
      Paul Gauthier (aider): 3
    aider/history.py:
      Paul Gauthier: 6
    aider/io.py:
      Paul Gauthier: 10
    aider/main.py:
      Paul Gauthier: 2
    aider/repo.py:
      Paul Gauthier: 26
    aider/repomap.py:
      Paul Gauthier: 22
    aider/sendchat.py:
      Paul Gauthier: 17
    aider/voice.py:
      Paul Gauthier: 77
      Paul Gauthier (aider): 8
    benchmark/benchmark.py:
      Paul Gauthier: 57
    scripts/versionbump.py:
      Paul Gauthier: 1
    tests/test_coder.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 13
    tests/test_commands.py:
      Paul Gauthier: 19
    tests/test_editblock.py:
      Paul Gauthier: 43
    tests/test_repo.py:
      Paul Gauthier: 55
  grand_total:
    Arseniy Pavlenko: 3
    Joshua Vial: 2
    Paul Gauthier: 481
    Paul Gauthier (aider): 24
  start_tag: v0.11.0
  total_lines: 510
- aider_percentage: 4.32
  aider_total: 23
  end_date: '2023-08-22'
  end_tag: v0.13.0
  file_counts:
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 1
    .github/workflows/windows-tests.yml:
      Paul Gauthier: 1
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 89
      Paul Gauthier (aider): 2
    aider/coders/editblock_coder.py:
      Paul Gauthier: 6
    aider/coders/editblock_func_coder.py:
      Paul Gauthier: 2
    aider/coders/single_wholefile_func_coder.py:
      Paul Gauthier: 2
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 16
    aider/coders/wholefile_func_coder.py:
      Paul Gauthier: 3
    aider/commands.py:
      Paul Gauthier: 34
    aider/io.py:
      Paul Gauthier: 7
    aider/main.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 19
    aider/repo.py:
      Paul Gauthier: 56
    aider/voice.py:
      Paul Gauthier: 24
    setup.py:
      Paul Gauthier (aider): 1
    tests/test_coder.py:
      Paul Gauthier: 255
      Paul Gauthier (aider): 1
    tests/test_main.py:
      Paul Gauthier: 1
    tests/test_repo.py:
      Paul Gauthier: 9
    tests/test_wholefile.py:
      Paul Gauthier: 1
  grand_total:
    Paul Gauthier: 510
    Paul Gauthier (aider): 23
  start_tag: v0.12.0
  total_lines: 533
- aider_percentage: 0.55
  aider_total: 1
  end_date: '2023-09-08'
  end_tag: v0.14.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Joshua Vial: 19
      Paul Gauthier: 4
    aider/commands.py:
      JV: 1
    aider/history.py:
      JV: 1
      Joshua Vial: 6
    aider/main.py:
      JV: 1
      Joshua Vial: 1
    aider/models/__init__.py:
      JV: 2
      Paul Gauthier: 13
    aider/models/model.py:
      JV: 21
      Joshua Vial: 4
      Paul Gauthier: 8
    aider/models/openai.py:
      JV: 3
      Paul Gauthier: 3
    aider/models/openrouter.py:
      JV: 28
      Joshua Vial: 2
      Paul Gauthier: 15
      Paul Gauthier (aider): 1
    aider/repo.py:
      JV: 2
    aider/repomap.py:
      JV: 1
      Joshua Vial: 1
    aider/sendchat.py:
      JV: 2
      Joshua Vial: 4
      Paul Gauthier: 1
    benchmark/Dockerfile:
      Paul Gauthier: 1
    setup.py:
      Paul Gauthier: 1
    tests/test_models.py:
      Joshua Vial: 22
      Paul Gauthier: 13
  grand_total:
    JV: 62
    Joshua Vial: 59
    Paul Gauthier: 60
    Paul Gauthier (aider): 1
  start_tag: v0.13.0
  total_lines: 182
- aider_percentage: 11.28
  aider_total: 38
  end_date: '2023-10-20'
  end_tag: v0.15.0
  file_counts:
    .github/workflows/release.yml:
      Paul Gauthier: 9
      Paul Gauthier (aider): 14
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 13
      Thinh Nguyen: 2
    aider/commands.py:
      Alexander Kjeldaas (aider): 1
      Paul Gauthier: 49
    aider/main.py:
      Paul Gauthier: 29
      Paul Gauthier (aider): 21
      Thinh Nguyen: 6
    aider/repo.py:
      Paul Gauthier: 26
      Paul Gauthier (aider): 2
    aider/repomap.py:
      Paul Gauthier: 11
    aider/voice.py:
      Paul Gauthier: 8
    benchmark/Dockerfile:
      Joshua Vial: 1
    benchmark/benchmark.py:
      Joshua Vial: 1
    docker/Dockerfile:
      Paul Gauthier: 9
    scripts/versionbump.py:
      Paul Gauthier: 2
    tests/test_commands.py:
      Paul Gauthier: 95
    tests/test_main.py:
      Paul Gauthier: 17
    tests/test_repo.py:
      Paul Gauthier: 20
  grand_total:
    Alexander Kjeldaas (aider): 1
    Joshua Vial: 2
    Paul Gauthier: 289
    Paul Gauthier (aider): 37
    Thinh Nguyen: 8
  start_tag: v0.14.0
  total_lines: 337
- aider_percentage: 1.76
  aider_total: 16
  end_date: '2023-10-29'
  end_tag: v0.16.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 7
    aider/coders/editblock_coder.py:
      Paul Gauthier: 13
    aider/commands.py:
      Paul Gauthier: 5
    aider/queries/tree-sitter-c-sharp-tags.scm:
      Paul Gauthier: 46
    aider/queries/tree-sitter-c-tags.scm:
      Paul Gauthier: 5
      Paul Gauthier (aider): 4
    aider/queries/tree-sitter-cpp-tags.scm:
      Paul Gauthier: 7
      Paul Gauthier (aider): 8
    aider/queries/tree-sitter-elisp-tags.scm:
      Paul Gauthier: 5
    aider/queries/tree-sitter-elixir-tags.scm:
      Paul Gauthier: 54
    aider/queries/tree-sitter-elm-tags.scm:
      Paul Gauthier: 19
    aider/queries/tree-sitter-go-tags.scm:
      Paul Gauthier: 30
    aider/queries/tree-sitter-java-tags.scm:
      Paul Gauthier: 20
    aider/queries/tree-sitter-javascript-tags.scm:
      Paul Gauthier: 88
    aider/queries/tree-sitter-ocaml-tags.scm:
      Paul Gauthier: 116
    aider/queries/tree-sitter-php-tags.scm:
      Paul Gauthier: 26
    aider/queries/tree-sitter-python-tags.scm:
      Paul Gauthier: 12
    aider/queries/tree-sitter-ql-tags.scm:
      Paul Gauthier: 26
    aider/queries/tree-sitter-ruby-tags.scm:
      Paul Gauthier: 64
    aider/queries/tree-sitter-rust-tags.scm:
      Paul Gauthier: 60
    aider/queries/tree-sitter-typescript-tags.scm:
      Paul Gauthier: 23
    aider/repomap.py:
      Paul Gauthier: 187
      Paul Gauthier (aider): 2
    benchmark/Dockerfile:
      Paul Gauthier: 4
    docker/Dockerfile:
      Paul Gauthier: 1
    setup.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 2
    tests/test_coder.py:
      Paul Gauthier: 19
    tests/test_commands.py:
      Paul Gauthier: 5
    tests/test_editblock.py:
      Paul Gauthier: 44
    tests/test_repomap.py:
      Paul Gauthier: 5
  grand_total:
    Paul Gauthier: 894
    Paul Gauthier (aider): 16
  start_tag: v0.15.0
  total_lines: 910
- aider_percentage: 5.35
  aider_total: 13
  end_date: '2023-11-06'
  end_tag: v0.17.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier: 7
      Paul Gauthier (aider): 7
    .github/workflows/release.yml:
      Paul Gauthier: 4
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 21
    aider/coders/editblock_coder.py:
      Paul Gauthier: 29
    aider/commands.py:
      Omri Bloch: 1
      Paul Gauthier: 5
      Paul Gauthier (aider): 6
    aider/io.py:
      Paul Gauthier: 1
    aider/main.py:
      Paul Gauthier: 1
    aider/models/openai.py:
      Paul Gauthier: 8
    aider/queries/tree-sitter-elisp-tags.scm:
      Paul Gauthier: 3
    aider/repomap.py:
      Paul Gauthier: 6
    benchmark/Dockerfile:
      Paul Gauthier: 2
    benchmark/benchmark.py:
      Paul Gauthier: 24
    docker/Dockerfile:
      Paul Gauthier: 5
    setup.py:
      Jack Hallam: 3
      Paul Gauthier: 10
    tests/test_commands.py:
      Paul Gauthier: 51
    tests/test_editblock.py:
      Paul Gauthier: 21
    tests/test_io.py:
      Paul Gauthier: 24
    tests/utils.py:
      Paul Gauthier: 3
  grand_total:
    Jack Hallam: 3
    Omri Bloch: 1
    Paul Gauthier: 226
    Paul Gauthier (aider): 13
  start_tag: v0.16.0
  total_lines: 243
- aider_percentage: 39.3
  aider_total: 90
  end_date: '2023-11-17'
  end_tag: v0.18.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 27
    aider/commands.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 3
    aider/io.py:
      Paul Gauthier: 3
    aider/models/model.py:
      Paul Gauthier: 13
    aider/repomap.py:
      Paul Gauthier: 10
    benchmark/benchmark.py:
      Paul Gauthier: 22
      Paul Gauthier (aider): 33
    benchmark/rungrid.py:
      Paul Gauthier: 16
    scripts/versionbump.py:
      Paul Gauthier (aider): 41
    tests/test_coder.py:
      Paul Gauthier: 14
    tests/test_commands.py:
      Paul Gauthier: 10
    tests/test_main.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 13
    tests/test_repomap.py:
      Paul Gauthier: 8
  grand_total:
    Paul Gauthier: 139
    Paul Gauthier (aider): 90
  start_tag: v0.17.0
  total_lines: 229
- aider_percentage: 0.72
  aider_total: 13
  end_date: '2023-12-19'
  end_tag: v0.19.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/__init__.py:
      Paul Gauthier: 2
    aider/coders/base_coder.py:
      Paul Gauthier: 54
    aider/coders/editblock_coder.py:
      Paul Gauthier: 2
    aider/coders/search_replace.py:
      Paul Gauthier: 767
    aider/coders/udiff_coder.py:
      Paul Gauthier: 389
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 2
    aider/commands.py:
      Paul Gauthier: 1
    aider/history.py:
      Paul Gauthier: 3
    aider/main.py:
      Paul Gauthier: 41
      Your Name: 3
      Your Name (aider): 13
    aider/models/__init__.py:
      Paul Gauthier: 3
    aider/models/model.py:
      Paul Gauthier: 7
    aider/models/openai.py:
      Paul Gauthier: 13
    aider/models/openrouter.py:
      Paul Gauthier: 4
    aider/repo.py:
      Paul Gauthier: 4
    aider/sendchat.py:
      Paul Gauthier: 15
    aider/utils.py:
      Paul Gauthier: 15
    aider/voice.py:
      Paul Gauthier: 7
    benchmark/benchmark.py:
      Paul Gauthier: 175
    benchmark/refactor_tools.py:
      Paul Gauthier: 209
    tests/test_coder.py:
      Paul Gauthier: 11
    tests/test_commands.py:
      Paul Gauthier: 1
    tests/test_io.py:
      Paul Gauthier: 1
    tests/test_main.py:
      Paul Gauthier: 10
      Your Name: 16
    tests/test_models.py:
      Paul Gauthier: 8
    tests/test_repo.py:
      Paul Gauthier: 1
    tests/test_repomap.py:
      Paul Gauthier: 1
    tests/test_sendchat.py:
      Paul Gauthier: 23
    tests/test_wholefile.py:
      Paul Gauthier: 10
  grand_total:
    Paul Gauthier: 1780
    Your Name: 19
    Your Name (aider): 13
  start_tag: v0.18.0
  total_lines: 1812
- aider_percentage: 11.38
  aider_total: 38
  end_date: '2024-01-04'
  end_tag: v0.20.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier: 4
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Joshua Vial: 26
      Paul Gauthier: 23
    aider/coders/search_replace.py:
      Paul Gauthier: 2
    aider/coders/udiff_coder.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 2
    aider/commands.py:
      Christopher Toth: 2
      Joshua Vial: 16
      Paul Gauthier: 2
      Paul Gauthier (aider): 6
    aider/io.py:
      Joshua Vial: 14
    aider/models/model.py:
      Joshua Vial: 43
    aider/models/openrouter.py:
      Joshua Vial: 4
    aider/repo.py:
      Christopher Toth: 5
    aider/repomap.py:
      Paul Gauthier: 6
    aider/sendchat.py:
      Joshua Vial: 9
    aider/utils.py:
      Joshua Vial: 29
    benchmark/benchmark.py:
      Joshua Vial: 16
    tests/test_commands.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 20
    tests/test_models.py:
      Joshua Vial: 13
    tests/test_udiff.py:
      Paul Gauthier: 63
      Paul Gauthier (aider): 10
  grand_total:
    Christopher Toth: 7
    Joshua Vial: 170
    Paul Gauthier: 119
    Paul Gauthier (aider): 38
  start_tag: v0.19.0
  total_lines: 334
- aider_percentage: 19.32
  aider_total: 17
  end_date: '2024-01-08'
  end_tag: v0.21.0
  file_counts:
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 1
    .github/workflows/windows-tests.yml:
      Paul Gauthier: 1
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/udiff_coder.py:
      Paul Gauthier: 22
    aider/main.py:
      Paul Gauthier (aider): 9
    aider/versioncheck.py:
      Paul Gauthier (aider): 8
    setup.py:
      Paul Gauthier: 2
    tests/test_udiff.py:
      Paul Gauthier: 44
  grand_total:
    Paul Gauthier: 71
    Paul Gauthier (aider): 17
  start_tag: v0.20.0
  total_lines: 88
- aider_percentage: 0.0
  aider_total: 0
  end_date: '2024-01-22'
  end_tag: v0.22.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 2
    aider/coders/udiff_coder.py:
      Paul Gauthier: 5
    aider/commands.py:
      Paul Gauthier: 44
    aider/main.py:
      Paul Gauthier: 2
  grand_total:
    Paul Gauthier: 54
  start_tag: v0.21.0
  total_lines: 54
- aider_percentage: 1.16
  aider_total: 2
  end_date: '2024-02-03'
  end_tag: v0.23.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 10
    aider/commands.py:
      Paul Gauthier: 2
    aider/main.py:
      Paul Gauthier: 13
      Zachary Vorhies: 6
    aider/mdstream.py:
      Paul Gauthier: 120
      Paul Gauthier (aider): 2
    aider/models/openai.py:
      Paul Gauthier: 3
    benchmark/benchmark.py:
      Paul Gauthier: 16
  grand_total:
    Paul Gauthier: 165
    Paul Gauthier (aider): 2
    Zachary Vorhies: 6
  start_tag: v0.22.0
  total_lines: 173
- aider_percentage: 6.68
  aider_total: 25
  end_date: '2024-02-10'
  end_tag: v0.24.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 5
    aider/commands.py:
      Paul Gauthier: 16
      Paul Gauthier (aider): 8
    aider/main.py:
      Paul Gauthier: 2
    aider/models/__init__.py:
      Paul Gauthier: 2
    aider/models/model.py:
      Paul Gauthier: 3
    aider/models/openai.py:
      Paul Gauthier: 135
    aider/scrape.py:
      Paul Gauthier: 170
      Paul Gauthier (aider): 17
    aider/utils.py:
      Paul Gauthier: 7
    tests/test_models.py:
      Paul Gauthier: 8
  grand_total:
    Paul Gauthier: 349
    Paul Gauthier (aider): 25
  start_tag: v0.23.0
  total_lines: 374
- aider_percentage: 5.79
  aider_total: 7
  end_date: '2024-03-04'
  end_tag: v0.25.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 46
    aider/commands.py:
      Paul Gauthier: 5
    aider/main.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 7
    aider/models/openai.py:
      Paul Gauthier: 1
    aider/repo.py:
      Paul Gauthier: 11
    aider/scrape.py:
      Paul Gauthier: 1
    tests/test_coder.py:
      Paul Gauthier: 22
    tests/test_commands.py:
      Paul Gauthier: 23
  grand_total:
    Paul Gauthier: 114
    Paul Gauthier (aider): 7
  start_tag: v0.24.0
  total_lines: 121
- aider_percentage: 0.0
  aider_total: 0
  end_date: '2024-03-08'
  end_tag: v0.26.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 8
    aider/main.py:
      Paul Gauthier: 26
  grand_total:
    Paul Gauthier: 35
  start_tag: v0.25.0
  total_lines: 35
- aider_percentage: 0.0
  aider_total: 0
  end_date: '2024-03-22'
  end_tag: v0.27.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 10
    aider/main.py:
      Paul Gauthier: 14
    aider/queries/tree-sitter-typescript-tags.scm:
      Ryan Freckleton: 21
    aider/repomap.py:
      Paul Gauthier: 6
    benchmark/benchmark.py:
      Paul Gauthier: 91
    tests/test_commands.py:
      Paul Gauthier: 3
    tests/test_repomap.py:
      Ryan Freckleton: 48
  grand_total:
    Paul Gauthier: 125
    Ryan Freckleton: 69
  start_tag: v0.26.0
  total_lines: 194
- aider_percentage: 0.0
  aider_total: 0
  end_date: '2024-04-09'
  end_tag: v0.28.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/models/openai.py:
      Paul Gauthier: 3
  grand_total:
    Paul Gauthier: 4
  start_tag: v0.27.0
  total_lines: 4
- aider_percentage: 5.69
  aider_total: 35
  end_date: '2024-04-21'
  end_tag: v0.29.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier: 1
    .github/workflows/release.yml:
      Paul Gauthier: 2
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 2
    .github/workflows/windows-tests.yml:
      Paul Gauthier: 2
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Aloha: 1
      Paul Gauthier: 22
    aider/coders/editblock_coder.py:
      Paul Gauthier: 9
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 2
    aider/commands.py:
      Paul Gauthier: 30
    aider/history.py:
      Paul Gauthier: 6
    aider/main.py:
      Paul Gauthier: 68
    aider/models.py:
      Paul Gauthier: 216
      Paul Gauthier (aider): 33
    aider/repo.py:
      Paul Gauthier: 19
    aider/repomap.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 2
    aider/sendchat.py:
      Paul Gauthier: 4
    aider/voice.py:
      Paul Gauthier: 3
    benchmark/benchmark.py:
      Paul Gauthier: 60
    tests/test_coder.py:
      Paul Gauthier: 28
    tests/test_commands.py:
      Paul Gauthier: 25
    tests/test_editblock.py:
      Paul Gauthier: 4
    tests/test_models.py:
      Paul Gauthier: 13
    tests/test_repo.py:
      Paul Gauthier: 17
    tests/test_repomap.py:
      Paul Gauthier: 13
    tests/test_sendchat.py:
      Paul Gauthier: 8
    tests/test_wholefile.py:
      Paul Gauthier: 14
  grand_total:
    Aloha: 1
    Paul Gauthier: 579
    Paul Gauthier (aider): 35
  start_tag: v0.28.0
  total_lines: 615
- aider_percentage: 0.0
  aider_total: 0
  end_date: '2024-04-23'
  end_tag: v0.30.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 11
    aider/history.py:
      Paul Gauthier: 2
    aider/main.py:
      Paul Gauthier: 26
    aider/models.py:
      Paul Gauthier: 154
    aider/sendchat.py:
      Paul Gauthier: 3
    aider/voice.py:
      Paul Gauthier: 2
    benchmark/benchmark.py:
      Paul Gauthier: 1
    tests/test_coder.py:
      Paul Gauthier: 1
    tests/test_commands.py:
      Paul Gauthier: 1
    tests/test_editblock.py:
      Paul Gauthier: 1
    tests/test_models.py:
      Paul Gauthier: 6
    tests/test_repo.py:
      Paul Gauthier: 1
    tests/test_repomap.py:
      Paul Gauthier: 2
    tests/test_wholefile.py:
      Paul Gauthier: 1
  grand_total:
    Paul Gauthier: 213
  start_tag: v0.29.0
  total_lines: 213
- aider_percentage: 0.16
  aider_total: 2
  end_date: '2024-05-02'
  end_tag: v0.31.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 375
    aider/coders/base_coder.py:
      Paul Gauthier: 120
    aider/commands.py:
      Paul Gauthier: 45
    aider/gui.py:
      Paul Gauthier: 531
      Paul Gauthier (aider): 2
    aider/main.py:
      Paul Gauthier: 114
    aider/models.py:
      Paul Gauthier: 14
    aider/scrape.py:
      Paul Gauthier: 15
    aider/sendchat.py:
      Paul Gauthier: 3
    tests/test_coder.py:
      Paul Gauthier: 16
    tests/test_commands.py:
      Paul Gauthier: 8
    tests/test_editblock.py:
      Paul Gauthier: 4
    tests/test_wholefile.py:
      Paul Gauthier: 1
  grand_total:
    Paul Gauthier: 1247
    Paul Gauthier (aider): 2
  start_tag: v0.30.0
  total_lines: 1249
- aider_percentage: 3.29
  aider_total: 8
  end_date: '2024-05-07'
  end_tag: v0.32.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 7
    aider/coders/__init__.py:
      Paul Gauthier: 2
    aider/coders/base_coder.py:
      Paul Gauthier: 54
    aider/coders/editblock_coder.py:
      Paul Gauthier: 3
    aider/coders/editblock_fenced_coder.py:
      Paul Gauthier: 11
    aider/gui.py:
      Paul Gauthier: 1
    aider/main.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 3
    aider/models.py:
      Paul Gauthier: 45
    aider/sendchat.py:
      Paul Gauthier: 10
    aider/utils.py:
      Paul Gauthier: 1
    benchmark/benchmark.py:
      Paul Gauthier: 78
      Paul Gauthier (aider): 5
    benchmark/plots.py:
      Paul Gauthier: 3
    tests/test_main.py:
      Paul Gauthier: 10
    tests/test_sendchat.py:
      Paul Gauthier: 4
  grand_total:
    Paul Gauthier: 235
    Paul Gauthier (aider): 8
  start_tag: v0.31.0
  total_lines: 243
- aider_percentage: 0.0
  aider_total: 0
  end_date: '2024-05-08'
  end_tag: v0.33.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 1
    aider/litellm.py:
      Paul Gauthier: 11
    aider/main.py:
      Paul Gauthier: 1
    aider/models.py:
      Paul Gauthier: 3
    aider/sendchat.py:
      Paul Gauthier: 1
    aider/voice.py:
      Paul Gauthier: 2
  grand_total:
    Paul Gauthier: 21
  start_tag: v0.32.0
  total_lines: 21
- aider_percentage: 0.0
  aider_total: 0
  end_date: '2024-05-10'
  end_tag: v0.34.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 6
    aider/coders/base_coder.py:
      Paul Gauthier: 18
    aider/main.py:
      Paul Gauthier: 9
    aider/models.py:
      Paul Gauthier: 7
    aider/repomap.py:
      Paul Gauthier: 3
    aider/sendchat.py:
      Paul Gauthier: 6
    tests/test_sendchat.py:
      Paul Gauthier: 4
  grand_total:
    Paul Gauthier: 54
  start_tag: v0.33.0
  total_lines: 54
- aider_percentage: 6.8
  aider_total: 17
  end_date: '2024-05-13'
  end_tag: v0.35.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 5
    aider/coders/base_coder.py:
      Paul Gauthier: 22
      Paul Gauthier (aider): 1
    aider/coders/editblock_coder.py:
      Paul Gauthier: 82
      Paul Gauthier (aider): 10
    aider/history.py:
      Paul Gauthier: 20
    aider/io.py:
      Paul Gauthier: 7
    aider/main.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 1
    aider/models.py:
      Paul Gauthier: 18
    aider/sendchat.py:
      Paul Gauthier: 6
    aider/utils.py:
      Paul Gauthier: 51
    aider/versioncheck.py:
      Paul Gauthier: 10
  grand_total:
    Paul Gauthier: 233
    Paul Gauthier (aider): 17
  start_tag: v0.34.0
  total_lines: 250
- aider_percentage: 15.38
  aider_total: 92
  end_date: '2024-05-22'
  end_tag: v0.36.0
  file_counts:
    Gemfile:
      Paul Gauthier (aider): 5
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 37
      Paul Gauthier (aider): 3
    aider/coders/base_coder.py:
      Paul Gauthier: 110
      Paul Gauthier (aider): 3
    aider/coders/wholefile_coder.py:
      Paul Gauthier (aider): 2
    aider/commands.py:
      Paul Gauthier: 45
    aider/io.py:
      Paul Gauthier: 9
    aider/linter.py:
      Paul Gauthier: 211
      Paul Gauthier (aider): 29
    aider/litellm.py:
      Paul Gauthier: 2
    aider/main.py:
      Paul Gauthier: 48
      Paul Gauthier (aider): 2
    aider/models.py:
      Paul Gauthier: 3
    aider/repo.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 14
    benchmark/benchmark.py:
      Paul Gauthier: 5
    benchmark/over_time.py:
      Paul Gauthier: 29
      Paul Gauthier (aider): 28
    scripts/jekyll_build.sh:
      Paul Gauthier: 1
      Paul Gauthier (aider): 3
    scripts/jekyll_run.sh:
      Paul Gauthier: 1
      Paul Gauthier (aider): 3
  grand_total:
    Paul Gauthier: 506
    Paul Gauthier (aider): 92
  start_tag: v0.35.0
  total_lines: 598
- aider_percentage: 19.06
  aider_total: 113
  end_date: '2024-06-04'
  end_tag: v0.37.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 71
      Paul Gauthier (aider): 3
    aider/coders/editblock_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Aleksandr Bobrov: 1
      Aleksandr Bobrov (aider): 1
      Paul Gauthier: 24
    aider/io.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 1
    aider/linter.py:
      Paul Gauthier: 4
    aider/litellm.py:
      Paul Gauthier: 1
    aider/repomap.py:
      Paul Gauthier: 113
    aider/sendchat.py:
      Paul Gauthier: 2
    aider/voice.py:
      Paul Gauthier (aider): 4
    benchmark/over_time.py:
      Paul Gauthier (aider): 7
    benchmark/swe_bench.py:
      Paul Gauthier: 99
      Paul Gauthier (aider): 24
    scripts/blame.py:
      Paul Gauthier: 153
      Paul Gauthier (aider): 59
    tests/test_io.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 14
  grand_total:
    Aleksandr Bobrov: 1
    Aleksandr Bobrov (aider): 1
    Paul Gauthier: 479
    Paul Gauthier (aider): 112
  start_tag: v0.36.0
  total_lines: 593
- aider_percentage: 9.53
  aider_total: 53
  end_date: '2024-06-16'
  end_tag: v0.38.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier: 2
      Paul Gauthier (aider): 4
    .github/workflows/pages.yml:
      Paul Gauthier: 71
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 3
      Paul Gauthier (aider): 4
    .github/workflows/windows-tests.yml:
      Paul Gauthier: 3
      Paul Gauthier (aider): 4
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Krazer: 4
      Paul Gauthier: 57
      develmusa: 1
    aider/args_formatter.py:
      Paul Gauthier: 116
      Paul Gauthier (aider): 20
    aider/coders/base_coder.py:
      Paul Gauthier: 77
    aider/commands.py:
      Paul Gauthier: 29
    aider/gui.py:
      Paul Gauthier: 22
    aider/io.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 2
    aider/main.py:
      Krazer: 13
      Paul Gauthier: 11
      Paul Gauthier (aider): 5
    aider/models.py:
      Krazer: 11
      Paul Gauthier: 10
    aider/repo.py:
      Paul Gauthier: 2
    aider/repomap.py:
      Paul Gauthier: 13
    aider/scrape.py:
      Paul Gauthier: 10
    aider/tests/test_urls.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 8
    aider/urls.py:
      Paul Gauthier: 8
    scripts/jekyll_run.sh:
      Paul Gauthier: 9
    scripts/update-docs.sh:
      Paul Gauthier: 14
      Paul Gauthier (aider): 6
    website/Gemfile:
      Paul Gauthier: 4
  grand_total:
    Krazer: 28
    Paul Gauthier: 474
    Paul Gauthier (aider): 53
    develmusa: 1
  start_tag: v0.37.0
  total_lines: 556
- aider_percentage: 15.56
  aider_total: 47
  end_date: '2024-06-20'
  end_tag: v0.39.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/__main__.py:
      Paul Gauthier (aider): 4
    aider/args.py:
      Daniel Vainsencher: 6
      John-Mason P. Shackelford: 14
      Paul Gauthier: 14
    aider/args_formatter.py:
      Paul Gauthier: 24
      Paul Gauthier (aider): 9
    aider/coders/base_coder.py:
      Daniel Vainsencher: 4
      Daniel Vainsencher (aider): 2
      Paul Gauthier: 2
    aider/commands.py:
      Paul Gauthier: 1
    aider/io.py:
      Daniel Vainsencher: 14
    aider/main.py:
      Daniel Vainsencher: 1
      John-Mason P. Shackelford: 14
    aider/models.py:
      Paul Gauthier: 14
    aider/repo.py:
      Paul Gauthier: 23
    aider/scrape.py:
      Nicolas Perez: 1
    aider/tests/test_commands.py:
      Paul Gauthier: 6
    aider/tests/test_main.py:
      John-Mason P. Shackelford: 80
    aider/tests/test_repo.py:
      Paul Gauthier: 19
      Paul Gauthier (aider): 21
    aider/urls.py:
      Nicolas Perez: 1
      Paul Gauthier: 1
    aider/utils.py:
      Daniel Vainsencher: 7
      Daniel Vainsencher (aider): 11
      John-Mason P. Shackelford: 7
    scripts/update-docs.sh:
      Paul Gauthier: 1
  grand_total:
    Daniel Vainsencher: 32
    Daniel Vainsencher (aider): 13
    John-Mason P. Shackelford: 115
    Nicolas Perez: 2
    Paul Gauthier: 106
    Paul Gauthier (aider): 34
  start_tag: v0.38.0
  total_lines: 302
- aider_percentage: 6.42
  aider_total: 21
  end_date: '2024-06-24'
  end_tag: v0.40.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Krazer: 6
      Paul Gauthier: 27
    aider/coders/base_coder.py:
      Paul Gauthier: 28
    aider/coders/editblock_coder.py:
      Paul Gauthier: 64
    aider/linter.py:
      Paul Gauthier: 23
      Paul Gauthier (aider): 21
    aider/main.py:
      Krazer: 32
      Paul Gauthier: 23
    aider/models.py:
      Dustin Miller: 13
      Krazer: 31
      Paul Gauthier: 16
    aider/repo.py:
      Paul Gauthier: 26
    aider/tests/test_editblock.py:
      Paul Gauthier: 16
  grand_total:
    Dustin Miller: 13
    Krazer: 69
    Paul Gauthier: 224
    Paul Gauthier (aider): 21
  start_tag: v0.39.0
  total_lines: 327
- aider_percentage: 4.74
  aider_total: 11
  end_date: '2024-07-01'
  end_tag: v0.41.0
  file_counts:
    .github/workflows/release.yml:
      Paul Gauthier: 1
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 5
    aider/coders/base_coder.py:
      Paul Gauthier: 122
      Paul Gauthier (aider): 2
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 2
    aider/commands.py:
      Amir Elaguizy (aider): 3
      Paul Gauthier: 1
    aider/gui.py:
      Paul Gauthier: 4
    aider/main.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 1
    aider/mdstream.py:
      Paul Gauthier: 1
    aider/models.py:
      Mitsuki Ogasahara: 3
      Paul Gauthier: 28
    aider/repo.py:
      Paul Gauthier: 7
    aider/repomap.py:
      Paul Gauthier: 12
    aider/sendchat.py:
      Paul Gauthier: 2
    aider/tests/test_coder.py:
      Paul Gauthier: 10
    aider/tests/test_editblock.py:
      Paul Gauthier: 2
    aider/tests/test_wholefile.py:
      Paul Gauthier: 4
    scripts/update-docs.sh:
      Paul Gauthier: 3
    setup.py:
      Paul Gauthier: 3
  grand_total:
    Amir Elaguizy (aider): 3
    Mitsuki Ogasahara: 3
    Paul Gauthier: 218
    Paul Gauthier (aider): 8
  start_tag: v0.40.0
  total_lines: 232
- aider_percentage: 2.29
  aider_total: 7
  end_date: '2024-07-04'
  end_tag: v0.42.0
  file_counts:
    .github/workflows/pages.yml:
      Paul Gauthier: 14
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 6
    aider/coders/base_coder.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 7
    aider/commands.py:
      Paul Gauthier: 31
    aider/history.py:
      Paul Gauthier: 5
    aider/io.py:
      Paul Gauthier: 32
    aider/llm.py:
      Paul Gauthier: 18
    aider/main.py:
      Paul Gauthier: 26
    aider/models.py:
      Paul Gauthier: 78
    aider/repomap.py:
      Paul Gauthier: 4
    aider/scrape.py:
      Paul Gauthier: 8
    aider/sendchat.py:
      Paul Gauthier: 45
    aider/tests/test_sendchat.py:
      Paul Gauthier: 1
    aider/versioncheck.py:
      Paul Gauthier: 12
    aider/voice.py:
      Paul Gauthier: 6
    scripts/jekyll_run.sh:
      Paul Gauthier: 2
  grand_total:
    Paul Gauthier: 299
    Paul Gauthier (aider): 7
  start_tag: v0.41.0
  total_lines: 306
- aider_percentage: 10.61
  aider_total: 40
  end_date: '2024-07-07'
  end_tag: v0.43.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier: 2
    .github/workflows/pages.yml:
      Paul Gauthier: 4
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 2
    .github/workflows/windows-tests.yml:
      Paul Gauthier: 2
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
    aider/args_formatter.py:
      Paul Gauthier: 4
    aider/coders/__init__.py:
      Paul Gauthier: 2
    aider/coders/base_coder.py:
      Paul Gauthier: 44
    aider/coders/help_coder.py:
      Paul Gauthier: 15
    aider/commands.py:
      Paul Gauthier: 63
      Paul Gauthier (aider): 5
    aider/help.py:
      Paul Gauthier: 114
      Paul Gauthier (aider): 6
    aider/help_pats.py:
      Paul Gauthier: 10
    aider/llm.py:
      Paul Gauthier: 1
    aider/main.py:
      Paul Gauthier: 35
    aider/repomap.py:
      Paul Gauthier: 14
    aider/tests/test_commands.py:
      Paul Gauthier: 1
    aider/tests/test_help.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 26
    aider/versioncheck.py:
      Paul Gauthier: 2
    scripts/jekyll_run.sh:
      Paul Gauthier: 1
    scripts/update-docs.sh:
      Paul Gauthier: 7
    setup.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 3
  grand_total:
    Paul Gauthier: 337
    Paul Gauthier (aider): 40
  start_tag: v0.42.0
  total_lines: 377
- aider_percentage: 27.02
  aider_total: 157
  end_date: '2024-07-16'
  end_tag: v0.44.0
  file_counts:
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 3
    .github/workflows/windows-tests.yml:
      Paul Gauthier: 4
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 4
    aider/args_formatter.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier (aider): 1
    aider/coders/editblock_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 10
    aider/help.py:
      Paul Gauthier: 20
    aider/main.py:
      Paul Gauthier: 20
    aider/models.py:
      Paul Gauthier: 11
    aider/scrape.py:
      Paul Gauthier: 53
    aider/utils.py:
      Paul Gauthier: 78
      Paul Gauthier (aider): 16
    aider/versioncheck.py:
      Paul Gauthier: 27
    aider/voice.py:
      Paul Gauthier: 6
    benchmark/Dockerfile:
      Paul Gauthier: 3
    docker/Dockerfile:
      Paul Gauthier: 14
      Paul Gauthier (aider): 1
    scripts/blame.py:
      Paul Gauthier: 38
      Paul Gauthier (aider): 49
    scripts/pip-compile.sh:
      Paul Gauthier: 18
    scripts/update-docs.sh:
      Paul Gauthier: 2
    setup.py:
      Paul Gauthier: 26
      Paul Gauthier (aider): 1
    tests/basic/test_coder.py:
      Paul Gauthier: 25
      Paul Gauthier (aider): 37
    tests/browser/test_browser.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 16
    tests/help/test_help.py:
      Paul Gauthier: 23
    tests/scrape/test_scrape.py:
      Paul Gauthier: 16
      Paul Gauthier (aider): 26
  grand_total:
    Paul Gauthier: 424
    Paul Gauthier (aider): 157
  start_tag: v0.43.0
  total_lines: 581
- aider_percentage: 44.93
  aider_total: 93
  end_date: '2024-07-18'
  end_tag: v0.45.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 3
    aider/commands.py:
      Paul Gauthier: 18
      Paul Gauthier (aider): 4
    aider/main.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 5
    aider/models.py:
      Paul Gauthier: 7
    aider/repomap.py:
      Paul Gauthier: 1
    aider/scrape.py:
      Paul Gauthier: 8
    aider/versioncheck.py:
      Paul Gauthier: 14
    tests/basic/test_coder.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 21
    tests/basic/test_commands.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 55
    tests/basic/test_main.py:
      Paul Gauthier: 27
      Paul Gauthier (aider): 5
  grand_total:
    Paul Gauthier: 114
    Paul Gauthier (aider): 93
  start_tag: v0.44.0
  total_lines: 207
- aider_percentage: 52.87
  aider_total: 313
  end_date: '2024-07-29'
  end_tag: v0.46.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier (aider): 3
    aider/coders/__init__.py:
      Paul Gauthier: 2
      Your Name: 1
    aider/coders/ask_coder.py:
      Your Name: 9
    aider/coders/base_coder.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 43
      Your Name: 27
      Your Name (aider): 6
    aider/coders/editblock_coder.py:
      Your Name (aider): 2
    aider/coders/editblock_fenced_coder.py:
      Your Name (aider): 2
    aider/coders/help_coder.py:
      Your Name: 1
      Your Name (aider): 1
    aider/coders/udiff_coder.py:
      Your Name (aider): 2
    aider/coders/wholefile_coder.py:
      Your Name (aider): 2
    aider/commands.py:
      Paul Gauthier: 43
      Your Name: 26
      Your Name (aider): 25
    aider/io.py:
      Paul Gauthier: 3
    aider/llm.py:
      Paul Gauthier: 10
    aider/main.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 8
      Your Name: 6
      Your Name (aider): 1
    aider/models.py:
      Paul Gauthier: 9
    aider/queries/tree-sitter-elm-tags.scm:
      Charles Joachim: 4
    aider/repomap.py:
      Paul Gauthier: 12
    aider/scrape.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 32
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 11
      Paul Gauthier (aider): 85
    benchmark/Dockerfile:
      Your Name: 1
    tests/basic/test_coder.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 5
    tests/basic/test_repo.py:
      Paul Gauthier (aider): 13
    tests/basic/test_repomap.py:
      Paul Gauthier: 70
      Paul Gauthier (aider): 10
    tests/scrape/test_scrape.py:
      Paul Gauthier: 14
      Paul Gauthier (aider): 73
  grand_total:
    Charles Joachim: 4
    Paul Gauthier: 204
    Paul Gauthier (aider): 272
    Your Name: 71
    Your Name (aider): 41
  start_tag: v0.45.0
  total_lines: 592
- aider_percentage: 56.98
  aider_total: 355
  end_date: '2024-07-31'
  end_tag: v0.47.0
  file_counts:
    .github/workflows/docker-release.yml:
      Paul Gauthier (aider): 20
    .github/workflows/release.yml:
      Paul Gauthier (aider): 2
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 5
    aider/coders/base_coder.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 3
    aider/commands.py:
      Paul Gauthier: 20
      Paul Gauthier (aider): 4
    aider/history.py:
      Paul Gauthier: 16
      Paul Gauthier (aider): 6
    aider/io.py:
      Paul Gauthier: 27
      Paul Gauthier (aider): 8
    aider/linter.py:
      Paul Gauthier: 14
      Paul Gauthier (aider): 1
    aider/main.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 1
    aider/queries/tree-sitter-ocaml-tags.scm:
      Paul Gauthier: 12
      Paul Gauthier (aider): 18
    aider/repo.py:
      Paul Gauthier (aider): 4
    aider/repomap.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 5
    aider/scrape.py:
      Paul Gauthier: 2
    aider/sendchat.py:
      Paul Gauthier (aider): 2
    aider/utils.py:
      Paul Gauthier: 7
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 11
      Paul Gauthier (aider): 12
    docker/Dockerfile:
      Paul Gauthier: 19
      Paul Gauthier (aider): 18
    scripts/blame.py:
      Paul Gauthier: 65
      Paul Gauthier (aider): 99
    scripts/update-blame.sh:
      Paul Gauthier: 6
    scripts/update-docs.sh:
      Paul Gauthier: 1
    tests/basic/test_coder.py:
      Paul Gauthier: 32
      Paul Gauthier (aider): 4
    tests/basic/test_commands.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 25
    tests/basic/test_history.py:
      Paul Gauthier (aider): 109
    tests/basic/test_repo.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 9
    tests/basic/test_repomap.py:
      Paul Gauthier: 1
  grand_total:
    Paul Gauthier: 268
    Paul Gauthier (aider): 355
  start_tag: v0.46.0
  total_lines: 623
- aider_percentage: 45.67
  aider_total: 269
  end_date: '2024-08-06'
  end_tag: v0.48.0
  file_counts:
    .github/workflows/ubuntu-tests.yml:
      paul-gauthier: 1
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 6
    aider/coders/base_coder.py:
      Paul Gauthier: 61
      Paul Gauthier (aider): 41
    aider/commands.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 24
    aider/history.py:
      Paul Gauthier: 2
    aider/main.py:
      Paul Gauthier: 20
      Paul Gauthier (aider): 30
    aider/models.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 7
      Thinh Nguyen: 1
    aider/repo.py:
      Paul Gauthier: 42
      Paul Gauthier (aider): 23
    aider/repomap.py:
      Paul Gauthier: 60
      Paul Gauthier (aider): 3
    aider/sendchat.py:
      Paul Gauthier: 26
      Paul Gauthier (aider): 2
    aider/utils.py:
      Paul Gauthier: 29
      Paul Gauthier (aider): 4
    scripts/blame.py:
      Paul Gauthier (aider): 2
    tests/basic/test_coder.py:
      Paul Gauthier: 10
    tests/basic/test_commands.py:
      Paul Gauthier: 19
      Paul Gauthier (aider): 14
    tests/basic/test_history.py:
      Paul Gauthier: 2
    tests/basic/test_main.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 39
    tests/basic/test_repo.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 35
    tests/basic/test_scripting.py:
      Paul Gauthier (aider): 39
  grand_total:
    Paul Gauthier: 318
    Paul Gauthier (aider): 269
    Thinh Nguyen: 1
    paul-gauthier: 1
  start_tag: v0.47.0
  total_lines: 589
- aider_percentage: 59.83
  aider_total: 429
  end_date: '2024-08-10'
  end_tag: v0.49.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 9
    aider/coders/base_coder.py:
      Paul Gauthier: 81
      Paul Gauthier (aider): 41
    aider/commands.py:
      Paul Gauthier: 34
      Paul Gauthier (aider): 105
    aider/io.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 23
    aider/llm.py:
      Paul Gauthier (aider): 5
    aider/main.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 4
    aider/models.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 3
    aider/repo.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 12
    aider/repomap.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 23
    aider/scrape.py:
      Paul Gauthier (aider): 17
    aider/sendchat.py:
      Paul Gauthier: 20
    aider/urls.py:
      Paul Gauthier: 1
    aider/utils.py:
      Paul Gauthier (aider): 11
    aider/versioncheck.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 9
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 11
      Paul Gauthier (aider): 11
    docker/Dockerfile:
      Paul Gauthier: 5
      Paul Gauthier (aider): 2
    tests/basic/test_coder.py:
      Paul Gauthier (aider): 5
    tests/basic/test_commands.py:
      Paul Gauthier: 35
      Paul Gauthier (aider): 83
    tests/basic/test_editblock.py:
      Paul Gauthier (aider): 1
    tests/basic/test_main.py:
      Paul Gauthier (aider): 28
    tests/basic/test_sendchat.py:
      Paul Gauthier: 45
    tests/basic/test_wholefile.py:
      Paul Gauthier (aider): 1
    tests/scrape/test_scrape.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 36
  grand_total:
    Paul Gauthier: 288
    Paul Gauthier (aider): 429
  start_tag: v0.48.0
  total_lines: 717
- aider_percentage: 65.23
  aider_total: 182
  end_date: '2024-08-13'
  end_tag: v0.50.0
  file_counts:
    .github/workflows/release.yml:
      Branch Vincent: 2
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier (aider): 8
    aider/coders/base_coder.py:
      Paul Gauthier: 24
      Paul Gauthier (aider): 30
    aider/commands.py:
      Amir Elaguizy (aider): 11
      Paul Gauthier: 26
      Paul Gauthier (aider): 15
    aider/io.py:
      Paul Gauthier: 1
    aider/main.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 2
    aider/models.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 4
    aider/scrape.py:
      Paul Gauthier (aider): 26
    aider/sendchat.py:
      Paul Gauthier (aider): 1
    aider/utils.py:
      Paul Gauthier: 1
    aider/versioncheck.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 1
    scripts/versionbump.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 34
    tests/basic/test_coder.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 16
    tests/basic/test_commands.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 27
    tests/basic/test_main.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 7
    tests/help/test_help.py:
      Paul Gauthier: 7
  grand_total:
    Amir Elaguizy (aider): 11
    Branch Vincent: 2
    Paul Gauthier: 95
    Paul Gauthier (aider): 171
  start_tag: v0.49.0
  total_lines: 279
- aider_percentage: 56.95
  aider_total: 582
  end_date: '2024-08-20'
  end_tag: v0.51.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 8
    aider/coders/__init__.py:
      Paul Gauthier: 3
    aider/coders/base_coder.py:
      Paul Gauthier: 168
      Paul Gauthier (aider): 45
    aider/coders/single_wholefile_func_coder.py:
      Paul Gauthier: 28
    aider/commands.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 5
    aider/llm.py:
      Paul Gauthier: 2
    aider/main.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 13
    aider/models.py:
      Paul Gauthier: 35
      Paul Gauthier (aider): 4
    aider/repomap.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 55
    aider/sendchat.py:
      Paul Gauthier: 3
    aider/utils.py:
      Paul Gauthier (aider): 6
    aider/website/_includes/code-in-json-benchmark.js:
      Paul Gauthier: 101
      Paul Gauthier (aider): 64
    aider/website/_includes/code-in-json-syntax.js:
      Paul Gauthier: 23
      Paul Gauthier (aider): 116
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    benchmark/benchmark.py:
      Paul Gauthier: 7
    benchmark/over_time.py:
      Paul Gauthier: 14
      Paul Gauthier (aider): 57
    docker/Dockerfile:
      Paul Gauthier: 10
    scripts/blame.py:
      Paul Gauthier (aider): 17
    tests/basic/test_commands.py:
      Paul Gauthier: 5
    tests/basic/test_main.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 92
    tests/basic/test_repomap.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 100
  grand_total:
    Paul Gauthier: 440
    Paul Gauthier (aider): 582
  start_tag: v0.50.0
  total_lines: 1022
- aider_percentage: 67.74
  aider_total: 485
  end_date: '2024-08-23'
  end_tag: v0.52.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 6
    aider/coders/base_coder.py:
      Paul Gauthier: 77
      Paul Gauthier (aider): 25
    aider/coders/chat_chunks.py:
      Paul Gauthier (aider): 53
    aider/coders/editblock_coder.py:
      Paul Gauthier: 42
      Paul Gauthier (aider): 67
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 40
      pcamp: 1
    aider/io.py:
      Paul Gauthier: 41
      Paul Gauthier (aider): 40
    aider/main.py:
      Paul Gauthier: 2
    aider/models.py:
      Paul Gauthier: 30
    aider/repomap.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 5
    aider/utils.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 9
    aider/versioncheck.py:
      Paul Gauthier: 2
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    benchmark/benchmark.py:
      Paul Gauthier: 1
    scripts/blame.py:
      Paul Gauthier: 1
    tests/basic/test_commands.py:
      Paul Gauthier (aider): 74
    tests/basic/test_editblock.py:
      Paul Gauthier (aider): 1
    tests/basic/test_find_or_blocks.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 106
    tests/basic/test_io.py:
      Paul Gauthier (aider): 32
    tests/basic/test_main.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 27
    tests/basic/test_wholefile.py:
      Paul Gauthier: 8
  grand_total:
    Paul Gauthier: 230
    Paul Gauthier (aider): 485
    pcamp: 1
  start_tag: v0.51.0
  total_lines: 716
- aider_percentage: 62.36
  aider_total: 434
  end_date: '2024-08-27'
  end_tag: v0.53.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 10
    aider/coders/base_coder.py:
      Paul Gauthier: 55
      Paul Gauthier (aider): 18
    aider/coders/chat_chunks.py:
      Paul Gauthier (aider): 9
    aider/coders/editblock_coder.py:
      Paul Gauthier: 44
      Paul Gauthier (aider): 6
    aider/commands.py:
      Paul Gauthier: 18
    aider/history.py:
      Paul Gauthier (aider): 3
    aider/io.py:
      Paul Gauthier: 44
      Paul Gauthier (aider): 22
    aider/main.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 9
    aider/models.py:
      Paul Gauthier: 50
      Paul Gauthier (aider): 21
    aider/repo.py:
      Paul Gauthier (aider): 3
    aider/repomap.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 1
    aider/sendchat.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 11
    aider/utils.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 9
    aider/versioncheck.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 10
    scripts/versionbump.py:
      Paul Gauthier: 1
    tests/basic/test_commands.py:
      Paul Gauthier: 6
    tests/basic/test_editblock.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 27
    tests/basic/test_io.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 44
    tests/basic/test_main.py:
      Paul Gauthier: 2
    tests/basic/test_models.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 42
    tests/basic/test_repo.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 8
    tests/basic/test_repomap.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 63
    tests/fixtures/sample-code-base/sample.js:
      Paul Gauthier (aider): 50
    tests/fixtures/sample-code-base/sample.py:
      Paul Gauthier (aider): 68
  grand_total:
    Paul Gauthier: 262
    Paul Gauthier (aider): 434
  start_tag: v0.52.0
  total_lines: 696
- aider_percentage: 67.4
  aider_total: 184
  end_date: '2024-08-28'
  end_tag: v0.54.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier (aider): 1
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier (aider): 1
    .github/workflows/windows-tests.yml:
      Paul Gauthier (aider): 1
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 10
    aider/coders/base_coder.py:
      Paul Gauthier: 25
      Paul Gauthier (aider): 12
    aider/commands.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 4
    aider/io.py:
      Paul Gauthier: 28
    aider/main.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 4
    aider/models.py:
      Paul Gauthier (aider): 7
    aider/run_cmd.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 66
    aider/utils.py:
      Paul Gauthier (aider): 14
    aider/versioncheck.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 9
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    tests/basic/test_coder.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 43
    tests/basic/test_io.py:
      Paul Gauthier: 4
    tests/basic/test_main.py:
      Antti Kaihola: 4
      Paul Gauthier (aider): 12
    tests/scrape/test_scrape.py:
      Paul Gauthier: 1
  grand_total:
    Antti Kaihola: 4
    Paul Gauthier: 85
    Paul Gauthier (aider): 184
  start_tag: v0.53.0
  total_lines: 273
- aider_percentage: 52.82
  aider_total: 759
  end_date: '2024-09-04'
  end_tag: v0.55.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 2
    aider/args.py:
      Paul Gauthier (aider): 7
    aider/coders/base_coder.py:
      Paul Gauthier: 62
      Paul Gauthier (aider): 39
    aider/coders/editblock_coder.py:
      Nikolay Sedelnikov: 8
    aider/coders/editblock_func_coder.py:
      Antti Kaihola: 2
    aider/coders/search_replace.py:
      Paul Gauthier: 2
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 16
    aider/commands.py:
      Antti Kaihola: 7
      Paul Gauthier: 74
      Paul Gauthier (aider): 25
    aider/format_settings.py:
      Paul Gauthier (aider): 2
    aider/gui.py:
      Paul Gauthier: 4
    aider/io.py:
      Paul Gauthier: 56
      Paul Gauthier (aider): 11
    aider/linter.py:
      Paul Gauthier: 5
    aider/llm.py:
      Paul Gauthier: 2
    aider/main.py:
      Paul Gauthier: 86
      Paul Gauthier (aider): 22
    aider/models.py:
      Paul Gauthier: 24
      Paul Gauthier (aider): 2
    aider/repo.py:
      Paul Gauthier: 85
    aider/repomap.py:
      Paul Gauthier: 32
      Paul Gauthier (aider): 4
    aider/report.py:
      Paul Gauthier: 77
      Paul Gauthier (aider): 120
    aider/run_cmd.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 24
    aider/scrape.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 8
    aider/special.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 197
    aider/urls.py:
      Paul Gauthier (aider): 1
    aider/utils.py:
      Paul Gauthier: 31
      Paul Gauthier (aider): 29
    aider/versioncheck.py:
      Paul Gauthier: 22
      Paul Gauthier (aider): 6
    aider/voice.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 9
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    scripts/versionbump.py:
      Paul Gauthier: 7
    tests/basic/test_coder.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 96
    tests/basic/test_editblock.py:
      Antti Kaihola: 3
      Nikolay Sedelnikov: 23
    tests/basic/test_io.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 14
    tests/basic/test_main.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 10
    tests/basic/test_models.py:
      Paul Gauthier (aider): 4
    tests/basic/test_repomap.py:
      Paul Gauthier (aider): 31
    tests/basic/test_run_cmd.py:
      Paul Gauthier (aider): 11
    tests/basic/test_special.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 74
    tests/scrape/test_scrape.py:
      Paul Gauthier (aider): 11
  grand_total:
    Antti Kaihola: 12
    Nikolay Sedelnikov: 31
    Paul Gauthier: 635
    Paul Gauthier (aider): 759
  start_tag: v0.54.0
  total_lines: 1437
- aider_percentage: 56.23
  aider_total: 149
  end_date: '2024-09-09'
  end_tag: v0.56.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 6
    aider/coders/base_coder.py:
      Paul Gauthier: 14
      Paul Gauthier (aider): 10
    aider/commands.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 6
    aider/io.py:
      Paul Gauthier: 5
    aider/linter.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 4
      fry69: 9
    aider/main.py:
      Paul Gauthier: 32
      Paul Gauthier (aider): 49
    aider/models.py:
      Paul Gauthier: 2
      fry69: 3
    aider/repo.py:
      Paul Gauthier: 14
    aider/repomap.py:
      Paul Gauthier: 13
    aider/report.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 20
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    benchmark/benchmark.py:
      Paul Gauthier: 1
    tests/basic/test_linter.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 51
    tests/basic/test_main.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 3
  grand_total:
    Paul Gauthier: 104
    Paul Gauthier (aider): 149
    fry69: 12
  start_tag: v0.55.0
  total_lines: 265
- aider_percentage: 70.27
  aider_total: 390
  end_date: '2024-09-21'
  end_tag: v0.57.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args_formatter.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 1
    aider/coders/base_coder.py:
      Krazer: 1
      Paul Gauthier: 14
      Paul Gauthier (aider): 2
    aider/coders/chat_chunks.py:
      Paul Gauthier: 4
    aider/coders/editblock_coder.py:
      Paul Gauthier (aider): 27
    aider/commands.py:
      Krazer: 3
      Paul Gauthier: 1
      Paul Gauthier (aider): 34
    aider/io.py:
      Krazer: 23
      Paul Gauthier: 8
      Paul Gauthier (aider): 42
    aider/main.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 8
    aider/models.py:
      Jay Alammar: 1
      Jay Alammar (aider): 13
      Paul Gauthier: 48
      Paul Gauthier (aider): 30
    aider/repo.py:
      Paul Gauthier: 3
    aider/run_cmd.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 33
    aider/sendchat.py:
      Paul Gauthier: 3
    aider/utils.py:
      Paul Gauthier: 2
    aider/website/docs/leaderboards/index.md:
      Anjor Kanekar: 1
      Paul Gauthier: 1
      Paul Gauthier (aider): 12
    benchmark/benchmark.py:
      Paul Gauthier: 4
    scripts/issues.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 123
    scripts/versionbump.py:
      Paul Gauthier (aider): 8
    tests/basic/test_coder.py:
      Paul Gauthier: 1
    tests/basic/test_editblock.py:
      Christian Clauss: 2
    tests/basic/test_io.py:
      Paul Gauthier (aider): 37
    tests/basic/test_main.py:
      Paul Gauthier: 18
      Paul Gauthier (aider): 20
  grand_total:
    Anjor Kanekar: 1
    Christian Clauss: 2
    Jay Alammar: 1
    Jay Alammar (aider): 13
    Krazer: 27
    Paul Gauthier: 134
    Paul Gauthier (aider): 377
  start_tag: v0.56.0
  total_lines: 555
- aider_percentage: 44.68
  aider_total: 600
  end_date: '2024-09-29'
  end_tag: v0.58.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier: 1
      Paul Gauthier (aider): 5
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Mike Bailey: 7
      Paul Gauthier: 8
      Paul Gauthier (aider): 51
      Stein Martin Hustad: 17
      fry69: 2
    aider/coders/__init__.py:
      Paul Gauthier: 6
    aider/coders/architect_coder.py:
      Paul Gauthier: 40
      Paul Gauthier (aider): 3
    aider/coders/base_coder.py:
      Paul Gauthier: 32
      Paul Gauthier (aider): 4
    aider/coders/editor_editblock_coder.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 1
    aider/coders/editor_whole_coder.py:
      Paul Gauthier: 7
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 2
    aider/commands.py:
      Jonathan Ellis: 1
      Mike Bailey: 1
      Paul Gauthier: 17
      Paul Gauthier (aider): 77
      fry69: 2
    aider/help.py:
      Paul Gauthier: 27
      Paul Gauthier (aider): 7
    aider/history.py:
      Paul Gauthier: 1
    aider/io.py:
      Paul Gauthier: 38
      Paul Gauthier (aider): 62
      Stein Martin Hustad: 5
      fry69: 9
    aider/linter.py:
      Paul Gauthier: 5
    aider/main.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 6
      Stein Martin Hustad: 4
      fry69: 1
      rti: 1
    aider/models.py:
      Paul Gauthier: 58
      Paul Gauthier (aider): 79
    aider/repo.py:
      Paul Gauthier: 16
      Paul Gauthier (aider): 2
    aider/repomap.py:
      Paul Gauthier: 5
    aider/scrape.py:
      Paul Gauthier (aider): 3
    aider/sendchat.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 5
    aider/utils.py:
      Paul Gauthier: 4
    aider/versioncheck.py:
      Paul Gauthier: 2
    aider/voice.py:
      Mike Bailey: 17
      Paul Gauthier: 2
      Paul Gauthier (aider): 6
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 152
    benchmark/benchmark.py:
      Paul Gauthier: 25
      Paul Gauthier (aider): 29
      fry69: 3
    scripts/issues.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 43
    scripts/update-docs.sh:
      Paul Gauthier: 1
    scripts/yank-old-versions.py:
      Paul Gauthier (aider): 51
    tests/basic/test_commands.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 82
    tests/basic/test_io.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 84
    tests/basic/test_main.py:
      Paul Gauthier: 2
    tests/basic/test_models.py:
      Paul Gauthier: 4
    tests/basic/test_sanity_check_repo.py:
      fry69: 179
    tests/basic/test_wholefile.py:
      Paul Gauthier: 9
  grand_total:
    Jonathan Ellis: 1
    Mike Bailey: 25
    Paul Gauthier: 494
    Paul Gauthier (aider): 600
    Stein Martin Hustad: 26
    fry69: 196
    rti: 1
  start_tag: v0.57.0
  total_lines: 1343
- aider_percentage: 68.72
  aider_total: 123
  end_date: '2024-10-04'
  end_tag: v0.59.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 6
    aider/args_formatter.py:
      Paul Gauthier: 4
    aider/coders/architect_coder.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 6
    aider/coders/editblock_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 47
    aider/gui.py:
      Paul Gauthier: 2
    aider/main.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 4
    aider/models.py:
      Paul Gauthier (aider): 12
    aider/repomap.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 3
    aider/urls.py:
      Paul Gauthier: 2
    aider/versioncheck.py:
      Paul Gauthier: 1
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 4
    scripts/issues.py:
      Paul Gauthier: 1
    scripts/update-docs.sh:
      Paul Gauthier: 2
    tests/basic/test_commands.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 33
    tests/basic/test_models.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 18
    tests/basic/test_sanity_check_repo.py:
      Paul Gauthier: 1
    tests/help/test_help.py:
      Paul Gauthier: 1
  grand_total:
    Paul Gauthier: 56
    Paul Gauthier (aider): 123
  start_tag: v0.58.0
  total_lines: 179
- aider_percentage: 57.2
  aider_total: 139
  end_date: '2024-10-22'
  end_tag: v0.60.0
  file_counts:
    .github/workflows/close-stale.yml:
      Paul Gauthier: 5
      Paul Gauthier (aider): 19
    .github/workflows/pages.yml:
      Paul Gauthier: 3
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 1
      fry69: 2
    aider/coders/base_coder.py:
      Paul Gauthier: 2
    aider/coders/editblock_coder.py:
      Paul Gauthier (aider): 3
    aider/commands.py:
      Paul Gauthier: 1
    aider/help.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 33
    aider/io.py:
      Jonathan Ellis: 10
      Paul Gauthier: 7
    aider/main.py:
      Paul Gauthier: 20
      Paul Gauthier (aider): 39
    aider/models.py:
      Paul Gauthier: 9
      Sven Grunewaldt: 10
      fry69: 5
    aider/resources/__init__.py:
      Paul Gauthier: 3
    aider/sendchat.py:
      Paul Gauthier: 3
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    tests/basic/test_editblock.py:
      Paul Gauthier: 15
    tests/basic/test_main.py:
      Paul Gauthier: 1
    tests/help/test_help.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 45
  grand_total:
    Jonathan Ellis: 10
    Paul Gauthier: 77
    Paul Gauthier (aider): 139
    Sven Grunewaldt: 10
    fry69: 7
  start_tag: v0.59.0
  total_lines: 243
- aider_percentage: 67.04
  aider_total: 781
  end_date: '2024-11-01'
  end_tag: v0.61.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/analytics.py:
      Paul Gauthier: 75
      Paul Gauthier (aider): 89
    aider/args.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 21
    aider/coders/base_coder.py:
      Paul Gauthier: 55
      Paul Gauthier (aider): 43
    aider/coders/editblock_coder.py:
      Paul Gauthier: 14
    aider/commands.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 88
    aider/io.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 32
    aider/linter.py:
      Paul Gauthier: 6
    aider/main.py:
      Paul Gauthier: 46
      Paul Gauthier (aider): 12
    aider/models.py:
      Paul Gauthier: 22
      Paul Gauthier (aider): 54
      kAIto47802: 4
    aider/repomap.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 54
    aider/sendchat.py:
      Paul Gauthier: 23
      Paul Gauthier (aider): 23
    aider/urls.py:
      Paul Gauthier: 2
    aider/utils.py:
      Paul Gauthier (aider): 6
    scripts/issues.py:
      Paul Gauthier (aider): 13
    scripts/pip-compile.sh:
      Paul Gauthier (aider): 13
    scripts/update-docs.sh:
      Paul Gauthier: 1
      Paul Gauthier (aider): 5
    tests/basic/test_analytics.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 99
    tests/basic/test_commands.py:
      Konstantin L: 10
      Paul Gauthier: 80
      Paul Gauthier (aider): 197
    tests/basic/test_io.py:
      Paul Gauthier (aider): 6
    tests/basic/test_main.py:
      Paul Gauthier (aider): 3
    tests/basic/test_models.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 9
    tests/basic/test_sanity_check_repo.py:
      Paul Gauthier (aider): 6
    tests/basic/test_sendchat.py:
      Paul Gauthier (aider): 8
  grand_total:
    Konstantin L: 10
    Paul Gauthier: 370
    Paul Gauthier (aider): 781
    kAIto47802: 4
  start_tag: v0.60.0
  total_lines: 1165
- aider_percentage: 77.78
  aider_total: 56
  end_date: '2024-11-04'
  end_tag: v0.62.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier (aider): 12
    aider/coders/editblock_coder.py:
      Paul Gauthier: 6
    aider/main.py:
      Paul Gauthier (aider): 4
    aider/models.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 28
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 4
      Paul Gauthier (aider): 12
  grand_total:
    Paul Gauthier: 16
    Paul Gauthier (aider): 56
  start_tag: v0.61.0
  total_lines: 72
- aider_percentage: 55.21
  aider_total: 350
  end_date: '2024-11-13'
  end_tag: v0.63.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/architect_coder.py:
      Paul Gauthier: 3
    aider/coders/base_coder.py:
      Paul Gauthier: 42
      Paul Gauthier (aider): 1
    aider/coders/editblock_coder.py:
      Paul Gauthier: 4
    aider/commands.py:
      Paul Gauthier: 13
    aider/exceptions.py:
      Paul Gauthier: 72
      Paul Gauthier (aider): 4
    aider/io.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 23
    aider/main.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 9
    aider/models.py:
      Logan Attwood: 13
      Paul Gauthier: 37
      Paul Gauthier (aider): 4
    aider/repo.py:
      Paul Gauthier: 7
    aider/repomap.py:
      Paul Gauthier: 3
    aider/sendchat.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 4
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    scripts/issues.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 179
    tests/basic/test_coder.py:
      Paul Gauthier: 2
    tests/basic/test_commands.py:
      Paul Gauthier (aider): 13
    tests/basic/test_editblock.py:
      Paul Gauthier: 41
    tests/basic/test_exceptions.py:
      Paul Gauthier (aider): 65
    tests/basic/test_main.py:
      Paul Gauthier: 1
    tests/basic/test_sanity_check_repo.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 2
    tests/basic/test_sendchat.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 46
    tests/scrape/test_scrape.py:
      Paul Gauthier: 1
  grand_total:
    Logan Attwood: 13
    Paul Gauthier: 271
    Paul Gauthier (aider): 350
  start_tag: v0.62.0
  total_lines: 634
- aider_percentage: 73.55
  aider_total: 865
  end_date: '2024-11-21'
  end_tag: v0.64.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/analytics.py:
      Paul Gauthier: 20
      Paul Gauthier (aider): 21
    aider/args.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 9
    aider/coders/base_coder.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 3
      caetanominuzzo: 1
    aider/commands.py:
      Chad Phillips: 4
      Paul Gauthier: 5
      Paul Gauthier (aider): 19
    aider/editor.py:
      Chad Phillips: 135
      Paul Gauthier (aider): 11
    aider/exceptions.py:
      Paul Gauthier: 5
    aider/help_pats.py:
      Paul Gauthier: 1
    aider/io.py:
      Chad Phillips: 9
      Paul Gauthier (aider): 41
      mw: 21
    aider/main.py:
      Paul Gauthier: 18
      Paul Gauthier (aider): 35
    aider/models.py:
      Paul Gauthier: 31
      Paul Gauthier (aider): 34
    aider/repo.py:
      Paul Gauthier (aider): 5
    aider/urls.py:
      Paul Gauthier: 1
    aider/website/_includes/edit-leaderboard.js:
      Paul Gauthier (aider): 96
    aider/website/_includes/quant-chart.js:
      Paul Gauthier: 3
      Paul Gauthier (aider): 65
    aider/website/_includes/refactor-leaderboard.js:
      Paul Gauthier (aider): 89
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
      Paul Gauthier (aider): 10
    aider/website/share/index.md:
      Paul Gauthier (aider): 29
    benchmark/over_time.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 162
    scripts/blame.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 2
    scripts/issues.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 12
    scripts/versionbump.py:
      Paul Gauthier: 5
    tests/basic/test_analytics.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 30
    tests/basic/test_commands.py:
      Paul Gauthier (aider): 4
    tests/basic/test_editor.py:
      Paul Gauthier (aider): 129
    tests/basic/test_main.py:
      Paul Gauthier (aider): 5
    tests/basic/test_models.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 54
  grand_total:
    Chad Phillips: 148
    Paul Gauthier: 141
    Paul Gauthier (aider): 865
    caetanominuzzo: 1
    mw: 21
  start_tag: v0.63.0
  total_lines: 1176
- aider_percentage: 81.19
  aider_total: 544
  end_date: '2024-11-26'
  end_tag: v0.65.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/analytics.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 5
    aider/args.py:
      Paul Gauthier (aider): 10
    aider/coders/base_coder.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 31
    aider/commands.py:
      Paul Gauthier: 2
    aider/io.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 9
    aider/main.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 19
    aider/models.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 17
    aider/queries/tree-sitter-dart-tags.scm:
      malkoG: 83
    aider/urls.py:
      Paul Gauthier (aider): 1
    aider/website/_includes/quant-chart.js:
      Paul Gauthier (aider): 76
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    benchmark/benchmark.py:
      Paul Gauthier (aider): 10
    benchmark/docker.sh:
      Paul Gauthier (aider): 1
    benchmark/over_time.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 157
    scripts/update-docs.sh:
      Paul Gauthier: 1
    scripts/update-history.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 64
    tests/basic/test_coder.py:
      Paul Gauthier (aider): 70
    tests/basic/test_editor.py:
      Paul Gauthier (aider): 12
    tests/basic/test_main.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 19
    tests/basic/test_models.py:
      Paul Gauthier (aider): 30
    tests/basic/test_repomap.py:
      Paul Gauthier (aider): 13
  grand_total:
    Paul Gauthier: 43
    Paul Gauthier (aider): 544
    malkoG: 83
  start_tag: v0.64.0
  total_lines: 670
- aider_percentage: 86.17
  aider_total: 841
  end_date: '2024-12-01'
  end_tag: v0.66.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier (aider): 2
    .github/workflows/docker-release.yml:
      Paul Gauthier (aider): 2
    .github/workflows/pages.yml:
      Paul Gauthier (aider): 3
    .github/workflows/release.yml:
      Paul Gauthier (aider): 2
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier: 1
      Paul Gauthier (aider): 8
    .github/workflows/windows-tests.yml:
      Paul Gauthier (aider): 4
    aider/__init__.py:
      Paul Gauthier: 16
    aider/analytics.py:
      Paul Gauthier (aider): 19
    aider/args.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 6
      Philippe de Reynal: 6
    aider/coders/base_coder.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 39
    aider/commands.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 27
    aider/io.py:
      Paul Gauthier (aider): 17
    aider/linter.py:
      Paul Gauthier (aider): 2
    aider/main.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 46
    aider/models.py:
      Paul Gauthier: 7
    aider/run_cmd.py:
      Paul Gauthier (aider): 8
    aider/utils.py:
      Paul Gauthier: 1
    aider/voice.py:
      Philippe de Reynal: 24
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    benchmark/benchmark.py:
      Paul Gauthier (aider): 38
    scripts/blame.py:
      Paul Gauthier (aider): 26
    scripts/issues.py:
      Paul Gauthier: 3
    scripts/update-history.py:
      Paul Gauthier (aider): 58
    tests/basic/test_coder.py:
      Paul Gauthier: 4
    tests/basic/test_commands.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 64
    tests/basic/test_main.py:
      Paul Gauthier (aider): 4
    tests/basic/test_models.py:
      Paul Gauthier: 2
    tests/basic/test_repomap.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 45
    tests/basic/test_voice.py:
      Paul Gauthier (aider): 103
    tests/browser/test_browser.py:
      Paul Gauthier: 1
    tests/fixtures/languages/c/test.c:
      Paul Gauthier (aider): 6
    tests/fixtures/languages/cpp/test.cpp:
      Paul Gauthier (aider): 6
    tests/fixtures/languages/csharp/test.cs:
      Paul Gauthier (aider): 39
    tests/fixtures/languages/elisp/test.el:
      Paul Gauthier (aider): 25
    tests/fixtures/languages/elixir/test.ex:
      Paul Gauthier (aider): 5
    tests/fixtures/languages/elm/test.elm:
      Paul Gauthier: 1
      Paul Gauthier (aider): 37
    tests/fixtures/languages/go/test.go:
      Paul Gauthier: 1
      Paul Gauthier (aider): 41
    tests/fixtures/languages/java/test.java:
      Paul Gauthier: 2
      Paul Gauthier (aider): 14
    tests/fixtures/languages/javascript/test.js:
      Paul Gauthier: 1
      Paul Gauthier (aider): 25
    tests/fixtures/languages/ocaml/test.ml:
      Paul Gauthier: 2
      Paul Gauthier (aider): 17
    tests/fixtures/languages/php/test.php:
      Paul Gauthier (aider): 5
    tests/fixtures/languages/python/test.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 26
    tests/fixtures/languages/ql/test.ql:
      Paul Gauthier (aider): 3
    tests/fixtures/languages/ruby/test.rb:
      Paul Gauthier (aider): 3
    tests/fixtures/languages/rust/test.rs:
      Paul Gauthier (aider): 33
    tests/fixtures/languages/tsx/test.tsx:
      Paul Gauthier (aider): 30
    tests/fixtures/languages/typescript/test.ts:
      Paul Gauthier (aider): 3
  grand_total:
    Paul Gauthier: 105
    Paul Gauthier (aider): 841
    Philippe de Reynal: 30
  start_tag: v0.65.0
  total_lines: 976
- aider_percentage: 67.86
  aider_total: 437
  end_date: '2024-12-06'
  end_tag: v0.67.0
  file_counts:
    .github/workflows/issues.yml:
      Paul Gauthier (aider): 29
    aider/__init__.py:
      Paul Gauthier: 1
    aider/analytics.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 7
    aider/args.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 4
    aider/coders/base_coder.py:
      Paul Gauthier: 15
    aider/commands.py:
      Paul Gauthier: 11
    aider/io.py:
      Paul Gauthier: 28
      Paul Gauthier (aider): 31
    aider/llm.py:
      Paul Gauthier: 5
    aider/main.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 5
    aider/models.py:
      Paul Gauthier: 1
    aider/run_cmd.py:
      Paul Gauthier: 1
    aider/utils.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 22
    aider/watch.py:
      Paul Gauthier: 51
      Paul Gauthier (aider): 221
    aider/website/_includes/qwq-chart.js:
      Paul Gauthier: 30
      Paul Gauthier (aider): 47
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    scripts/blame.py:
      Paul Gauthier (aider): 26
    scripts/update-history.py:
      Paul Gauthier: 5
    scripts/versionbump.py:
      Paul Gauthier: 2
    tests/basic/test_analytics.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 3
    tests/basic/test_main.py:
      Paul Gauthier: 1
    tests/basic/test_models.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 7
    tests/basic/test_watch.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 34
    tests/browser/test_browser.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 1
  grand_total:
    Paul Gauthier: 207
    Paul Gauthier (aider): 437
  start_tag: v0.66.0
  total_lines: 644
- aider_percentage: 71.57
  aider_total: 428
  end_date: '2024-12-10'
  end_tag: v0.68.0
  file_counts:
    .github/workflows/pages.yml:
      Paul Gauthier (aider): 1
    aider/__init__.py:
      Paul Gauthier: 1
    aider/analytics.py:
      Paul Gauthier: 2
    aider/args.py:
      Paul Gauthier: 30
      Paul Gauthier (aider): 15
    aider/coders/base_coder.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 10
    aider/coders/editor_editblock_coder.py:
      Paul Gauthier: 1
    aider/coders/editor_whole_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 24
      Paul Gauthier (aider): 25
    aider/copypaste.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 68
    aider/exceptions.py:
      Paul Gauthier: 9
    aider/history.py:
      Paul Gauthier (aider): 1
    aider/io.py:
      Paul Gauthier: 13
    aider/main.py:
      Paul Gauthier: 17
      Paul Gauthier (aider): 29
    aider/models.py:
      Paul Gauthier: 15
    aider/repo.py:
      Paul Gauthier (aider): 1
    aider/run_cmd.py:
      Paul Gauthier: 1
    aider/sendchat.py:
      Paul Gauthier (aider): 4
    aider/utils.py:
      Paul Gauthier: 1
    aider/voice.py:
      Paul Gauthier: 9
    aider/watch.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 35
    aider/website/_includes/edit-leaderboard.js:
      Paul Gauthier: 2
      Paul Gauthier (aider): 90
    aider/website/_includes/head_custom.html:
      Paul Gauthier: 7
      Paul Gauthier (aider): 53
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
      Paul Gauthier (aider): 2
    scripts/blame.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 17
    scripts/pip-compile.sh:
      Paul Gauthier: 2
    tests/basic/test_commands.py:
      Paul Gauthier (aider): 24
    tests/basic/test_history.py:
      Paul Gauthier (aider): 3
    tests/basic/test_main.py:
      Paul Gauthier (aider): 46
    tests/basic/test_repo.py:
      Paul Gauthier (aider): 3
    tests/basic/test_sendchat.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 1
    tests/basic/test_watch.py:
      Paul Gauthier: 1
  grand_total:
    Paul Gauthier: 170
    Paul Gauthier (aider): 428
  start_tag: v0.67.0
  total_lines: 598
- aider_percentage: 67.87
  aider_total: 207
  end_date: '2024-12-13'
  end_tag: v0.69.0
  file_counts:
    .github/workflows/pages.yml:
      Paul Gauthier: 2
    aider/__init__.py:
      Paul Gauthier: 1
    aider/analytics.py:
      Paul Gauthier: 2
    aider/args.py:
      Mir Adnan ALI: 3
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      JeongJuhyeon: 1
      Mir Adnan ALI: 3
    aider/commands.py:
      Mir Adnan ALI: 4
      Paul Gauthier: 5
      Paul Gauthier (aider): 3
    aider/io.py:
      Mir Adnan ALI: 37
      Paul Gauthier: 8
      Paul Gauthier (aider): 3
    aider/main.py:
      Mir Adnan ALI: 1
    aider/models.py:
      Paul Gauthier: 7
    aider/watch.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 47
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    benchmark/benchmark.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 7
    scripts/blame.py:
      Paul Gauthier (aider): 1
    scripts/issues.py:
      Paul Gauthier (aider): 58
    scripts/update-history.py:
      Paul Gauthier: 3
    tests/basic/test_io.py:
      Paul Gauthier (aider): 20
    tests/basic/test_watch.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 68
  grand_total:
    JeongJuhyeon: 1
    Mir Adnan ALI: 48
    Paul Gauthier: 49
    Paul Gauthier (aider): 207
  start_tag: v0.68.0
  total_lines: 305
- aider_percentage: 74.22
  aider_total: 875
  end_date: '2024-12-26'
  end_tag: v0.70.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/analytics.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 41
    aider/args.py:
      Evan Johnson: 2
    aider/coders/search_replace.py:
      Paul Gauthier: 5
    aider/commands.py:
      Paul Gauthier (aider): 41
    aider/help_pats.py:
      Paul Gauthier: 3
    aider/io.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 9
    aider/main.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 5
      apaz-cli: 3
      mdk: 6
    aider/models.py:
      Paul Gauthier: 29
    aider/repo.py:
      Paul Gauthier: 14
    aider/utils.py:
      Paul Gauthier: 2
    aider/watch.py:
      Paul Gauthier: 13
    aider/website/_includes/head_custom.html:
      Paul Gauthier (aider): 4
    aider/website/_includes/leaderboard.js:
      Paul Gauthier (aider): 14
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 28
      Paul Gauthier (aider): 2
    benchmark/Dockerfile:
      Paul Gauthier: 8
      Paul Gauthier (aider): 43
    benchmark/benchmark.py:
      Paul Gauthier: 69
      Paul Gauthier (aider): 153
    benchmark/clone-exercism.sh:
      Paul Gauthier: 2
      Paul Gauthier (aider): 18
    benchmark/cpp-test.sh:
      Paul Gauthier: 10
      Paul Gauthier (aider): 1
    benchmark/docker.sh:
      Paul Gauthier (aider): 4
    benchmark/install-docker-ubuntu.sh:
      Paul Gauthier (aider): 63
    benchmark/npm-test.sh:
      Paul Gauthier: 10
      Paul Gauthier (aider): 3
    benchmark/problem_stats.py:
      Paul Gauthier: 35
      Paul Gauthier (aider): 318
    benchmark/rsync.sh:
      Paul Gauthier: 7
      Paul Gauthier (aider): 26
    scripts/blame.py:
      Paul Gauthier (aider): 6
    scripts/my_models.py:
      Paul Gauthier (aider): 95
    scripts/update-blame.sh:
      Paul Gauthier (aider): 3
    scripts/update-docs.sh:
      Paul Gauthier: 1
    tests/basic/test_analytics.py:
      Paul Gauthier (aider): 19
    tests/basic/test_main.py:
      Paul Gauthier (aider): 7
    tests/basic/test_sanity_check_repo.py:
      mdk: 28
  grand_total:
    Evan Johnson: 2
    Paul Gauthier: 265
    Paul Gauthier (aider): 875
    apaz-cli: 3
    mdk: 34
  start_tag: v0.69.0
  total_lines: 1179
- aider_percentage: 60.36
  aider_total: 236
  end_date: '2025-01-10'
  end_tag: v0.71.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
    aider/coders/base_coder.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 13
    aider/commands.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 22
    aider/io.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 16
    aider/linter.py:
      Aaron Weisberg: 5
    aider/main.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 13
      apaz-cli: 18
    aider/mdstream.py:
      Paul Gauthier: 38
      Paul Gauthier (aider): 58
    aider/models.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 2
    aider/repo.py:
      Krazer: 10
      Paul Gauthier: 5
    aider/run_cmd.py:
      Aaron Weisberg: 2
    aider/utils.py:
      Paul Gauthier: 9
    aider/voice.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 13
    aider/watch.py:
      Paul Gauthier: 1
    benchmark/Dockerfile:
      Josh Vera: 1
      Paul Maunders: 12
    benchmark/benchmark.py:
      Nimesh Ghelani: 1
      Paul Gauthier: 6
      Paul Gauthier (aider): 30
    benchmark/problem_stats.py:
      Paul Gauthier (aider): 5
    docker/Dockerfile:
      Paul Gauthier (aider): 32
    scripts/update-history.py:
      Paul Gauthier (aider): 1
    tests/basic/test_commands.py:
      Paul Gauthier: 2
    tests/basic/test_io.py:
      Paul Gauthier (aider): 6
    tests/basic/test_linter.py:
      Aaron Weisberg: 2
    tests/basic/test_models.py:
      Paul Gauthier (aider): 25
  grand_total:
    Aaron Weisberg: 9
    Josh Vera: 1
    Krazer: 10
    Nimesh Ghelani: 1
    Paul Gauthier: 104
    Paul Gauthier (aider): 236
    Paul Maunders: 12
    apaz-cli: 18
  start_tag: v0.70.0
  total_lines: 391
- aider_percentage: 48.76
  aider_total: 138
  end_date: '2025-01-20'
  end_tag: v0.72.0
  file_counts:
    .github/workflows/docker-build-test.yml:
      Paul Gauthier (aider): 38
    .github/workflows/pages.yml:
      Paul Gauthier: 3
      Paul Gauthier (aider): 1
    .github/workflows/ubuntu-tests.yml:
      Paul Gauthier (aider): 8
    .github/workflows/windows-tests.yml:
      Paul Gauthier (aider): 8
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Titusz Pan: 6
    aider/coders/base_coder.py:
      Paul Gauthier: 11
    aider/coders/single_wholefile_func_coder.py:
      Paul Gauthier: 1
    aider/coders/wholefile_func_coder.py:
      Paul Gauthier: 1
    aider/commands.py:
      Paul Gauthier: 3
    aider/history.py:
      Paul Gauthier: 7
    aider/io.py:
      Paul Gauthier (aider): 14
      Titusz Pan: 2
    aider/main.py:
      Titusz Pan: 1
    aider/models.py:
      Paul Gauthier: 16
    aider/queries/tree-sitter-kotlin-tags.scm:
      Paul Walker: 27
    aider/repomap.py:
      Paul Gauthier (aider): 2
    aider/sendchat.py:
      Paul Gauthier: 9
      Paul Gauthier (aider): 22
    aider/utils.py:
      Paul Gauthier: 1
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 2
    benchmark/benchmark.py:
      Paul Gauthier: 9
    benchmark/rsync.sh:
      Paul Gauthier: 21
    docker/Dockerfile:
      Paul Gauthier: 2
      Paul Gauthier (aider): 6
    scripts/my_models.py:
      Paul Gauthier: 3
    scripts/update-docs.sh:
      Paul Gauthier: 2
    tests/basic/test_io.py:
      Paul Gauthier (aider): 39
    tests/basic/test_repomap.py:
      Paul Walker: 1
    tests/fixtures/languages/kotlin/test.kt:
      Paul Walker: 16
  grand_total:
    Paul Gauthier: 92
    Paul Gauthier (aider): 138
    Paul Walker: 44
    Titusz Pan: 9
  start_tag: v0.71.0
  total_lines: 283
- aider_percentage: 37.47
  aider_total: 284
  end_date: '2025-01-31'
  end_tag: v0.73.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 2
    aider/coders/base_coder.py:
      Paul Gauthier: 37
      Paul Gauthier (aider): 26
    aider/commands.py:
      xqyz: 1
    aider/io.py:
      Paul Gauthier: 7
    aider/main.py:
      Paul Gauthier: 13
      Paul Gauthier (aider): 15
    aider/models.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 33
    aider/resources/model-settings.yml:
      Paul Gauthier: 334
      kennyfrc: 11
      xqyz: 4
    aider/sendchat.py:
      Mir Adnan ALI: 28
      Paul Gauthier: 11
      Paul Gauthier (aider): 6
    aider/urls.py:
      Paul Gauthier: 1
    aider/website/_includes/leaderboard.js:
      Paul Gauthier (aider): 1
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 3
      Paul Gauthier (aider): 2
    benchmark/benchmark.py:
      Paul Gauthier (aider): 21
    benchmark/rsync.sh:
      Paul Gauthier: 2
    tests/basic/test_coder.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 39
    tests/basic/test_main.py:
      Paul Gauthier (aider): 62
    tests/basic/test_sendchat.py:
      Paul Gauthier (aider): 77
  grand_total:
    Mir Adnan ALI: 28
    Paul Gauthier: 430
    Paul Gauthier (aider): 284
    kennyfrc: 11
    xqyz: 5
  start_tag: v0.72.0
  total_lines: 758
- aider_percentage: 76.07
  aider_total: 604
  end_date: '2025-02-06'
  end_tag: v0.74.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 24
      Paul Gauthier (aider): 9
    aider/coders/editblock_coder.py:
      Paul Gauthier: 5
    aider/coders/wholefile_coder.py:
      Paul Gauthier: 2
    aider/commands.py:
      Paul Gauthier: 1
    aider/exceptions.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 6
    aider/history.py:
      Paul Gauthier (aider): 1
    aider/io.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 18
    aider/llm.py:
      Paul Gauthier: 3
    aider/main.py:
      Paul Gauthier: 21
      Paul Gauthier (aider): 25
    aider/models.py:
      Paul Gauthier: 83
      Paul Gauthier (aider): 77
    aider/repo.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 2
      "Viktor Sz\xE9pe": 3
    aider/resources/model-settings.yml:
      Paul Gauthier: 11
    aider/watch.py:
      Paul Gauthier (aider): 45
    benchmark/docker.sh:
      Paul Gauthier: 2
    docker/Dockerfile:
      Paul Gauthier: 5
      Paul Gauthier (aider): 4
    tests/basic/test_editblock.py:
      Paul Gauthier: 7
    tests/basic/test_history.py:
      Paul Gauthier (aider): 13
    tests/basic/test_io.py:
      Paul Gauthier (aider): 46
    tests/basic/test_main.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 1
    tests/basic/test_models.py:
      Paul Gauthier (aider): 297
    tests/basic/test_repo.py:
      Paul Gauthier (aider): 11
    tests/basic/test_sendchat.py:
      Paul Gauthier (aider): 7
    tests/basic/test_watch.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 42
  grand_total:
    Paul Gauthier: 187
    Paul Gauthier (aider): 604
    "Viktor Sz\xE9pe": 3
  start_tag: v0.73.0
  total_lines: 794
- aider_percentage: 44.78
  aider_total: 163
  end_date: '2025-02-24'
  end_tag: v0.75.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 7
    aider/coders/base_coder.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 4
    aider/commands.py:
      FeepingCreature (aider): 6
    aider/editor.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 5
    aider/io.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 4
    aider/linter.py:
      Paul Gauthier: 1
    aider/main.py:
      Paul Gauthier: 16
    aider/models.py:
      Paul Gauthier: 4
    aider/queries/tree-sitter-language-pack/javascript-tags.scm:
      Paul Gauthier: 5
    aider/queries/tree-sitter-languages/hcl-tags.scm:
      Paul Gauthier: 3
      Warren Krewenki: 74
    aider/queries/tree-sitter-languages/javascript-tags.scm:
      Paul Gauthier: 5
    aider/repomap.py:
      Paul Gauthier: 43
      Paul Gauthier (aider): 11
    aider/resources/model-settings.yml:
      Paul Gauthier: 12
    aider/special.py:
      Lucas Shadler: 1
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    benchmark/Dockerfile:
      Paul Gauthier (aider): 1
    benchmark/benchmark.py:
      Paul Gauthier: 4
    benchmark/cpp-test.sh:
      Paul Gauthier: 1
    scripts/blame.py:
      Paul Gauthier (aider): 2
    scripts/issues.py:
      Paul Gauthier (aider): 17
    tests/basic/test_coder.py:
      Paul Gauthier (aider): 18
    tests/basic/test_editor.py:
      Antti Kaihola: 1
      Paul Gauthier (aider): 41
    tests/basic/test_models.py:
      Paul Gauthier (aider): 1
    tests/basic/test_repomap.py:
      Paul Gauthier (aider): 1
    tests/fixtures/languages/hcl/test.tf:
      Paul Gauthier (aider): 52
  grand_total:
    Antti Kaihola: 1
    FeepingCreature (aider): 6
    Lucas Shadler: 1
    Paul Gauthier: 125
    Paul Gauthier (aider): 157
    Warren Krewenki: 74
  start_tag: v0.74.0
  total_lines: 364
- aider_percentage: 84.75
  aider_total: 1589
  end_date: '2025-03-10'
  end_tag: v0.76.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 25
    aider/args_formatter.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 3
    aider/coders/base_coder.py:
      Paul Gauthier: 54
      Paul Gauthier (aider): 29
    aider/deprecated.py:
      Paul Gauthier (aider): 107
    aider/io.py:
      Paul Gauthier: 7
      Paul Gauthier (aider): 127
    aider/main.py:
      Akira Komamura: 2
      Mattias: 1
      Paul Gauthier: 4
      Paul Gauthier (aider): 16
    aider/models.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 68
    aider/queries/tree-sitter-language-pack/csharp-tags.scm:
      Paul Gauthier: 14
      Paul Gauthier (aider): 12
    aider/reasoning_tags.py:
      Paul Gauthier: 14
      Paul Gauthier (aider): 68
    aider/repo.py:
      Akira Komamura: 1
      Paul Gauthier (aider): 4
    aider/repomap.py:
      Paul Gauthier: 9
    aider/resources/model-settings.yml:
      Paul Gauthier: 61
      Paul Gauthier (aider): 32
      gmoz22: 4
    aider/website/_includes/leaderboard.js:
      Paul Gauthier (aider): 48
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 2
    benchmark/benchmark.py:
      Paul Gauthier: 1
    benchmark/problem_stats.py:
      Paul Gauthier (aider): 2
    docker/Dockerfile:
      Paul Gauthier: 1
    scripts/blame.py:
      Paul Gauthier: 1
    scripts/pip-compile.sh:
      Claudia Pellegrino: 10
      Paul Gauthier: 6
      Paul Gauthier (aider): 11
    scripts/update-history.py:
      Paul Gauthier: 1
    scripts/versionbump.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 64
    tests/basic/test_deprecated.py:
      Paul Gauthier: 10
      Paul Gauthier (aider): 130
    tests/basic/test_io.py:
      Paul Gauthier (aider): 54
    tests/basic/test_main.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 93
    tests/basic/test_model_info_manager.py:
      Paul Gauthier (aider): 72
    tests/basic/test_models.py:
      Paul Gauthier: 27
      Paul Gauthier (aider): 34
    tests/basic/test_reasoning.py:
      Paul Gauthier: 36
      Paul Gauthier (aider): 525
    tests/basic/test_repomap.py:
      Paul Gauthier: 2
    tests/basic/test_ssl_verification.py:
      Paul Gauthier (aider): 65
  grand_total:
    Akira Komamura: 3
    Claudia Pellegrino: 10
    Mattias: 1
    Paul Gauthier: 268
    Paul Gauthier (aider): 1589
    gmoz22: 4
  start_tag: v0.75.0
  total_lines: 1875
- aider_percentage: 71.93
  aider_total: 1399
  end_date: '2025-03-13'
  end_tag: v0.77.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier (aider): 5
    aider/coders/architect_coder.py:
      Paul Gauthier (aider): 2
    aider/coders/base_coder.py:
      Paul Gauthier (aider): 14
    aider/commands.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 71
    aider/deprecated.py:
      Paul Gauthier: 2
    aider/io.py:
      Paul Gauthier (aider): 5
    aider/main.py:
      Paul Gauthier (aider): 12
    aider/models.py:
      Paul Gauthier (aider): 83
    aider/queries/tree-sitter-language-pack/arduino-tags.scm:
      Paul Gauthier: 3
      Paul Gauthier (aider): 2
    aider/queries/tree-sitter-language-pack/c-tags.scm:
      Paul Gauthier: 4
      Paul Gauthier (aider): 5
    aider/queries/tree-sitter-language-pack/chatito-tags.scm:
      Paul Gauthier: 11
      Paul Gauthier (aider): 5
    aider/queries/tree-sitter-language-pack/commonlisp-tags.scm:
      Paul Gauthier: 116
      Paul Gauthier (aider): 6
    aider/queries/tree-sitter-language-pack/cpp-tags.scm:
      Paul Gauthier: 7
      Paul Gauthier (aider): 8
    aider/queries/tree-sitter-language-pack/d-tags.scm:
      Paul Gauthier: 9
      Paul Gauthier (aider): 17
    aider/queries/tree-sitter-language-pack/dart-tags.scm:
      Paul Gauthier: 42
      Paul Gauthier (aider): 19
    aider/queries/tree-sitter-language-pack/elisp-tags.scm:
      Paul Gauthier: 1
      Paul Gauthier (aider): 2
    aider/queries/tree-sitter-language-pack/elixir-tags.scm:
      Paul Gauthier: 10
      Paul Gauthier (aider): 8
    aider/queries/tree-sitter-language-pack/elm-tags.scm:
      Paul Gauthier: 8
      Paul Gauthier (aider): 11
    aider/queries/tree-sitter-language-pack/gleam-tags.scm:
      Paul Gauthier: 26
      Paul Gauthier (aider): 15
    aider/queries/tree-sitter-language-pack/go-tags.scm:
      Paul Gauthier: 14
      Paul Gauthier (aider): 14
    aider/queries/tree-sitter-language-pack/java-tags.scm:
      Paul Gauthier: 10
      Paul Gauthier (aider): 7
    aider/queries/tree-sitter-language-pack/lua-tags.scm:
      Paul Gauthier: 25
      Paul Gauthier (aider): 9
    aider/queries/tree-sitter-language-pack/pony-tags.scm:
      Paul Gauthier: 20
      Paul Gauthier (aider): 19
    aider/queries/tree-sitter-language-pack/properties-tags.scm:
      Paul Gauthier: 3
      Paul Gauthier (aider): 2
    aider/queries/tree-sitter-language-pack/python-tags.scm:
      Paul Gauthier: 9
      Paul Gauthier (aider): 5
    aider/queries/tree-sitter-language-pack/r-tags.scm:
      Paul Gauthier: 17
      Paul Gauthier (aider): 4
    aider/queries/tree-sitter-language-pack/racket-tags.scm:
      Paul Gauthier: 10
      Paul Gauthier (aider): 2
    aider/queries/tree-sitter-language-pack/ruby-tags.scm:
      Paul Gauthier: 23
      Paul Gauthier (aider): 12
    aider/queries/tree-sitter-language-pack/rust-tags.scm:
      Paul Gauthier: 41
      Paul Gauthier (aider): 14
    aider/queries/tree-sitter-language-pack/solidity-tags.scm:
      Paul Gauthier: 30
      Paul Gauthier (aider): 13
    aider/queries/tree-sitter-language-pack/swift-tags.scm:
      Paul Gauthier: 39
      Paul Gauthier (aider): 12
    aider/queries/tree-sitter-language-pack/udev-tags.scm:
      Paul Gauthier: 15
      Paul Gauthier (aider): 5
    aider/resources/model-settings.yml:
      Paul Gauthier: 9
    aider/watch.py:
      Yutaka Matsubara: 4
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 3
      Paul Gauthier (aider): 8
    scripts/redact-cast.py:
      Paul Gauthier: 27
      Paul Gauthier (aider): 98
    scripts/tsl_pack_langs.py:
      Paul Gauthier (aider): 145
    scripts/versionbump.py:
      Paul Gauthier (aider): 1
    tests/basic/test_coder.py:
      Paul Gauthier (aider): 104
    tests/basic/test_commands.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 190
    tests/basic/test_models.py:
      Paul Gauthier (aider): 44
    tests/basic/test_repomap.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 125
    tests/fixtures/languages/arduino/test.ino:
      Paul Gauthier (aider): 21
    tests/fixtures/languages/c/test.c:
      Paul Gauthier (aider): 12
    tests/fixtures/languages/chatito/test.chatito:
      Paul Gauthier (aider): 20
    tests/fixtures/languages/commonlisp/test.lisp:
      Paul Gauthier (aider): 17
    tests/fixtures/languages/d/test.d:
      Paul Gauthier (aider): 26
    tests/fixtures/languages/dart/test.dart:
      Paul Gauthier (aider): 21
    tests/fixtures/languages/elm/test.elm:
      Paul Gauthier (aider): 16
    tests/fixtures/languages/gleam/test.gleam:
      Paul Gauthier (aider): 10
    tests/fixtures/languages/lua/test.lua:
      Paul Gauthier (aider): 25
    tests/fixtures/languages/pony/test.pony:
      Paul Gauthier (aider): 8
    tests/fixtures/languages/properties/test.properties:
      Paul Gauthier (aider): 14
    tests/fixtures/languages/r/test.r:
      Paul Gauthier (aider): 17
    tests/fixtures/languages/racket/test.rkt:
      Paul Gauthier (aider): 8
    tests/fixtures/languages/solidity/test.sol:
      Paul Gauthier (aider): 21
    tests/fixtures/languages/swift/test.swift:
      Paul Gauthier (aider): 18
    tests/fixtures/languages/udev/test.rules:
      Paul Gauthier (aider): 22
  grand_total:
    Paul Gauthier: 542
    Paul Gauthier (aider): 1399
    Yutaka Matsubara: 4
  start_tag: v0.76.0
  total_lines: 1945
- aider_percentage: 91.82
  aider_total: 2682
  end_date: '2025-03-21'
  end_tag: v0.78.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args.py:
      Paul Gauthier (aider): 24
      Yutaka Matsubara: 2
    aider/coders/base_coder.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 6
    aider/commands.py:
      Carles Sala (aider): 30
      Paul Gauthier (aider): 10
    aider/help_pats.py:
      Paul Gauthier: 6
    aider/io.py:
      Marco Mayer: 2
      Paul Gauthier (aider): 17
    aider/main.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 29
    aider/mdstream.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 22
    aider/models.py:
      Paul Gauthier (aider): 41
      lentil32 (aider): 15
    aider/repo.py:
      Paul Gauthier (aider): 5
    aider/resources/model-settings.yml:
      Paul Gauthier: 3
      Paul Gauthier (aider): 22
    aider/website/_includes/head_custom.html:
      Paul Gauthier: 3
      Paul Gauthier (aider): 53
    aider/website/_includes/recording.js:
      Paul Gauthier: 4
      Paul Gauthier (aider): 424
    aider/website/assets/asciinema/asciinema-player.min.js:
      Paul Gauthier: 1
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    aider/website/index.html:
      Paul Gauthier: 173
      Paul Gauthier (aider): 371
    scripts/badges.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 496
    scripts/blame.py:
      Paul Gauthier: 2
    scripts/jekyll_run.sh:
      Paul Gauthier: 1
      Paul Gauthier (aider): 5
    scripts/logo_svg.py:
      Paul Gauthier: 5
      Paul Gauthier (aider): 169
    scripts/recording_audio.py:
      Paul Gauthier (aider): 338
    scripts/redact-cast.py:
      Paul Gauthier: 22
      Paul Gauthier (aider): 37
    scripts/tmux_record.sh:
      Paul Gauthier: 1
      Paul Gauthier (aider): 17
    scripts/update-docs.sh:
      Paul Gauthier: 1
    scripts/update-history.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 52
    tests/basic/test_aws_credentials.py:
      lentil32 (aider): 169
    tests/basic/test_commands.py:
      Carles Sala (aider): 40
    tests/basic/test_main.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 193
    tests/basic/test_repo.py:
      Paul Gauthier (aider): 48
    tests/help/test_help.py:
      Paul Gauthier (aider): 49
  grand_total:
    Carles Sala (aider): 70
    Marco Mayer: 2
    Paul Gauthier: 235
    Paul Gauthier (aider): 2428
    Yutaka Matsubara: 2
    lentil32 (aider): 184
  start_tag: v0.77.0
  total_lines: 2921
- aider_percentage: 65.38
  aider_total: 221
  end_date: '2025-03-25'
  end_tag: v0.79.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/__init__.py:
      Paul Gauthier: 2
    aider/coders/base_coder.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 5
    aider/coders/context_coder.py:
      Paul Gauthier: 45
      Paul Gauthier (aider): 8
    aider/commands.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 20
    aider/io.py:
      Paul Gauthier: 11
      Paul Gauthier (aider): 2
    aider/main.py:
      Paul Gauthier (aider): 4
    aider/models.py:
      Paul Gauthier: 3
      Paul Gauthier (aider): 1
    aider/repomap.py:
      Paul Gauthier: 17
    aider/resources/model-settings.yml:
      Paul Gauthier: 13
      Paul Gauthier (aider): 10
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    aider/website/index.html:
      Paul Gauthier: 3
      Paul Gauthier (aider): 16
    scripts/badges.py:
      Paul Gauthier (aider): 2
    scripts/blame.py:
      Paul Gauthier (aider): 16
    scripts/dl_icons.py:
      Paul Gauthier (aider): 60
    scripts/tmux_record.sh:
      Paul Gauthier: 1
    tests/basic/test_coder.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 77
  grand_total:
    Paul Gauthier: 117
    Paul Gauthier (aider): 221
  start_tag: v0.78.0
  total_lines: 338
- aider_percentage: 86.86
  aider_total: 1837
  end_date: '2025-03-31'
  end_tag: v0.80.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier: 2
    aider/commands.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 20
    aider/exceptions.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 3
    aider/io.py:
      Andrey Ivanov: 2
      Matteo Landi (aider): 11
      Paul Gauthier (aider): 38
    aider/linter.py:
      Mir Adnan ALI: 2
    aider/main.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 21
    aider/mdstream.py:
      Peter Schilling (aider) (aider): 25
    aider/models.py:
      Paul Gauthier: 12
      Paul Gauthier (aider): 9
    aider/onboarding.py:
      Paul Gauthier: 44
      Paul Gauthier (aider): 389
    aider/queries/tree-sitter-languages/scala-tags.scm:
      Vasil Markoukin: 65
    aider/repo.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 7
    aider/repomap.py:
      Paul Gauthier (aider): 19
    aider/resources/model-settings.yml:
      Paul Gauthier (aider): 13
    aider/scrape.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 1
    aider/utils.py:
      Paul Gauthier (aider): 5
    aider/watch.py:
      Matteo Landi (aider): 2
    aider/website/_includes/leaderboard.js:
      Paul Gauthier: 1
      Paul Gauthier (aider): 2
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    aider/website/index.html:
      Paul Gauthier: 51
      Paul Gauthier (aider): 175
    scripts/30k-image.py:
      Paul Gauthier: 8
      Paul Gauthier (aider): 227
    scripts/homepage.py:
      Paul Gauthier (aider): 122
    tests/basic/test_commands.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 48
    tests/basic/test_exceptions.py:
      Paul Gauthier (aider): 17
    tests/basic/test_io.py:
      Paul Gauthier (aider): 28
    tests/basic/test_main.py:
      Paul Gauthier: 15
      Paul Gauthier (aider): 199
    tests/basic/test_onboarding.py:
      Paul Gauthier (aider): 439
    tests/basic/test_repomap.py:
      Vasil Markoukin: 3
    tests/basic/test_ssl_verification.py:
      Paul Gauthier (aider): 8
    tests/basic/test_watch.py:
      Matteo Landi (aider): 9
    tests/fixtures/languages/scala/test.scala:
      Vasil Markoukin: 61
  grand_total:
    Andrey Ivanov: 2
    Matteo Landi (aider): 22
    Mir Adnan ALI: 2
    Paul Gauthier: 145
    Paul Gauthier (aider): 1790
    Peter Schilling (aider) (aider): 25
    Vasil Markoukin: 129
  start_tag: v0.79.0
  total_lines: 2115
- aider_percentage: 85.55
  aider_total: 225
  end_date: '2025-04-04'
  end_tag: v0.81.0
  file_counts:
    .github/workflows/check_pypi_version.yml:
      Paul Gauthier: 11
      Paul Gauthier (aider): 75
    .github/workflows/windows_check_pypi_version.yml:
      Paul Gauthier: 4
      Paul Gauthier (aider): 86
    aider/__init__.py:
      Paul Gauthier: 1
    aider/coders/base_coder.py:
      Paul Gauthier (aider): 4
    aider/exceptions.py:
      Paul Gauthier: 6
      Paul Gauthier (aider): 12
    aider/main.py:
      Paul Gauthier (aider): 40
    aider/models.py:
      Paul Gauthier (aider): 2
    aider/resources/model-settings.yml:
      Paul Gauthier: 9
      Paul Gauthier (aider): 1
    aider/website/_includes/leaderboard.js:
      Paul Gauthier (aider): 5
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 1
    aider/website/index.html:
      Paul Gauthier: 3
    tests/basic/test_exceptions.py:
      Paul Gauthier: 3
  grand_total:
    Paul Gauthier: 38
    Paul Gauthier (aider): 225
  start_tag: v0.80.0
  total_lines: 263
- aider_percentage: 91.85
  aider_total: 1567
  end_date: '2025-04-14'
  end_tag: v0.82.0
  file_counts:
    aider/__init__.py:
      Paul Gauthier: 1
    aider/args_formatter.py:
      Paul Gauthier (aider): 4
    aider/coders/__init__.py:
      Paul Gauthier (aider): 4
    aider/coders/base_coder.py:
      Paul Gauthier: 4
      Paul Gauthier (aider): 5
    aider/coders/editor_diff_fenced_coder.py:
      Paul Gauthier (aider): 9
    aider/coders/patch_coder.py:
      Paul Gauthier (aider): 679
    aider/coders/search_replace.py:
      Paul Gauthier (aider): 1
    aider/main.py:
      Paul Gauthier (aider): 1
    aider/models.py:
      Paul Gauthier: 1
      Paul Gauthier (aider): 25
    aider/resources/model-settings.yml:
      Felix Lisczyk: 13
      Paul Gauthier: 37
      Paul Gauthier (aider): 68
    aider/website/_includes/leaderboard.js:
      Paul Gauthier: 38
      Paul Gauthier (aider): 6
    aider/website/_includes/leaderboard_table.js:
      Paul Gauthier (aider): 518
    aider/website/docs/leaderboards/index.md:
      Paul Gauthier: 15
      Paul Gauthier (aider): 209
    aider/website/index.html:
      Paul Gauthier: 28
    scripts/homepage.py:
      Paul Gauthier (aider): 2
    scripts/versionbump.py:
      Paul Gauthier (aider): 11
    tests/basic/test_coder.py:
      Paul Gauthier: 2
      Paul Gauthier (aider): 25
  grand_total:
    Felix Lisczyk: 13
    Paul Gauthier: 126
    Paul Gauthier (aider): 1567
  start_tag: v0.81.0
  total_lines: 1706
