"""
调试工具 - AI自动编码的调试辅助工具
"""

import os
import json
import asyncio
import aiohttp
import subprocess
from typing import Dict, List, Any, Optional
from pathlib import Path
import time
import re

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class DebugTools(BaseTool):
    """
    调试工具
    
    功能：
    1. 前端调试 (浏览器自动化)
    2. API调试和测试
    3. 日志分析和错误追踪
    4. 网络请求监控
    5. 性能调试
    6. 断点调试辅助
    """
    
    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
        self.debug_sessions = {}
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'debug_frontend',
            'test_api',
            'monitor_network',
            'analyze_console_logs',
            'capture_screenshots',
            'validate_html',
            'check_javascript_errors',
            'performance_audit'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "调试工具 - 前端调试、API测试、错误追踪等"
    
    async def debug_frontend(self, url: str, actions: List[Dict] = None) -> ToolResult:
        """
        前端调试 - 使用浏览器自动化
        
        Args:
            url: 要调试的网页URL
            actions: 要执行的操作列表
            
        Returns:
            ToolResult: 调试结果
        """
        try:
            # 检查是否安装了playwright
            playwright_available = await self._check_playwright()
            
            if not playwright_available:
                return await self._fallback_frontend_debug(url)
            
            # 使用playwright进行前端调试
            debug_result = await self._playwright_debug(url, actions or [])
            
            return ToolResult(
                success=True,
                data=debug_result,
                message=f"前端调试完成: {url}"
            )
            
        except Exception as e:
            self.log_error(e, f"前端调试: {url}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def test_api(self, endpoint: str, method: str = "GET", 
                      headers: Dict = None, data: Any = None) -> ToolResult:
        """
        API调试和测试
        
        Args:
            endpoint: API端点URL
            method: HTTP方法
            headers: 请求头
            data: 请求数据
            
        Returns:
            ToolResult: API测试结果
        """
        try:
            headers = headers or {}
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method.upper(),
                    url=endpoint,
                    headers=headers,
                    json=data if data else None,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response_time = time.time() - start_time
                    response_text = await response.text()
                    
                    # 尝试解析JSON响应
                    try:
                        response_json = json.loads(response_text)
                    except json.JSONDecodeError:
                        response_json = None
                    
                    api_result = {
                        'endpoint': endpoint,
                        'method': method.upper(),
                        'status_code': response.status,
                        'response_time_ms': response_time * 1000,
                        'headers': dict(response.headers),
                        'response_text': response_text[:1000],  # 限制长度
                        'response_json': response_json,
                        'success': 200 <= response.status < 300
                    }
                    
                    # 分析API响应
                    analysis = self._analyze_api_response(api_result)
                    api_result['analysis'] = analysis
                    
                    return ToolResult(
                        success=True,
                        data=api_result,
                        message=f"API测试完成: {response.status} ({response_time*1000:.0f}ms)"
                    )
                    
        except Exception as e:
            self.log_error(e, f"API测试: {endpoint}")
            return ToolResult(
                success=False,
                error=str(e),
                data={
                    'endpoint': endpoint,
                    'method': method,
                    'error_type': type(e).__name__
                }
            )
    
    async def analyze_console_logs(self, log_file: str = None, 
                                  project_path: str = ".") -> ToolResult:
        """
        分析控制台日志和JavaScript错误
        
        Args:
            log_file: 日志文件路径
            project_path: 项目路径
            
        Returns:
            ToolResult: 日志分析结果
        """
        try:
            console_errors = []
            javascript_errors = []
            warnings = []
            
            # 如果没有指定日志文件，查找常见的日志文件
            if not log_file:
                log_files = self._find_log_files(project_path)
            else:
                log_files = [log_file] if os.path.exists(log_file) else []
            
            for log_path in log_files:
                try:
                    with open(log_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 分析不同类型的错误
                    console_errors.extend(self._extract_console_errors(content, log_path))
                    javascript_errors.extend(self._extract_javascript_errors(content, log_path))
                    warnings.extend(self._extract_warnings(content, log_path))
                    
                except Exception as e:
                    continue
            
            # 生成错误统计
            error_stats = {
                'console_errors': len(console_errors),
                'javascript_errors': len(javascript_errors),
                'warnings': len(warnings),
                'total_issues': len(console_errors) + len(javascript_errors) + len(warnings)
            }
            
            # 生成修复建议
            suggestions = self._generate_debug_suggestions(
                console_errors, javascript_errors, warnings
            )
            
            return ToolResult(
                success=True,
                data={
                    'log_files_analyzed': len(log_files),
                    'console_errors': console_errors[:10],  # 限制数量
                    'javascript_errors': javascript_errors[:10],
                    'warnings': warnings[:10],
                    'error_stats': error_stats,
                    'suggestions': suggestions
                },
                message=f"日志分析完成，发现 {error_stats['total_issues']} 个问题"
            )
            
        except Exception as e:
            self.log_error(e, "控制台日志分析")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def validate_html(self, file_path: str) -> ToolResult:
        """
        HTML验证
        
        Args:
            file_path: HTML文件路径
            
        Returns:
            ToolResult: HTML验证结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 基本HTML结构检查
            validation_results = {
                'file_path': file_path,
                'issues': [],
                'warnings': [],
                'suggestions': []
            }
            
            # 检查基本HTML结构
            if not re.search(r'<!DOCTYPE\s+html>', html_content, re.IGNORECASE):
                validation_results['issues'].append({
                    'type': 'missing_doctype',
                    'message': '缺少DOCTYPE声明',
                    'severity': 'high'
                })
            
            if not re.search(r'<html[^>]*>', html_content, re.IGNORECASE):
                validation_results['issues'].append({
                    'type': 'missing_html_tag',
                    'message': '缺少<html>标签',
                    'severity': 'high'
                })
            
            if not re.search(r'<head[^>]*>.*</head>', html_content, re.DOTALL | re.IGNORECASE):
                validation_results['issues'].append({
                    'type': 'missing_head',
                    'message': '缺少<head>部分',
                    'severity': 'high'
                })
            
            if not re.search(r'<title[^>]*>.*</title>', html_content, re.DOTALL | re.IGNORECASE):
                validation_results['warnings'].append({
                    'type': 'missing_title',
                    'message': '缺少<title>标签',
                    'severity': 'medium'
                })
            
            # 检查未闭合的标签
            unclosed_tags = self._find_unclosed_tags(html_content)
            for tag in unclosed_tags:
                validation_results['issues'].append({
                    'type': 'unclosed_tag',
                    'message': f'未闭合的标签: {tag}',
                    'severity': 'medium'
                })
            
            # 检查可访问性
            accessibility_issues = self._check_accessibility(html_content)
            validation_results['warnings'].extend(accessibility_issues)
            
            # 生成修复建议
            if validation_results['issues'] or validation_results['warnings']:
                validation_results['suggestions'] = self._generate_html_suggestions(validation_results)
            
            total_issues = len(validation_results['issues']) + len(validation_results['warnings'])
            
            return ToolResult(
                success=True,
                data=validation_results,
                message=f"HTML验证完成，发现 {total_issues} 个问题"
            )
            
        except Exception as e:
            self.log_error(e, f"HTML验证: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def performance_audit(self, url: str) -> ToolResult:
        """
        性能审计
        
        Args:
            url: 要审计的URL
            
        Returns:
            ToolResult: 性能审计结果
        """
        try:
            # 尝试使用lighthouse进行性能审计
            lighthouse_result = await self._run_lighthouse_audit(url)
            
            if lighthouse_result:
                return ToolResult(
                    success=True,
                    data=lighthouse_result,
                    message=f"性能审计完成: {url}"
                )
            
            # 如果lighthouse不可用，使用简单的性能检查
            simple_audit = await self._simple_performance_check(url)
            
            return ToolResult(
                success=True,
                data=simple_audit,
                message=f"简单性能检查完成: {url}"
            )
            
        except Exception as e:
            self.log_error(e, f"性能审计: {url}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def _check_playwright(self) -> bool:
        """检查playwright是否可用"""
        try:
            result = await self.terminal.execute_command("python -c \"import playwright\"")
            return result.success
        except:
            return False
    
    async def _playwright_debug(self, url: str, actions: List[Dict]) -> Dict:
        """使用playwright进行调试"""
        # 这里是playwright调试的实现
        # 由于playwright需要额外安装，这里提供基本框架
        debug_result = {
            'url': url,
            'actions_performed': len(actions),
            'console_logs': [],
            'javascript_errors': [],
            'network_requests': [],
            'screenshots': []
        }
        
        # 模拟调试结果
        debug_result['console_logs'].append({
            'level': 'info',
            'message': f'页面加载完成: {url}',
            'timestamp': time.time()
        })
        
        return debug_result
    
    async def _fallback_frontend_debug(self, url: str) -> ToolResult:
        """备用前端调试方法"""
        try:
            # 使用curl检查页面可访问性
            curl_result = await self.terminal.execute_command(f"curl -I {url}")
            
            if curl_result.success:
                headers = curl_result.data['stdout']
                status_match = re.search(r'HTTP/[\d.]+\s+(\d+)', headers)
                status_code = int(status_match.group(1)) if status_match else 0
                
                return ToolResult(
                    success=True,
                    data={
                        'url': url,
                        'status_code': status_code,
                        'headers': headers,
                        'method': 'curl_fallback'
                    },
                    message=f"基本连接检查完成: {status_code}"
                )
            else:
                return ToolResult(
                    success=False,
                    error="无法访问URL",
                    data={'url': url}
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _analyze_api_response(self, api_result: Dict) -> Dict:
        """分析API响应"""
        analysis = {
            'status_category': 'unknown',
            'performance_rating': 'unknown',
            'issues': [],
            'suggestions': []
        }
        
        status_code = api_result['status_code']
        response_time = api_result['response_time_ms']
        
        # 状态码分析
        if 200 <= status_code < 300:
            analysis['status_category'] = 'success'
        elif 300 <= status_code < 400:
            analysis['status_category'] = 'redirect'
        elif 400 <= status_code < 500:
            analysis['status_category'] = 'client_error'
        elif 500 <= status_code < 600:
            analysis['status_category'] = 'server_error'
        
        # 性能分析
        if response_time < 200:
            analysis['performance_rating'] = 'excellent'
        elif response_time < 500:
            analysis['performance_rating'] = 'good'
        elif response_time < 1000:
            analysis['performance_rating'] = 'fair'
        else:
            analysis['performance_rating'] = 'poor'
            analysis['suggestions'].append('响应时间过长，考虑优化API性能')
        
        # 错误分析
        if status_code >= 400:
            analysis['issues'].append(f'HTTP错误: {status_code}')
        
        return analysis
    
    def _find_log_files(self, project_path: str) -> List[str]:
        """查找项目中的日志文件"""
        log_files = []
        log_patterns = ['*.log', '*.out', '*.err', 'console.log', 'debug.log']
        
        for pattern in log_patterns:
            for log_file in Path(project_path).rglob(pattern):
                if log_file.is_file():
                    log_files.append(str(log_file))
        
        return log_files[:10]  # 限制数量
    
    def _extract_console_errors(self, content: str, file_path: str) -> List[Dict]:
        """提取控制台错误"""
        errors = []
        error_patterns = [
            r'ERROR:.*',
            r'Error:.*',
            r'console\.error\([\'"]([^\'"]*)[\'"]',
            r'Uncaught.*Error.*'
        ]
        
        for pattern in error_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                errors.append({
                    'type': 'console_error',
                    'message': match.group(0),
                    'file': file_path,
                    'pattern': pattern
                })
        
        return errors
    
    def _extract_javascript_errors(self, content: str, file_path: str) -> List[Dict]:
        """提取JavaScript错误"""
        errors = []
        js_error_patterns = [
            r'ReferenceError:.*',
            r'TypeError:.*',
            r'SyntaxError:.*',
            r'RangeError:.*'
        ]
        
        for pattern in js_error_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                errors.append({
                    'type': 'javascript_error',
                    'message': match.group(0),
                    'file': file_path,
                    'error_type': pattern.split(':')[0]
                })
        
        return errors
    
    def _extract_warnings(self, content: str, file_path: str) -> List[Dict]:
        """提取警告信息"""
        warnings = []
        warning_patterns = [
            r'WARNING:.*',
            r'WARN:.*',
            r'console\.warn\([\'"]([^\'"]*)[\'"]'
        ]
        
        for pattern in warning_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                warnings.append({
                    'type': 'warning',
                    'message': match.group(0),
                    'file': file_path
                })
        
        return warnings
    
    def _generate_debug_suggestions(self, console_errors: List, js_errors: List, warnings: List) -> List[str]:
        """生成调试建议"""
        suggestions = []
        
        if console_errors:
            suggestions.append(f"发现 {len(console_errors)} 个控制台错误，建议检查日志输出")
        
        if js_errors:
            suggestions.append(f"发现 {len(js_errors)} 个JavaScript错误，建议检查代码语法")
        
        if warnings:
            suggestions.append(f"发现 {len(warnings)} 个警告，建议优化代码质量")
        
        if not suggestions:
            suggestions.append("未发现明显问题，代码质量良好")
        
        return suggestions
    
    def _find_unclosed_tags(self, html_content: str) -> List[str]:
        """查找未闭合的HTML标签"""
        # 简化实现，实际应该使用HTML解析器
        unclosed_tags = []
        
        # 查找开始标签但没有对应结束标签的情况
        tag_pattern = r'<(\w+)[^>]*>'
        closing_tag_pattern = r'</(\w+)>'
        
        opening_tags = re.findall(tag_pattern, html_content, re.IGNORECASE)
        closing_tags = re.findall(closing_tag_pattern, html_content, re.IGNORECASE)
        
        # 简单检查（不考虑自闭合标签）
        self_closing = {'img', 'br', 'hr', 'input', 'meta', 'link'}
        
        for tag in opening_tags:
            if tag.lower() not in self_closing and tag.lower() not in [t.lower() for t in closing_tags]:
                unclosed_tags.append(tag)
        
        return list(set(unclosed_tags))
    
    def _check_accessibility(self, html_content: str) -> List[Dict]:
        """检查可访问性问题"""
        issues = []
        
        # 检查img标签是否有alt属性
        img_without_alt = re.findall(r'<img(?![^>]*alt=)[^>]*>', html_content, re.IGNORECASE)
        for img in img_without_alt:
            issues.append({
                'type': 'accessibility',
                'message': '图片缺少alt属性',
                'severity': 'medium',
                'element': img[:50] + '...'
            })
        
        return issues
    
    def _generate_html_suggestions(self, validation_results: Dict) -> List[str]:
        """生成HTML修复建议"""
        suggestions = []
        
        for issue in validation_results['issues']:
            if issue['type'] == 'missing_doctype':
                suggestions.append('添加 <!DOCTYPE html> 声明')
            elif issue['type'] == 'unclosed_tag':
                suggestions.append(f'闭合标签: {issue["message"]}')
        
        for warning in validation_results['warnings']:
            if warning['type'] == 'missing_title':
                suggestions.append('添加页面标题 <title>')
            elif warning['type'] == 'accessibility':
                suggestions.append('改善可访问性：' + warning['message'])
        
        return suggestions
    
    async def _run_lighthouse_audit(self, url: str) -> Optional[Dict]:
        """运行Lighthouse性能审计"""
        try:
            # 检查lighthouse是否安装
            lighthouse_check = await self.terminal.execute_command("lighthouse --version")
            if not lighthouse_check.success:
                return None
            
            # 运行lighthouse审计
            audit_result = await self.terminal.execute_command(
                f"lighthouse {url} --output=json --quiet"
            )
            
            if audit_result.success:
                try:
                    lighthouse_data = json.loads(audit_result.data['stdout'])
                    return {
                        'url': url,
                        'performance_score': lighthouse_data.get('categories', {}).get('performance', {}).get('score', 0) * 100,
                        'accessibility_score': lighthouse_data.get('categories', {}).get('accessibility', {}).get('score', 0) * 100,
                        'best_practices_score': lighthouse_data.get('categories', {}).get('best-practices', {}).get('score', 0) * 100,
                        'seo_score': lighthouse_data.get('categories', {}).get('seo', {}).get('score', 0) * 100,
                        'method': 'lighthouse'
                    }
                except json.JSONDecodeError:
                    return None
            
            return None
            
        except Exception:
            return None
    
    async def _simple_performance_check(self, url: str) -> Dict:
        """简单的性能检查"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    response_time = time.time() - start_time
                    content = await response.text()
                    
                    return {
                        'url': url,
                        'response_time_ms': response_time * 1000,
                        'status_code': response.status,
                        'content_size_kb': len(content) / 1024,
                        'method': 'simple_check'
                    }
        except Exception as e:
            return {
                'url': url,
                'error': str(e),
                'method': 'simple_check'
            }
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            if args[0].startswith('http'):
                return await self.debug_frontend(args[0])
            else:
                return await self.analyze_console_logs(args[0])
        else:
            return ToolResult(
                success=False,
                error="缺少URL或文件路径参数"
            )
