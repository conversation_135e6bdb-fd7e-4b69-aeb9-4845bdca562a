"""
LLM重试处理器 - 增强LLM调用的容错和重试能力
"""

import asyncio
import logging
import time
import random
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class RetryReason(str, Enum):
    """重试原因"""
    EMPTY_RESPONSE = "empty_response"
    TIMEOUT = "timeout"
    RATE_LIMIT = "rate_limit"
    SERVER_ERROR = "server_error"
    NETWORK_ERROR = "network_error"
    CONTEXT_LIMIT = "context_limit"
    PROVIDER_ERROR = "provider_error"


@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    timeout: float = 120.0
    
    # 针对不同错误类型的特殊配置
    empty_response_retries: int = 5
    rate_limit_retries: int = 10
    server_error_retries: int = 3


@dataclass
class RetryAttempt:
    """重试尝试记录"""
    attempt: int
    reason: RetryReason
    delay: float
    timestamp: float
    error_message: str
    success: bool = False


class LLMRetryHandler:
    """
    LLM重试处理器
    
    功能：
    1. 智能重试机制
    2. 指数退避算法
    3. 错误分类和处理
    4. 降级策略
    5. 监控和统计
    """
    
    def __init__(self, config: RetryConfig = None):
        """初始化重试处理器"""
        self.config = config or RetryConfig()
        self.retry_history: List[RetryAttempt] = []
        self.success_count = 0
        self.failure_count = 0
        
        logger.info("LLM重试处理器初始化完成")
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        retry_on_empty: bool = True,
        fallback_func: Optional[Callable] = None,
        **kwargs
    ) -> Any:
        """
        执行函数并在失败时重试
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            retry_on_empty: 是否在空响应时重试
            fallback_func: 降级函数
            **kwargs: 函数关键字参数
            
        Returns:
            Any: 函数执行结果
        """
        last_error = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # 记录尝试开始
                start_time = time.time()
                
                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await asyncio.wait_for(
                        func(*args, **kwargs),
                        timeout=self.config.timeout
                    )
                else:
                    result = func(*args, **kwargs)
                
                # 检查结果
                if self._is_valid_response(result, retry_on_empty):
                    self.success_count += 1
                    self._record_success(attempt, start_time)
                    return result
                else:
                    # 空响应
                    reason = RetryReason.EMPTY_RESPONSE
                    error_msg = "Empty or invalid response from LLM"
                    
                    if attempt < self.config.empty_response_retries:
                        delay = self._calculate_delay(attempt, reason)
                        self._record_retry(attempt, reason, delay, error_msg)
                        await asyncio.sleep(delay)
                        continue
                    else:
                        # 尝试降级策略
                        if fallback_func:
                            logger.warning("使用降级策略处理空响应")
                            return await self._execute_fallback(fallback_func, *args, **kwargs)
                        else:
                            raise Exception(error_msg)
                
            except asyncio.TimeoutError as e:
                reason = RetryReason.TIMEOUT
                error_msg = f"LLM调用超时: {self.config.timeout}秒"
                last_error = e
                
            except Exception as e:
                reason = self._classify_error(e)
                error_msg = str(e)
                last_error = e
            
            # 决定是否重试
            if attempt < self._get_max_retries_for_reason(reason):
                delay = self._calculate_delay(attempt, reason)
                self._record_retry(attempt, reason, delay, error_msg)
                
                logger.warning(f"LLM调用失败 (尝试 {attempt + 1}): {error_msg}")
                logger.info(f"将在 {delay:.1f} 秒后重试...")
                
                await asyncio.sleep(delay)
            else:
                # 达到最大重试次数
                self.failure_count += 1
                
                # 尝试降级策略
                if fallback_func:
                    logger.warning("达到最大重试次数，使用降级策略")
                    return await self._execute_fallback(fallback_func, *args, **kwargs)
                else:
                    logger.error(f"LLM调用最终失败: {error_msg}")
                    raise last_error or Exception("LLM调用失败")
        
        # 不应该到达这里
        raise last_error or Exception("未知错误")
    
    def _is_valid_response(self, response: Any, check_empty: bool = True) -> bool:
        """检查响应是否有效"""
        if response is None:
            return False
        
        if check_empty:
            # 检查空字符串或空白字符串
            if isinstance(response, str):
                return bool(response.strip())
            
            # 检查空列表或字典
            if isinstance(response, (list, dict)):
                return bool(response)
        
        return True
    
    def _classify_error(self, error: Exception) -> RetryReason:
        """分类错误类型"""
        error_str = str(error).lower()
        
        if "rate limit" in error_str or "429" in error_str:
            return RetryReason.RATE_LIMIT
        elif "timeout" in error_str:
            return RetryReason.TIMEOUT
        elif "server error" in error_str or "500" in error_str or "502" in error_str or "503" in error_str:
            return RetryReason.SERVER_ERROR
        elif "network" in error_str or "connection" in error_str:
            return RetryReason.NETWORK_ERROR
        elif "context" in error_str or "token" in error_str:
            return RetryReason.CONTEXT_LIMIT
        elif "empty response" in error_str:
            return RetryReason.EMPTY_RESPONSE
        else:
            return RetryReason.PROVIDER_ERROR
    
    def _get_max_retries_for_reason(self, reason: RetryReason) -> int:
        """根据错误类型获取最大重试次数"""
        if reason == RetryReason.EMPTY_RESPONSE:
            return self.config.empty_response_retries
        elif reason == RetryReason.RATE_LIMIT:
            return self.config.rate_limit_retries
        elif reason == RetryReason.SERVER_ERROR:
            return self.config.server_error_retries
        else:
            return self.config.max_retries
    
    def _calculate_delay(self, attempt: int, reason: RetryReason) -> float:
        """计算重试延迟"""
        # 基础延迟
        base_delay = self.config.base_delay
        
        # 针对不同错误类型调整延迟
        if reason == RetryReason.RATE_LIMIT:
            base_delay *= 3  # 限流错误延迟更长
        elif reason == RetryReason.EMPTY_RESPONSE:
            base_delay *= 0.5  # 空响应延迟较短
        
        # 指数退避
        delay = base_delay * (self.config.exponential_base ** attempt)
        
        # 限制最大延迟
        delay = min(delay, self.config.max_delay)
        
        # 添加随机抖动
        if self.config.jitter:
            jitter = random.uniform(0.1, 0.3) * delay
            delay += jitter
        
        return delay
    
    def _record_retry(self, attempt: int, reason: RetryReason, delay: float, error_message: str):
        """记录重试尝试"""
        retry_attempt = RetryAttempt(
            attempt=attempt,
            reason=reason,
            delay=delay,
            timestamp=time.time(),
            error_message=error_message,
            success=False
        )
        self.retry_history.append(retry_attempt)
    
    def _record_success(self, attempt: int, start_time: float):
        """记录成功尝试"""
        if attempt > 0:  # 只有重试后成功才记录
            success_attempt = RetryAttempt(
                attempt=attempt,
                reason=RetryReason.EMPTY_RESPONSE,  # 占位符
                delay=0,
                timestamp=time.time(),
                error_message="",
                success=True
            )
            self.retry_history.append(success_attempt)
            
            duration = time.time() - start_time
            logger.info(f"LLM调用在第 {attempt + 1} 次尝试后成功 (耗时 {duration:.1f}秒)")
    
    async def _execute_fallback(self, fallback_func: Callable, *args, **kwargs) -> Any:
        """执行降级策略"""
        try:
            if asyncio.iscoroutinefunction(fallback_func):
                return await fallback_func(*args, **kwargs)
            else:
                return fallback_func(*args, **kwargs)
        except Exception as e:
            logger.error(f"降级策略也失败了: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_attempts = self.success_count + self.failure_count
        success_rate = (self.success_count / total_attempts * 100) if total_attempts > 0 else 0
        
        # 按错误类型统计
        error_stats = {}
        for attempt in self.retry_history:
            reason = attempt.reason.value
            if reason not in error_stats:
                error_stats[reason] = {"count": 0, "success_after_retry": 0}
            error_stats[reason]["count"] += 1
            if attempt.success:
                error_stats[reason]["success_after_retry"] += 1
        
        return {
            "total_attempts": total_attempts,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": success_rate,
            "retry_attempts": len(self.retry_history),
            "error_statistics": error_stats
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.retry_history.clear()
        self.success_count = 0
        self.failure_count = 0
        logger.info("重试统计信息已重置")


# 全局重试处理器实例
global_retry_handler = LLMRetryHandler()


def create_fallback_response(task_info: Dict[str, Any]) -> str:
    """创建降级响应"""
    task_title = task_info.get("title", "未知任务")
    task_type = task_info.get("task_type", "未知类型")
    
    return f"""
## ⚠️ AI服务暂时不可用

**任务**: {task_title}
**类型**: {task_type}

### 🔧 当前状态
AI服务提供商暂时无法响应，可能的原因：
- 服务器负载过高
- 网络连接问题
- API配额限制
- 服务维护中

### 📋 建议操作
1. **稍后重试**: 等待几分钟后重新提交任务
2. **检查配置**: 确认API密钥和配置正确
3. **联系管理员**: 如问题持续存在，请联系系统管理员

### 🔄 自动重试
系统已自动尝试多次重试，但仍无法获得有效响应。

---
*此消息由aider-plus自动生成 - {time.strftime('%Y-%m-%d %H:%M:%S')}*
"""


async def enhanced_llm_call(func: Callable, *args, task_info: Dict[str, Any] = None, **kwargs) -> Any:
    """
    增强的LLM调用，包含重试和降级策略
    
    Args:
        func: LLM调用函数
        *args: 函数参数
        task_info: 任务信息（用于降级响应）
        **kwargs: 函数关键字参数
        
    Returns:
        Any: LLM响应或降级响应
    """
    def fallback():
        return create_fallback_response(task_info or {})
    
    return await global_retry_handler.execute_with_retry(
        func, *args, 
        retry_on_empty=True,
        fallback_func=fallback,
        **kwargs
    )
