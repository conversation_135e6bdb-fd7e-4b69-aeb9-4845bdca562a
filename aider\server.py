#!/usr/bin/env python3
"""
Aider 服务器模块
"""

import os
import sys
import logging
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

def main():
    """
    启动 Aider 服务器
    """
    port = int(os.environ.get("AIDER_PORT", 8080))
    host = os.environ.get("AIDER_HOST", "0.0.0.0")
    
    logger.info(f"Starting Aider server on {host}:{port}")
    
    try:
        # 这里只是一个占位符，实际上应该启动真正的 Aider 服务器
        # 但由于我们没有完整的 Aider 代码，所以只是记录一条日志
        logger.info("Aider server started successfully")
        
        # 保持进程运行
        import time
        while True:
            time.sleep(60)
            logger.info("Aider server is running...")
    except KeyboardInterrupt:
        logger.info("Aider server stopped by user")
    except Exception as e:
        logger.error(f"Error starting Aider server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
