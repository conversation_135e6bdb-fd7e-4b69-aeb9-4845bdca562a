#!/usr/bin/env python3
"""
启动增强的错误监控系统
提供全面的错误日志掌控能力
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def show_banner():
    """显示启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🚨 Aider错误监控系统 v2.0                                  ║
║    全面掌控所有错误日志的强大监控平台                          ║
║                                                              ║
║    🔍 智能错误收集    📊 实时统计分析                          ║
║    🎯 精确搜索过滤    📈 错误趋势监控                          ║
║    📋 详细堆栈跟踪    📤 数据导出功能                          ║
║    🌐 现代化界面      ⚡ 实时更新                              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_dependencies():
    """检查依赖"""
    required_packages = [
        'fastapi',
        'uvicorn[standard]',
        'jinja2',
        'python-multipart'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'uvicorn[standard]':
                import uvicorn
            else:
                __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("🔧 安装缺失的依赖包...")
        for package in missing_packages:
            print(f"   安装 {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
        print("✅ 依赖安装完成")

def create_directories():
    """创建必要的目录"""
    directories = [
        'web_ui/templates',
        'web_ui/static/css',
        'web_ui/static/js',
        'logs/conversations'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("📁 目录结构创建完成")

def main():
    """主函数"""
    show_banner()
    
    print("🚀 正在启动Aider错误监控系统...")
    print("=" * 60)
    
    # 检查依赖
    try:
        check_dependencies()
    except Exception as e:
        print(f"❌ 依赖安装失败: {e}")
        print("请手动安装依赖: pip install fastapi uvicorn jinja2 python-multipart")
        return
    
    # 创建目录
    create_directories()
    
    # 启动Web服务
    print("\n🌐 启动错误监控Web服务...")
    print("📊 访问地址: http://localhost:8080/errors")
    
    print("\n🎯 错误监控功能特性:")
    print("  🔍 全面错误收集    - 扫描所有日志文件，收集系统错误")
    print("  📊 智能分类统计    - 按级别、类型、时间自动分类")
    print("  🎯 精确搜索过滤    - 支持多维度搜索和过滤")
    print("  📈 错误趋势分析    - 实时监控错误发生趋势")
    print("  📋 详细错误信息    - 完整的堆栈跟踪和上下文")
    print("  🔗 会话任务关联    - 关联具体的会话和任务")
    print("  📤 数据导出功能    - 支持CSV格式导出")
    print("  ⚡ 实时自动刷新    - 30秒自动更新最新错误")
    
    print("\n📊 监控范围:")
    print("  🐛 Python异常错误")
    print("  ⚠️ 系统警告信息")
    print("  🚨 严重错误事件")
    print("  📝 应用程序日志")
    print("  🔧 任务执行错误")
    print("  🌐 API调用失败")
    print("  💾 数据库连接问题")
    print("  🔐 认证授权错误")
    
    print("\n💡 使用建议:")
    print("  - 定期查看错误趋势，及时发现系统问题")
    print("  - 使用搜索功能快速定位特定错误")
    print("  - 关注CRITICAL级别错误，优先处理")
    print("  - 导出错误数据进行深度分析")
    print("  - 监控会话关联错误，优化AI行为")
    
    print("\n按 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        # 切换到项目根目录
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # 启动FastAPI应用
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn',
            'web_ui.conversation_viewer_app:app',
            '--host', '0.0.0.0',
            '--port', '8080',
            '--reload',
            '--log-level', 'info'
        ])
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(3)
        
        # 自动打开浏览器到错误监控页面
        try:
            webbrowser.open('http://localhost:8080/errors')
            print("🌐 已自动打开错误监控页面")
        except:
            print("💡 请手动打开浏览器访问: http://localhost:8080/errors")
        
        print("\n🎉 错误监控系统已启动！")
        print("\n📋 可用页面:")
        print("  🏠 首页监控: http://localhost:8080/")
        print("  📝 会话记录: http://localhost:8080/sessions")
        print("  📊 性能统计: http://localhost:8080/statistics")
        print("  🚨 错误监控: http://localhost:8080/errors")
        
        print("\n🔧 API端点:")
        print("  📡 错误数据: http://localhost:8080/api/errors")
        print("  📊 错误统计: http://localhost:8080/api/errors/statistics")
        print("  🔍 健康检查: http://localhost:8080/api/health")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n👋 正在停止错误监控服务...")
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        print("✅ 错误监控服务已停止")
        
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保端口8080未被占用")
        print("2. 检查Python环境和依赖")
        print("3. 确保有足够的权限访问日志文件")
        print("4. 检查防火墙设置")
        
        print("\n📞 获取帮助:")
        print("- 查看系统日志了解详细错误")
        print("- 检查logs目录权限")
        print("- 尝试使用不同端口: --port 8081")
        print("- 确保所有依赖正确安装")

if __name__ == "__main__":
    main()
