"""
部署监控器 - 监控GitLab CI/CD部署状态
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from bot_agent.clients.gitlab_client import GitLabClient

logger = logging.getLogger(__name__)


class DeploymentStatus(str, Enum):
    """部署状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELED = "canceled"
    SKIPPED = "skipped"


@dataclass
class DeploymentInfo:
    """部署信息"""
    pipeline_id: int
    job_id: int
    project_id: int
    ref: str
    status: DeploymentStatus
    stage: str
    job_name: str
    started_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    duration: Optional[float] = None
    failure_reason: Optional[str] = None
    logs: Optional[str] = None
    environment: Optional[str] = None
    deployment_url: Optional[str] = None


class DeploymentMonitor:
    """
    部署监控器

    功能：
    1. 监控GitLab CI/CD管道状态
    2. 实时跟踪部署进度
    3. 收集部署日志和错误信息
    4. 触发自动化响应
    """

    def __init__(self, gitlab_client: GitLabClient = None):
        """初始化部署监控器"""
        self.gitlab_client = gitlab_client or GitLabClient()
        self.active_deployments: Dict[str, DeploymentInfo] = {}
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        self.callbacks: List[Callable] = []

        logger.info("DeploymentMonitor initialized")

    def add_callback(self, callback: Callable[[DeploymentInfo], None]):
        """添加状态变更回调"""
        self.callbacks.append(callback)

    async def start_monitoring_project(self, project_id: int, poll_interval: int = 30):
        """
        开始监控项目的部署

        Args:
            project_id: 项目ID
            poll_interval: 轮询间隔（秒）
        """
        task_key = f"project_{project_id}"

        if task_key in self.monitoring_tasks:
            logger.warning(f"项目 {project_id} 已在监控中")
            return

        task = asyncio.create_task(
            self._monitor_project_deployments(project_id, poll_interval)
        )
        self.monitoring_tasks[task_key] = task

        logger.info(f"开始监控项目 {project_id} 的部署")

    async def stop_monitoring_project(self, project_id: int):
        """停止监控项目"""
        task_key = f"project_{project_id}"

        if task_key in self.monitoring_tasks:
            self.monitoring_tasks[task_key].cancel()
            del self.monitoring_tasks[task_key]
            logger.info(f"停止监控项目 {project_id}")

    async def monitor_specific_pipeline(self, project_id: int, pipeline_id: int) -> DeploymentInfo:
        """
        监控特定管道的部署

        Args:
            project_id: 项目ID
            pipeline_id: 管道ID

        Returns:
            DeploymentInfo: 最终部署信息
        """
        logger.info(f"开始监控管道 {pipeline_id}")

        deployment_key = f"{project_id}_{pipeline_id}"

        while True:
            try:
                # 获取管道信息
                pipeline = self.gitlab_client.get_pipeline(project_id, pipeline_id)
                if not pipeline:
                    logger.error(f"无法获取管道 {pipeline_id} 信息")
                    break

                # 获取部署作业
                jobs = self.gitlab_client.get_pipeline_jobs(project_id, pipeline_id)
                deployment_jobs = [job for job in jobs if self._is_deployment_job(job)]

                if not deployment_jobs:
                    logger.warning(f"管道 {pipeline_id} 中未找到部署作业")
                    await asyncio.sleep(10)
                    continue

                # 处理每个部署作业
                for job in deployment_jobs:
                    job_key = f"{deployment_key}_{job['id']}"

                    deployment_info = self._create_deployment_info(
                        project_id, pipeline_id, pipeline, job
                    )

                    # 检查状态变更
                    if job_key in self.active_deployments:
                        old_status = self.active_deployments[job_key].status
                        if old_status != deployment_info.status:
                            logger.info(f"部署状态变更: {old_status} -> {deployment_info.status}")
                            await self._notify_status_change(deployment_info)
                    else:
                        logger.info(f"新部署作业: {job['name']} ({deployment_info.status})")
                        await self._notify_status_change(deployment_info)

                    self.active_deployments[job_key] = deployment_info

                    # 如果部署完成（成功或失败），收集详细信息
                    if deployment_info.status in [DeploymentStatus.SUCCESS, DeploymentStatus.FAILED]:
                        await self._collect_deployment_details(deployment_info)

                        # 如果是失败，返回详细信息
                        if deployment_info.status == DeploymentStatus.FAILED:
                            return deployment_info

                # 检查管道是否完成
                if pipeline['status'] in ['success', 'failed', 'canceled']:
                    logger.info(f"管道 {pipeline_id} 完成，状态: {pipeline['status']}")

                    # 返回最后一个部署作业的信息
                    if deployment_jobs:
                        final_job = deployment_jobs[-1]
                        final_key = f"{deployment_key}_{final_job['id']}"
                        if final_key in self.active_deployments:
                            return self.active_deployments[final_key]
                    break

                await asyncio.sleep(10)  # 每10秒检查一次

            except Exception as e:
                logger.error(f"监控管道 {pipeline_id} 时出错: {e}", exc_info=True)
                await asyncio.sleep(30)

        # 创建默认的部署信息
        return DeploymentInfo(
            pipeline_id=pipeline_id,
            job_id=0,
            project_id=project_id,
            ref=pipeline.get('ref', 'unknown'),
            status=DeploymentStatus.FAILED,
            stage='unknown',
            job_name='unknown',
            failure_reason="监控过程中出现错误"
        )

    async def _monitor_project_deployments(self, project_id: int, poll_interval: int):
        """监控项目的所有部署"""
        logger.info(f"开始持续监控项目 {project_id}")

        last_check = datetime.now() - timedelta(hours=1)

        while True:
            try:
                # 获取最近的管道
                pipelines = self.gitlab_client.get_project_pipelines(
                    project_id,
                    updated_after=last_check.isoformat()
                )

                for pipeline in pipelines:
                    pipeline_id = pipeline['id']

                    # 检查是否包含部署作业
                    jobs = self.gitlab_client.get_pipeline_jobs(project_id, pipeline_id)
                    deployment_jobs = [job for job in jobs if self._is_deployment_job(job)]

                    if deployment_jobs:
                        # 监控这个管道
                        asyncio.create_task(
                            self.monitor_specific_pipeline(project_id, pipeline_id)
                        )

                last_check = datetime.now()
                await asyncio.sleep(poll_interval)

            except asyncio.CancelledError:
                logger.info(f"项目 {project_id} 监控被取消")
                break
            except Exception as e:
                logger.error(f"监控项目 {project_id} 时出错: {e}", exc_info=True)
                await asyncio.sleep(poll_interval)

    def _is_deployment_job(self, job: Dict) -> bool:
        """判断是否为部署作业"""
        job_name = job.get('name', '').lower()
        stage = job.get('stage', '').lower()

        deployment_indicators = [
            'deploy', 'deployment', '部署',
            'release', 'publish', '发布',
            'production', 'staging', 'prod'
        ]

        return any(indicator in job_name or indicator in stage
                  for indicator in deployment_indicators)

    def _create_deployment_info(self, project_id: int, pipeline_id: int,
                              pipeline: Dict, job: Dict) -> DeploymentInfo:
        """创建部署信息对象"""
        status_mapping = {
            'pending': DeploymentStatus.PENDING,
            'running': DeploymentStatus.RUNNING,
            'success': DeploymentStatus.SUCCESS,
            'failed': DeploymentStatus.FAILED,
            'canceled': DeploymentStatus.CANCELED,
            'skipped': DeploymentStatus.SKIPPED
        }

        status = status_mapping.get(job.get('status'), DeploymentStatus.PENDING)

        # 解析时间
        started_at = None
        finished_at = None
        duration = None

        if job.get('started_at'):
            started_at = datetime.fromisoformat(job['started_at'].replace('Z', '+00:00'))

        if job.get('finished_at'):
            finished_at = datetime.fromisoformat(job['finished_at'].replace('Z', '+00:00'))

        if started_at and finished_at:
            duration = (finished_at - started_at).total_seconds()

        return DeploymentInfo(
            pipeline_id=pipeline_id,
            job_id=job['id'],
            project_id=project_id,
            ref=pipeline.get('ref', 'unknown'),
            status=status,
            stage=job.get('stage', 'unknown'),
            job_name=job.get('name', 'unknown'),
            started_at=started_at,
            finished_at=finished_at,
            duration=duration,
            environment=job.get('environment', {}).get('name') if job.get('environment') else None
        )

    async def _collect_deployment_details(self, deployment_info: DeploymentInfo):
        """收集部署详细信息"""
        try:
            # 获取作业日志
            logs = self.gitlab_client.get_job_log(
                deployment_info.project_id,
                deployment_info.job_id
            )
            deployment_info.logs = logs

            # 如果失败，尝试提取失败原因
            if deployment_info.status == DeploymentStatus.FAILED and logs:
                deployment_info.failure_reason = self._extract_failure_reason(logs)

            # 获取部署URL（如果有环境）
            if deployment_info.environment:
                environments = self.gitlab_client.get_project_environments(
                    deployment_info.project_id
                )
                for env in environments:
                    if env.get('name') == deployment_info.environment:
                        deployment_info.deployment_url = env.get('external_url')
                        break

        except Exception as e:
            logger.error(f"收集部署详细信息失败: {e}")

    def _extract_failure_reason(self, logs: str) -> str:
        """从日志中提取失败原因"""
        if not logs:
            return "未知错误"

        # 查找常见的错误模式
        error_patterns = [
            r'ERROR:\s*(.+)',
            r'Error:\s*(.+)',
            r'FAILED:\s*(.+)',
            r'Exception:\s*(.+)',
            r'Fatal:\s*(.+)',
        ]

        import re
        for pattern in error_patterns:
            matches = re.findall(pattern, logs, re.IGNORECASE)
            if matches:
                return matches[-1].strip()  # 返回最后一个错误

        # 如果没有找到特定错误，返回日志的最后几行
        lines = logs.strip().split('\n')
        if lines:
            return '\n'.join(lines[-3:])  # 最后3行

        return "未知错误"

    async def _notify_status_change(self, deployment_info: DeploymentInfo):
        """通知状态变更"""
        for callback in self.callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(deployment_info)
                else:
                    callback(deployment_info)
            except Exception as e:
                logger.error(f"回调执行失败: {e}", exc_info=True)

    def get_active_deployments(self) -> List[DeploymentInfo]:
        """获取活跃的部署"""
        return list(self.active_deployments.values())

    def get_deployment_history(self, project_id: int, limit: int = 10) -> List[DeploymentInfo]:
        """获取部署历史"""
        project_deployments = [
            deployment for deployment in self.active_deployments.values()
            if deployment.project_id == project_id
        ]

        # 按时间排序
        project_deployments.sort(
            key=lambda x: x.started_at or datetime.min,
            reverse=True
        )

        return project_deployments[:limit]

    async def cleanup_old_deployments(self, max_age_hours: int = 24):
        """清理旧的部署记录"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        to_remove = []
        for key, deployment in self.active_deployments.items():
            if (deployment.finished_at and deployment.finished_at < cutoff_time) or \
               (deployment.started_at and deployment.started_at < cutoff_time):
                to_remove.append(key)

        for key in to_remove:
            del self.active_deployments[key]

        if to_remove:
            logger.info(f"清理了 {len(to_remove)} 个旧的部署记录")

    async def stop_all_monitoring(self):
        """停止所有监控"""
        for task in self.monitoring_tasks.values():
            task.cancel()

        self.monitoring_tasks.clear()
        logger.info("已停止所有部署监控")

    def export_deployment_report(self, deployment_info: DeploymentInfo) -> str:
        """导出部署报告"""
        report = f"""
## 🚀 部署报告

**项目ID**: {deployment_info.project_id}
**管道ID**: {deployment_info.pipeline_id}
**作业ID**: {deployment_info.job_id}
**分支**: {deployment_info.ref}
**状态**: {deployment_info.status.value}
**阶段**: {deployment_info.stage}
**作业名称**: {deployment_info.job_name}

### ⏰ 时间信息
- **开始时间**: {deployment_info.started_at or 'N/A'}
- **结束时间**: {deployment_info.finished_at or 'N/A'}
- **持续时间**: {f'{deployment_info.duration:.2f}秒' if deployment_info.duration else 'N/A'}

### 🌍 环境信息
- **部署环境**: {deployment_info.environment or 'N/A'}
- **访问URL**: {deployment_info.deployment_url or 'N/A'}

### ❌ 错误信息
{deployment_info.failure_reason or '无'}

### 📋 部署日志
```
{deployment_info.logs[-1000:] if deployment_info.logs else '无日志'}
```
"""
        return report
