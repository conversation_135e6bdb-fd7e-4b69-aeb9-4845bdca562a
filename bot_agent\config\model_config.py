"""
模型配置管理器 - 统一管理不同任务类型的AI模型配置
"""

import os
import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)


class ModelConfig:
    """
    AI模型配置管理器
    
    负责管理不同任务类型使用的AI模型：
    - 推理分析任务使用 DeepSeek R1
    - 代码生成任务使用 DeepSeek Chat
    """
    
    # 默认模型配置
    DEFAULT_MODELS = {
        "analysis": "openrouter/deepseek/deepseek-r1:free",      # 推理分析模型
        "code_generation": "openrouter/deepseek/deepseek-chat",  # 代码生成模型
        "chat": "openrouter/deepseek/deepseek-chat",             # 通用对话模型
    }
    
    # 环境变量映射
    ENV_MAPPING = {
        "analysis": "ANALYSIS_MODEL",
        "code_generation": "AIDER_MODEL", 
        "chat": "CHAT_MODEL",
    }
    
    @classmethod
    def get_analysis_model(cls) -> str:
        """
        获取推理分析模型
        
        Returns:
            str: 推理分析模型名称
        """
        model = os.getenv(cls.ENV_MAPPING["analysis"], cls.DEFAULT_MODELS["analysis"])
        logger.debug(f"获取推理分析模型: {model}")
        return model
    
    @classmethod
    def get_code_generation_model(cls) -> str:
        """
        获取代码生成模型
        
        Returns:
            str: 代码生成模型名称
        """
        model = os.getenv(cls.ENV_MAPPING["code_generation"], cls.DEFAULT_MODELS["code_generation"])
        logger.debug(f"获取代码生成模型: {model}")
        return model
    
    @classmethod
    def get_chat_model(cls) -> str:
        """
        获取通用对话模型
        
        Returns:
            str: 通用对话模型名称
        """
        model = os.getenv(cls.ENV_MAPPING["chat"], cls.DEFAULT_MODELS["chat"])
        logger.debug(f"获取通用对话模型: {model}")
        return model
    
    @classmethod
    def convert_aider_to_openrouter_format(cls, aider_model: str) -> str:
        """
        将Aider格式的模型名称转换为OpenRouter直接调用格式
        
        Args:
            aider_model: Aider格式的模型名称 (如: openrouter/deepseek/deepseek-r1:free)
            
        Returns:
            str: OpenRouter直接调用格式的模型名称 (如: deepseek/deepseek-r1:free)
        """
        if aider_model.startswith("openrouter/"):
            converted_model = aider_model[len("openrouter/"):]
            logger.debug(f"转换模型名称: {aider_model} -> {converted_model}")
            return converted_model
        
        return aider_model
    
    @classmethod
    def get_openrouter_analysis_model(cls) -> str:
        """
        获取OpenRouter格式的推理分析模型名称
        
        Returns:
            str: OpenRouter格式的推理分析模型名称
        """
        aider_model = cls.get_analysis_model()
        return cls.convert_aider_to_openrouter_format(aider_model)
    
    @classmethod
    def get_openrouter_code_generation_model(cls) -> str:
        """
        获取OpenRouter格式的代码生成模型名称
        
        Returns:
            str: OpenRouter格式的代码生成模型名称
        """
        aider_model = cls.get_code_generation_model()
        return cls.convert_aider_to_openrouter_format(aider_model)
    
    @classmethod
    def get_api_key(cls) -> Optional[str]:
        """
        获取OpenRouter API密钥
        
        Returns:
            Optional[str]: API密钥，如果未设置则返回None
        """
        return os.getenv("OPENROUTER_API_KEY")
    
    @classmethod
    def validate_config(cls) -> Dict[str, bool]:
        """
        验证模型配置是否完整
        
        Returns:
            Dict[str, bool]: 验证结果
        """
        results = {
            "api_key": cls.get_api_key() is not None,
            "analysis_model": bool(cls.get_analysis_model()),
            "code_generation_model": bool(cls.get_code_generation_model()),
        }
        
        logger.info(f"模型配置验证结果: {results}")
        return results
    
    @classmethod
    def log_current_config(cls):
        """记录当前模型配置"""
        logger.info("当前AI模型配置:")
        logger.info(f"  - 推理分析模型: {cls.get_analysis_model()}")
        logger.info(f"  - 代码生成模型: {cls.get_code_generation_model()}")
        logger.info(f"  - 通用对话模型: {cls.get_chat_model()}")
        logger.info(f"  - API密钥已设置: {'是' if cls.get_api_key() else '否'}")
        
        # 验证配置
        validation = cls.validate_config()
        if all(validation.values()):
            logger.info("✅ 所有模型配置正常")
        else:
            logger.warning(f"⚠️ 模型配置不完整: {validation}")


# 便捷函数
def get_analysis_model() -> str:
    """获取推理分析模型"""
    return ModelConfig.get_analysis_model()


def get_code_generation_model() -> str:
    """获取代码生成模型"""
    return ModelConfig.get_code_generation_model()


def get_openrouter_analysis_model() -> str:
    """获取OpenRouter格式的推理分析模型"""
    return ModelConfig.get_openrouter_analysis_model()


def get_openrouter_code_generation_model() -> str:
    """获取OpenRouter格式的代码生成模型"""
    return ModelConfig.get_openrouter_code_generation_model()
