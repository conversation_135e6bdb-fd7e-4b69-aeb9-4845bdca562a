# GitLab API配置指南

## 🔧 配置GitLab API Token

为了让系统能够获取Job日志和其他GitLab信息，需要配置GitLab API Token。

### 1. 创建GitLab Personal Access Token

1. 登录你的GitLab实例 (http://***************)
2. 点击右上角头像 → **Preferences**
3. 在左侧菜单选择 **Access Tokens**
4. 创建新的Personal Access Token：
   - **Name**: `aider-plus-bot`
   - **Expiration date**: 设置合适的过期时间
   - **Scopes**: 选择以下权限：
     - ✅ `api` - 完整API访问权限
     - ✅ `read_api` - 读取API权限
     - ✅ `read_repository` - 读取仓库权限
     - ✅ `write_repository` - 写入仓库权限（如果需要创建分支）

5. 点击 **Create personal access token**
6. **重要**: 复制生成的token，这是唯一一次显示

### 2. 配置环境变量

将token添加到环境变量中：

#### Windows (PowerShell)
```powershell
$env:GITLAB_API_TOKEN="your_token_here"
$env:GITLAB_API_URL="http://***************/api/v4"
```

#### Linux/Mac
```bash
export GITLAB_API_TOKEN="your_token_here"
export GITLAB_API_URL="http://***************/api/v4"
```

#### 永久配置 (推荐)
创建 `.env` 文件：
```bash
GITLAB_API_TOKEN=your_token_here
GITLAB_API_URL=http://***************/api/v4
GITLAB_WEBHOOK_TOKEN=your_webhook_secret
ALLOWED_USERS=aider-worker,aider-worker-wsl1
```

### 3. 验证项目ID

确认正确的项目ID：

1. 在GitLab中打开你的项目
2. 项目ID显示在项目名称下方
3. 或者通过API获取：
   ```bash
   curl -H "PRIVATE-TOKEN: your_token" \
        "http://***************/api/v4/projects?search=aider-plus"
   ```

## 🧪 测试API连接

运行测试脚本验证配置：

```bash
python test_gitlab_api.py
```

成功的输出应该包含：
- ✅ 项目信息获取成功
- ✅ Pipeline列表获取成功
- ✅ 作业日志获取成功

## 🔍 Job日志获取API

系统使用以下GitLab API端点获取Job日志：

```
GET /projects/:id/jobs/:job_id/trace
```

### API调用示例

```python
import requests

headers = {
    "PRIVATE-TOKEN": "your_token_here"
}

# 获取作业日志
response = requests.get(
    "http://***************/api/v4/projects/9/jobs/651/trace",
    headers=headers
)

if response.status_code == 200:
    log_content = response.text
    print(f"日志长度: {len(log_content)} 字符")
else:
    print(f"获取失败: {response.status_code} - {response.text}")
```

## 🚨 常见问题

### 1. 401 Unauthorized
- **原因**: API Token未配置或无效
- **解决**: 检查环境变量 `GITLAB_API_TOKEN`

### 2. 404 Project Not Found
- **原因**: 项目ID错误或Token权限不足
- **解决**: 
  - 确认项目ID正确
  - 确保Token有访问该项目的权限

### 3. 403 Forbidden
- **原因**: Token权限不足
- **解决**: 重新创建Token，确保包含必要的scopes

### 4. 日志为空
- **原因**: 作业可能还在运行或日志已被清理
- **解决**: 检查作业状态，确保作业已完成

## 🔧 系统集成

配置完成后，系统将能够：

1. **自动获取Job日志**: 当Job失败时，自动获取详细日志
2. **智能错误分析**: 分析日志中的错误模式
3. **生成修复建议**: 基于实际错误提供针对性建议
4. **实时监控**: 监控Pipeline和Job状态变化

## 📊 监控效果

正确配置后，你将看到：

```
🚨 作业失败分析报告

**作业名称**: lint
**作业ID**: 651
**失败原因**: script_failure

### 🔍 错误分析
检测到以下关键错误:
- **ERROR**: Line too long (120 > 79 characters) at line 45
- **ERROR**: Missing docstring in function 'process_data' at line 67

### 💡 修复建议
1. 📝 **代码规范**: 运行本地lint检查并修复代码规范问题
2. 🔄 **重试**: 如果是临时问题，可以重新运行作业
3. 📋 **日志**: 查看完整的作业日志获取更多详细信息
```

这样，你就不需要手动把错误发给AI了，系统会自动分析并提供解决方案！
