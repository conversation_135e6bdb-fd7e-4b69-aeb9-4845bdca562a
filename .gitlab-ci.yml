stages:
  - lint
  - fix_style
  - test
  - build
  - deploy_dev
  - deploy_prod
  - backup_data

variables:
  CI_REGISTRY: ***************:5050
  CI_REGISTRY_IMAGE: ***************:5050/docker/ci-images/aider-plus
  DEV_SERVER: ***************
  PROD_SERVER: ***************
  HTTP_PROXY: http://*************:10810
  NO_PROXY: *************/24,localhost,127.0.0.1,docker
  IMAGE_TAG: $CI_COMMIT_REF_SLUG
  DATA_STORAGE_DIR: /home/<USER>/aider-plus/data
  REDIS_IMAGE: $CI_REGISTRY/docker/ci-images/redis:7.0
  CONSUL_IMAGE: $CI_REGISTRY/docker/ci-images/consul:1.20
  # CI_PUSH_TOKEN 应该在 GitLab CI/CD 设置中配置为受保护的变量
  CI_PUSH_TOKEN: $CI_PUSH_TOKEN

# 通用 Docker 配置模板
.docker_setup:
  image: $CI_REGISTRY/docker/ci-images/docker:20.10
  services:
    - name: $CI_REGISTRY/docker/ci-images/docker:20.10-dind
      alias: docker
      command: ["--tls=false", "--insecure-registry=***************:5050"]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - mkdir -p /etc/docker
    - |
      cat > /etc/docker/daemon.json <<EOF
      {
        "insecure-registries":["$CI_REGISTRY"],
        "proxies": {
          "http-proxy":"$HTTP_PROXY",
          "https-proxy":"$HTTP_PROXY",
          "no-proxy":"$NO_PROXY"
        }
      }
      EOF
    - export HTTP_PROXY="$HTTP_PROXY"
    - export HTTPS_PROXY="$HTTP_PROXY"
    - export NO_PROXY="$NO_PROXY"
    - for i in {1..30}; do docker info && break; sleep 2; done
    - docker info --format '{{.ServerVersion}}' || echo "Docker daemon not running"
    - echo "$CI_IMAGES_TOKEN" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY

# Python 通用配置模板
.python_setup:
  image: ***************:5050/docker/ci-images/python:3.9-slim
  before_script:
    - pip install -U pip
    - pip install flake8 pytest pytest-cov
    # 设置 Git 凭据，用于推送代码
    - if [ -n "$CI_PUSH_TOKEN" ]; then
        git config --global credential.helper store;
        echo "https://gitlab-ci-token:${CI_PUSH_TOKEN}@${CI_SERVER_HOST}" > ~/.git-credentials;
      fi

# 代码风格检查
lint:
  extends: .python_setup
  stage: lint
  script:
    - pip install flake8
    - flake8 aider bot_agent augment > flake8_report.txt || true
    - cat flake8_report.txt
    - if [ -s flake8_report.txt ]; then echo "发现代码风格问题，请查看报告"; else echo "代码风格检查通过"; fi
  artifacts:
    paths:
      - flake8_report.txt
    expire_in: 1 week
  rules:
    # 只在打标签或合并到主分支时自动触发
    - if: '$CI_COMMIT_TAG || $CI_COMMIT_BRANCH == "main"'
      changes:
        - "**/*.py"
    # 其他情况下手动触发
    - if: '$CI_COMMIT_BRANCH'
      when: manual
      allow_failure: true

# 修复代码风格问题
fix_style:
  extends: .python_setup
  stage: fix_style
  script:
    - pip install flake8 autopep8 autoflake isort
    - |
      if [ -f flake8_report.txt ] && [ -s flake8_report.txt ]; then
        echo "修复代码风格问题..."
        # 修复空行中的空格和行尾空格
        find aider bot_agent augment -name "*.py" -exec sed -i 's/[ \t]*$//' {} \;
        # 使用 autopep8 修复 PEP 8 问题
        autopep8 --in-place --recursive --aggressive --aggressive aider bot_agent augment
        # 使用 autoflake 移除未使用的导入
        autoflake --in-place --remove-all-unused-imports --recursive aider bot_agent augment
        # 使用 isort 排序导入
        isort aider bot_agent augment
        # 再次运行 flake8 检查
        flake8 aider bot_agent augment > fixed_flake8_report.txt || true
        cat fixed_flake8_report.txt
        # 如果还有问题，创建一个修复分支并提交更改
        if [ -s fixed_flake8_report.txt ]; then
          echo "仍有一些代码风格问题未能自动修复，请手动检查"
        else
          echo "所有代码风格问题已修复"
        fi
        # 提交更改
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitLab CI"
        git add aider bot_agent augment
        git commit -m "自动修复代码风格问题 [skip ci]" || echo "没有更改需要提交"
        git push origin HEAD:$CI_COMMIT_REF_NAME || echo "无法推送更改，可能需要手动处理"
      else
        echo "没有代码风格问题需要修复"
      fi
  artifacts:
    paths:
      - fixed_flake8_report.txt
    expire_in: 1 week
    when: always
  rules:
    # 只在打标签或合并到主分支时自动触发
    - if: '$CI_COMMIT_TAG || $CI_COMMIT_BRANCH == "main"'
      changes:
        - "**/*.py"
      needs:
        - lint
    # 其他情况下手动触发
    - if: '$CI_COMMIT_BRANCH'
      when: manual
      allow_failure: true

# 单元测试
test:
  extends: .python_setup
  stage: test
  script:
    - pip install -r requirements.txt
    - pip install -r bot_agent/requirements.txt
    - pytest tests/ --cov=aider --cov=bot_agent --cov=augment --cov-report=xml
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - coverage.xml
    expire_in: 1 week
  rules:
    # 只在打标签或合并到主分支时自动触发
    - if: '$CI_COMMIT_TAG || $CI_COMMIT_BRANCH == "main"'
      changes:
        - "**/*.py"
        - "requirements.txt"
        - "bot_agent/requirements.txt"
    # 其他情况下手动触发
    - if: '$CI_COMMIT_BRANCH'
      when: manual
      allow_failure: true
  needs:
    - job: fix_style
      optional: true

# 构建基础镜像 (包含所有依赖)
build_base:
  extends: .docker_setup
  stage: build
  script:
    - docker build -f Dockerfile.base -t $CI_REGISTRY_IMAGE-base:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-base:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "dev"'
      changes:
        - requirements.txt
        - Dockerfile.base
    - if: '$CI_COMMIT_TAG'
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: always
  timeout: 1 hours

# 构建应用镜像 (包含 Bot 代理服务、Aider 和 Augment)
build_unified:
  extends: .docker_setup
  stage: build
  script:
    # 拉取基础镜像
    - docker pull $CI_REGISTRY_IMAGE-base:$IMAGE_TAG || echo "基础镜像不存在，将使用默认基础镜像"
    # 构建应用镜像
    - docker build -f Dockerfile.app --build-arg BASE_IMAGE=$CI_REGISTRY_IMAGE-base:$IMAGE_TAG -t $CI_REGISTRY_IMAGE-unified:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE-unified:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "dev"'
      changes:
        - aider/**/*
        - augment/**/*
        - bot_agent/**/*
        - Dockerfile.app
        - start-unified.sh
    - if: '$CI_COMMIT_TAG'
  needs:
    - job: build_base
      optional: true
  timeout: 20 minutes

# 部署通用配置模板
.deploy_setup:
  extends: .docker_setup
  before_script:
    - apk add --no-cache gettext openssh-client  # 安装 envsubst 和 ssh 客户端
    - mkdir -p ~/.ssh
    - echo "$DEV_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $DEV_SERVER >> ~/.ssh/known_hosts
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "docker --version && docker-compose --version" || { echo "Docker or Docker Compose not installed on $DEV_SERVER"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "mkdir -p $DATA_STORAGE_DIR/consul $DATA_STORAGE_DIR/redis"

# 部署统一服务到开发环境
deploy_dev_unified:
  extends: .deploy_setup
  stage: deploy_dev
  script:
    - envsubst < docker-compose.external.yml > docker-compose-rendered.yml
    - scp docker-compose-rendered.yml longer@$DEV_SERVER:/home/<USER>/aider-plus/docker-compose.yml || { echo "SCP failed"; exit 1; }
    - scp start-unified.sh longer@$DEV_SERVER:/home/<USER>/aider-plus/start-unified.sh || { echo "SCP failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        cd /home/<USER>/aider-plus &&
        chmod +x start-unified.sh &&
        docker-compose pull aider-plus-unified &&
        docker-compose up -d aider-plus-unified &&
        docker-compose ps --services &&
        echo 'Unified deployment completed' || { echo 'Unified deployment failed'; exit 1; }"
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - aider/**/*
        - augment/**/*
        - bot_agent/**/*
        - docker-compose.external.yml
        - Dockerfile.app
        - start-unified.sh
      when: always
    - if: '$CI_COMMIT_BRANCH == "dev"'
      when: manual
      allow_failure: true
  dependencies:
    - build_unified
  timeout: 20 minutes

# 注意：不再需要部署服务依赖，因为我们使用外部的 Consul 和 Redis
# deploy_dev_dependencies 任务已被移除，因为我们现在使用 *************** 上已有的 Consul 和 Redis 服务

# 部署到生产环境
deploy_prod:
  extends: .deploy_setup
  stage: deploy_prod
  before_script:
    - apk add --no-cache gettext openssh-client
    - mkdir -p ~/.ssh
    - echo "$PROD_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $PROD_SERVER >> ~/.ssh/known_hosts
  script:
    - envsubst < docker-compose.external.prod.yml > docker-compose-rendered.yml
    - scp docker-compose-rendered.yml longer@$DEV_SERVER:/home/<USER>/aider-plus/docker-compose.yml || { echo "SCP failed"; exit 1; }
    - scp start-unified.sh longer@$DEV_SERVER:/home/<USER>/aider-plus/start-unified.sh || { echo "SCP failed"; exit 1; }
    - ssh -o StrictHostKeyChecking=no longer@$DEV_SERVER "
        cd /home/<USER>/aider-plus &&
        chmod +x start-unified.sh &&
        docker-compose pull &&
        docker-compose up -d &&
        docker-compose ps --services &&
        echo 'Production deployment completed' || { echo 'Production deployment failed'; exit 1; }"
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: manual
  dependencies:
    - build_unified
  timeout: 30 minutes

# 备份数据
backup_data:
  stage: backup_data
  image: $CI_REGISTRY/docker/ci-images/docker:20.10
  before_script:
    - apk add --no-cache openssh-client
    - mkdir -p ~/.ssh
    - echo "$DEV_SSH_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $DEV_SERVER >> ~/.ssh/known_hosts
  script:
    - mkdir -p data_backup/redis data_backup/consul
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/redis/* data_backup/redis/ || echo "No Redis data to backup"
    - scp -r longer@$DEV_SERVER:$DATA_STORAGE_DIR/consul/* data_backup/consul/ || echo "No Consul data to backup"
  artifacts:
    paths:
      - data_backup/
    expire_in: 1 month
    when: always
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "main"'
      when: manual
      allow_failure: true
  timeout: 30 minutes