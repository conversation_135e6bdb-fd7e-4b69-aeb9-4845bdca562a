{% extends "base.html" %}

{% block title %}错误日志监控 - Aider行为分析系统{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet">
<style>
    .error-item {
        cursor: pointer;
        transition: all 0.3s;
        border-left: 4px solid #dc3545;
    }
    .error-item:hover {
        background: #f8f9fa;
        border-radius: 8px;
        transform: translateX(4px);
    }
    .error-level-critical { border-left-color: #dc3545; }
    .error-level-error { border-left-color: #fd7e14; }
    .error-level-warning { border-left-color: #ffc107; }
    .error-level-info { border-left-color: #0dcaf0; }

    .traceback-container {
        background: #f8f9fa;
        border-radius: 4px;
        max-height: 300px;
        overflow-y: auto;
    }

    /* 彻底修复模态框层级问题 */

    /* 第一步：重置可能影响堆叠上下文的元素 */
    body.modal-open .content-wrapper {
        backdrop-filter: none !important;
        transform: none !important;
    }

    body.modal-open .main-content {
        backdrop-filter: none !important;
        transform: none !important;
    }

    body.modal-open .card {
        backdrop-filter: none !important;
        transform: none !important;
    }

    /* 第二步：设置极高的z-index值 */
    .modal {
        z-index: 999999 !important;
        position: fixed !important;
    }

    .modal-backdrop {
        z-index: 999998 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        position: fixed !important;
    }

    .modal.show {
        display: block !important;
        z-index: 999999 !important;
    }

    .modal-dialog {
        z-index: 999999 !important;
        position: relative !important;
    }

    .modal-content {
        z-index: 999999 !important;
        position: relative !important;
        border: none;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        background: white !important;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        z-index: 999999 !important;
        position: relative !important;
        background: transparent !important;
    }

    /* 第三步：确保按钮和交互元素可用 */
    .modal .btn,
    .modal .btn-close,
    .modal input,
    .modal textarea,
    .modal select {
        z-index: 999999 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* 第四步：强制覆盖任何可能的冲突 */
    body.modal-open .modal-backdrop.show {
        z-index: 999998 !important;
        opacity: 0.5 !important;
    }

    body.modal-open .modal.show {
        z-index: 999999 !important;
    }

    /* 模态框内容样式 - 合并到上面的定义中 */

    .modal-header {
        border-bottom: 1px solid #e9ecef;
        border-radius: 12px 12px 0 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .modal-footer {
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 12px 12px;
        background: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-exclamation-triangle text-danger"></i>
        错误日志监控
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="exportBtn">
                <i class="fas fa-download"></i> 导出
            </button>
            <button type="button" class="btn btn-sm btn-outline-info" id="testModalBtn" onclick="testModal()">
                <i class="fas fa-vial"></i> 测试模态框
            </button>
            <button type="button" class="btn btn-sm btn-outline-warning" onclick="debugZIndex()">
                <i class="fas fa-bug"></i> 调试层级
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="forceTestModal()">
                <i class="fas fa-rocket"></i> 强制测试
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" id="statsCards">
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
            <div class="metric-value" id="totalErrors">-</div>
            <div class="metric-label">总错误数</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
            <div class="metric-value" id="criticalErrors">-</div>
            <div class="metric-label">严重错误</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
            <div class="metric-value" id="recentErrors">-</div>
            <div class="metric-label">最近1小时</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card metric-card text-white" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="metric-value" id="errorRate">-</div>
            <div class="metric-label">错误率</div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="search-filters">
    <div class="row">
        <div class="col-md-2">
            <label for="hoursFilter" class="form-label">时间范围</label>
            <select class="form-select" id="hoursFilter">
                <option value="1">最近1小时</option>
                <option value="6">最近6小时</option>
                <option value="24" selected>最近24小时</option>
                <option value="72">最近3天</option>
                <option value="168">最近7天</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="levelFilter" class="form-label">错误级别</label>
            <select class="form-select" id="levelFilter">
                <option value="">全部级别</option>
                <option value="CRITICAL">严重</option>
                <option value="ERROR">错误</option>
                <option value="WARNING">警告</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="exceptionFilter" class="form-label">异常类型</label>
            <select class="form-select" id="exceptionFilter">
                <option value="">全部类型</option>
            </select>
        </div>
        <div class="col-md-2">
            <label for="loggerFilter" class="form-label">日志器</label>
            <select class="form-select" id="loggerFilter">
                <option value="">全部日志器</option>
            </select>
        </div>
        <div class="col-md-3">
            <label for="searchQuery" class="form-label">搜索</label>
            <input type="text" class="form-control" id="searchQuery" placeholder="搜索错误消息...">
        </div>
        <div class="col-md-1 d-flex align-items-end">
            <button type="button" class="btn btn-outline-secondary w-100" id="searchBtn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>
<!-- 错误趋势图表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    错误趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="errorTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 错误列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle"></i>
            错误日志列表
        </h5>
        <span class="badge bg-secondary" id="errorCount">加载中...</span>
    </div>
    <div class="card-body p-0">
        <div id="errorsList">
            <div class="loading">
                <div class="spinner"></div>
                <div>正在收集错误日志...</div>
            </div>
        </div>
    </div>
</div>

<!-- 错误详情模态框 -->
<div class="modal fade" id="errorDetailModal" tabindex="-1" aria-labelledby="errorDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="errorDetailModalLabel">错误详情</h5>
                <button type="button" class="btn-close" onclick="closeModal()" aria-label="关闭"></button>
            </div>
            <div class="modal-body" id="errorDetailContent">
                <!-- 错误详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> 关闭
                </button>
                <button type="button" class="btn btn-primary" id="copyErrorBtn" onclick="errorMonitor && errorMonitor.copyErrorToClipboard()">
                    <i class="fas fa-copy"></i> 复制错误信息
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
<script src="/static/js/errors.js"></script>
<script>
// 测试模态框功能
function testModal() {
    console.log('测试模态框...');

    const testError = {
        timestamp: new Date().toISOString(),
        level: 'ERROR',
        logger_name: 'test.logger',
        message: '这是一个测试错误消息',
        exception_type: 'TestException',
        exception_message: '测试异常消息',
        file_path: '/test/file.py',
        line_number: 42,
        function_name: 'test_function',
        session_id: 'test-session-123',
        task_id: 'test-task-456',
        source_file: 'test.log',
        traceback: 'Traceback (most recent call last):\n  File "/test/file.py", line 42, in test_function\n    raise TestException("测试异常")\nTestException: 测试异常'
    };

    if (errorMonitor) {
        errorMonitor.showErrorDetail(testError);
    } else {
        console.error('errorMonitor 未初始化');
        // 直接显示模态框作为备用方案
        showModalDirectly(testError);
    }
}

// 直接显示模态框的备用方法 - 彻底重写
function showModalDirectly(error) {
    // 移除现有的模态框和背景
    const existingModals = document.querySelectorAll('.modal, .modal-backdrop');
    existingModals.forEach(el => el.remove());

    // 创建全新的模态框HTML
    const modalHTML = `
        <div class="modal fade show" id="forceModal" style="display: block; z-index: 999999; position: fixed;">
            <div class="modal-dialog modal-xl" style="z-index: 999999; position: relative;">
                <div class="modal-content" style="z-index: 999999; position: relative; background: white;">
                    <div class="modal-header" style="z-index: 999999; position: relative;">
                        <h5 class="modal-title">错误详情 (强制显示)</h5>
                        <button type="button" class="btn-close" onclick="forceCloseModal()" style="z-index: 999999; position: relative;"></button>
                    </div>
                    <div class="modal-body" style="z-index: 999999; position: relative;">
                        <div class="alert alert-info">
                            <h6>测试错误详情</h6>
                            <p><strong>消息:</strong> ${error.message}</p>
                            <p><strong>时间:</strong> ${new Date(error.timestamp).toLocaleString('zh-CN')}</p>
                            <p><strong>级别:</strong> ${error.level}</p>
                            <p><strong>异常类型:</strong> ${error.exception_type || 'N/A'}</p>
                        </div>
                    </div>
                    <div class="modal-footer" style="z-index: 999999; position: relative;">
                        <button type="button" class="btn btn-secondary" onclick="forceCloseModal()" style="z-index: 999999; position: relative;">
                            <i class="fas fa-times"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show" style="z-index: 999998; position: fixed; background-color: rgba(0,0,0,0.5);"></div>
    `;

    // 直接插入到body中
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    document.body.classList.add('modal-open');

    // 禁用页面滚动
    document.body.style.overflow = 'hidden';

    console.log('强制模态框已显示');
}

// 强制关闭模态框
function forceCloseModal() {
    const forceModal = document.getElementById('forceModal');
    const backdrop = document.querySelector('.modal-backdrop');

    if (forceModal) forceModal.remove();
    if (backdrop) backdrop.remove();

    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';

    console.log('强制模态框已关闭');
}

// 强制测试模态框
function forceTestModal() {
    const testError = {
        timestamp: new Date().toISOString(),
        level: 'ERROR',
        logger_name: 'force.test.logger',
        message: '这是强制显示的测试错误消息',
        exception_type: 'ForceTestException',
        exception_message: '强制测试异常消息',
        file_path: '/force/test/file.py',
        line_number: 999,
        function_name: 'force_test_function',
        session_id: 'force-test-session-999',
        task_id: 'force-test-task-999',
        source_file: 'force-test.log',
        traceback: 'Traceback (most recent call last):\n  File "/force/test/file.py", line 999, in force_test_function\n    raise ForceTestException("强制测试异常")\nForceTestException: 强制测试异常'
    };

    showModalDirectly(testError);
}

// 关闭模态框
function closeModal() {
    const modalElement = document.getElementById('errorDetailModal');

    // 尝试使用Bootstrap方式关闭
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    if (modalInstance) {
        modalInstance.hide();
    } else {
        // 强制关闭
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        document.body.classList.remove('modal-open');

        // 移除背景遮罩
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
    }

    console.log('模态框已关闭');
}

// 调试z-index层级
function debugZIndex() {
    console.log('=== Z-Index 调试信息 ===');

    const modal = document.getElementById('errorDetailModal');
    const backdrop = document.querySelector('.modal-backdrop');

    if (modal) {
        const computedStyle = window.getComputedStyle(modal);
        console.log('Modal z-index:', computedStyle.zIndex);
        console.log('Modal display:', computedStyle.display);
        console.log('Modal position:', computedStyle.position);

        const dialog = modal.querySelector('.modal-dialog');
        if (dialog) {
            const dialogStyle = window.getComputedStyle(dialog);
            console.log('Dialog z-index:', dialogStyle.zIndex);
        }

        const content = modal.querySelector('.modal-content');
        if (content) {
            const contentStyle = window.getComputedStyle(content);
            console.log('Content z-index:', contentStyle.zIndex);
        }
    }

    if (backdrop) {
        const backdropStyle = window.getComputedStyle(backdrop);
        console.log('Backdrop z-index:', backdropStyle.zIndex);
        console.log('Backdrop display:', backdropStyle.display);
    }

    // 列出所有高z-index元素
    const allElements = document.querySelectorAll('*');
    const highZElements = [];
    allElements.forEach(el => {
        const style = window.getComputedStyle(el);
        const zIndex = parseInt(style.zIndex);
        if (zIndex > 1000) {
            highZElements.push({
                element: el.tagName + (el.className ? '.' + el.className.split(' ').join('.') : ''),
                zIndex: zIndex
            });
        }
    });

    console.log('高z-index元素:', highZElements.sort((a, b) => b.zIndex - a.zIndex));
    console.log('=== 调试信息结束 ===');
}

// 添加键盘快捷键支持
document.addEventListener('keydown', function(event) {
    // ESC键关闭模态框
    if (event.key === 'Escape') {
        closeModal();
    }
});
</script>
{% endblock %}
