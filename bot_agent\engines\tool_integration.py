"""
工具集成层 - 将工具系统与Aider智能集成
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from bot_agent.tools.tool_router import ToolRouter, ToolSuggestion
from bot_agent.tools.base_tool import ToolResult

logger = logging.getLogger(__name__)


class AiderToolIntegration:
    """
    Aider工具集成
    
    功能：
    1. 智能工具建议生成
    2. 工具执行结果整合
    3. Aider提示词增强
    4. 自动化工作流编排
    """
    
    def __init__(self):
        self.tool_router = ToolRouter()
        self.execution_history = []
    
    async def enhance_aider_request(
        self, 
        original_request: str, 
        project_path: str,
        task_context: Dict = None
    ) -> str:
        """
        增强Aider请求，添加工具建议
        
        Args:
            original_request: 原始请求
            project_path: 项目路径
            task_context: 任务上下文
            
        Returns:
            str: 增强后的请求
        """
        try:
            logger.info("开始增强Aider请求")
            
            # 分析任务并获取工具建议
            context = {
                'project_path': project_path,
                **(task_context or {})
            }
            
            suggestions = await self.tool_router.analyze_task(original_request, context)
            
            if not suggestions:
                return original_request
            
            # 构建增强的请求
            enhanced_request = self._build_enhanced_request(
                original_request, suggestions, context
            )
            
            logger.info(f"请求增强完成，添加了 {len(suggestions)} 个工具建议")
            return enhanced_request
            
        except Exception as e:
            logger.error(f"请求增强失败: {e}", exc_info=True)
            return original_request
    
    def _build_enhanced_request(
        self, 
        original_request: str, 
        suggestions: List[ToolSuggestion],
        context: Dict
    ) -> str:
        """构建增强的请求"""
        
        # 获取最佳工具建议
        best_suggestion = suggestions[0] if suggestions else None
        
        enhanced_request = f"""
{original_request}

## 🛠️ 智能工具建议

基于任务分析，我为你准备了以下工具和命令：

"""
        
        # 添加工具建议
        for i, suggestion in enumerate(suggestions[:3], 1):  # 最多显示3个建议
            enhanced_request += f"""
### {i}. {suggestion.tool_name.title()} 工具
- **置信度**: {suggestion.confidence:.1%}
- **建议原因**: {suggestion.reason}
- **推荐命令**: `{suggestion.command}`

"""
        
        # 添加最佳工具的详细指导
        if best_suggestion and best_suggestion.confidence > 0.6:
            enhanced_request += f"""
## 🎯 推荐执行方案

基于分析，最适合的工具是 **{best_suggestion.tool_name}**（置信度: {best_suggestion.confidence:.1%}）

### 建议的执行步骤：
{self._generate_execution_steps(best_suggestion, context)}

### 可以直接使用的命令：
```bash
{best_suggestion.command}
```

"""
        
        # 添加工具使用指导
        enhanced_request += """
## 📋 工具使用指导

你可以：
1. **直接执行建议的命令** - 使用上面提供的命令
2. **自定义参数** - 根据具体需求调整命令参数
3. **组合使用** - 结合多个工具完成复杂任务
4. **查看结果** - 分析工具执行结果并据此调整代码

## 🚀 开始实现

请根据以上建议开始实现功能。如果需要执行命令，请直接使用建议的命令。
如果遇到问题，我会自动分析错误并提供解决方案。

---

现在开始处理原始任务：
"""
        
        return enhanced_request
    
    def _generate_execution_steps(self, suggestion: ToolSuggestion, context: Dict) -> str:
        """生成执行步骤"""
        tool_name = suggestion.tool_name
        
        if tool_name == 'testing':
            return """
1. 检查测试环境配置
2. 发现项目中的测试文件
3. 运行相应的测试类型
4. 分析测试结果和覆盖率
5. 根据结果优化代码
"""
        
        elif tool_name == 'terminal':
            return """
1. 验证命令的安全性和正确性
2. 在适当的目录下执行命令
3. 监控执行过程和输出
4. 处理可能的错误和异常
5. 验证执行结果
"""
        
        elif tool_name == 'log_analysis':
            return """
1. 定位相关的日志文件
2. 解析日志格式和内容
3. 识别错误模式和异常
4. 分析性能指标
5. 生成诊断报告和修复建议
"""
        
        else:
            return """
1. 准备工具执行环境
2. 验证输入参数
3. 执行工具操作
4. 分析执行结果
5. 应用结果到项目中
"""
    
    async def execute_tools_for_task(
        self, 
        task_description: str, 
        project_path: str,
        auto_execute: bool = True
    ) -> Dict[str, Any]:
        """
        为任务执行相关工具
        
        Args:
            task_description: 任务描述
            project_path: 项目路径
            auto_execute: 是否自动执行
            
        Returns:
            Dict: 执行结果
        """
        try:
            logger.info(f"为任务执行工具: {task_description}")
            
            context = {'project_path': project_path}
            
            if auto_execute:
                # 自动执行最佳工具
                results = await self.tool_router.auto_execute(task_description, context)
            else:
                # 只分析不执行
                suggestions = await self.tool_router.analyze_task(task_description, context)
                results = [ToolResult(
                    success=True,
                    data={'suggestions': [s.__dict__ for s in suggestions]},
                    message=f"分析完成，找到 {len(suggestions)} 个工具建议"
                )]
            
            # 记录执行历史
            execution_record = {
                'timestamp': datetime.now().isoformat(),
                'task_description': task_description,
                'project_path': project_path,
                'auto_execute': auto_execute,
                'results': [r.to_dict() for r in results]
            }
            
            self.execution_history.append(execution_record)
            
            return {
                'success': all(r.success for r in results),
                'results': results,
                'execution_record': execution_record
            }
            
        except Exception as e:
            logger.error(f"工具执行失败: {e}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'results': []
            }
    
    def generate_tool_report(self, results: List[ToolResult]) -> str:
        """
        生成工具执行报告
        
        Args:
            results: 工具执行结果列表
            
        Returns:
            str: Markdown格式的报告
        """
        try:
            if not results:
                return "## 📊 工具执行报告\n\n未执行任何工具。"
            
            report = "## 📊 工具执行报告\n\n"
            
            # 总体统计
            success_count = sum(1 for r in results if r.success)
            total_count = len(results)
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0
            
            report += f"### 📈 执行统计\n"
            report += f"- **总执行数**: {total_count}\n"
            report += f"- **成功数**: {success_count}\n"
            report += f"- **成功率**: {success_rate:.1f}%\n\n"
            
            # 详细结果
            for i, result in enumerate(results, 1):
                status = "✅ 成功" if result.success else "❌ 失败"
                tool_name = result.metadata.get('tool_name', '未知工具') if result.metadata else '未知工具'
                
                report += f"### {i}. {tool_name} - {status}\n\n"
                
                if result.message:
                    report += f"**消息**: {result.message}\n\n"
                
                if result.error:
                    report += f"**错误**: {result.error}\n\n"
                
                if result.metadata and result.metadata.get('command'):
                    report += f"**执行命令**: `{result.metadata['command']}`\n\n"
                
                if result.execution_time:
                    report += f"**执行时间**: {result.execution_time:.2f}秒\n\n"
                
                # 添加关键数据
                if result.data and isinstance(result.data, dict):
                    key_data = self._extract_key_data(result.data)
                    if key_data:
                        report += f"**关键信息**:\n{key_data}\n\n"
                
                report += "---\n\n"
            
            return report
            
        except Exception as e:
            logger.error(f"生成工具报告失败: {e}", exc_info=True)
            return f"## 📊 工具执行报告\n\n报告生成失败: {str(e)}"
    
    def _extract_key_data(self, data: Dict) -> str:
        """提取关键数据"""
        key_info = []
        
        # 测试结果
        if 'total_coverage' in data:
            key_info.append(f"- 代码覆盖率: {data['total_coverage']:.1f}%")
        
        if 'summary' in data and isinstance(data['summary'], dict):
            summary = data['summary']
            if 'passed' in summary:
                key_info.append(f"- 测试通过: {summary['passed']}")
            if 'failed' in summary:
                key_info.append(f"- 测试失败: {summary['failed']}")
        
        # 系统信息
        if 'platform' in data:
            key_info.append(f"- 系统平台: {data['platform']}")
        
        # 进程信息
        if 'running' in data:
            status = "运行中" if data['running'] else "未运行"
            key_info.append(f"- 进程状态: {status}")
        
        # 日志分析
        if 'health_score' in data:
            key_info.append(f"- 系统健康度: {data['health_score']}%")
        
        if 'total_errors' in data:
            key_info.append(f"- 错误数量: {data['total_errors']}")
        
        return "\n".join(key_info) if key_info else ""
    
    def get_execution_history(self, limit: int = 10) -> List[Dict]:
        """获取执行历史"""
        return self.execution_history[-limit:] if self.execution_history else []
    
    def clear_execution_history(self):
        """清空执行历史"""
        self.execution_history.clear()
        logger.info("执行历史已清空")
    
    async def suggest_next_actions(self, results: List[ToolResult]) -> List[str]:
        """
        基于工具执行结果建议下一步操作
        
        Args:
            results: 工具执行结果
            
        Returns:
            List[str]: 建议操作列表
        """
        suggestions = []
        
        for result in results:
            if not result.success:
                suggestions.append(f"🔧 修复工具执行错误: {result.error}")
                continue
            
            tool_name = result.metadata.get('tool_name') if result.metadata else None
            
            if tool_name == 'testing' and result.data:
                # 测试结果建议
                if result.data.get('total_coverage', 0) < 80:
                    suggestions.append("📈 提高代码覆盖率，添加更多测试用例")
                
                if result.data.get('summary', {}).get('failed', 0) > 0:
                    suggestions.append("🐛 修复失败的测试用例")
            
            elif tool_name == 'log_analysis' and result.data:
                # 日志分析建议
                health_score = result.data.get('health_score', 100)
                if health_score < 70:
                    suggestions.append("🚨 系统健康度较低，需要处理错误和性能问题")
                
                if result.data.get('total_errors', 0) > 0:
                    suggestions.append("🔍 分析和修复日志中的错误")
        
        if not suggestions:
            suggestions.append("✅ 所有工具执行正常，可以继续开发")
        
        return suggestions
