"""
代码重构工具 - AI自动编码的重构能力
"""

import os
import ast
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from pathlib import Path
import tempfile
import shutil

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class RefactorTools(BaseTool):
    """
    代码重构工具
    
    功能：
    1. 函数提取和分离
    2. 变量重命名
    3. 类重构和拆分
    4. 代码移动和重组
    5. 重复代码检测和消除
    6. 接口提取
    7. 方法内联和提取
    8. 代码结构优化
    """
    
    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'extract_function',
            'rename_variable',
            'refactor_class',
            'move_code',
            'eliminate_duplicates',
            'extract_interface',
            'inline_method',
            'optimize_structure'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "代码重构工具 - 函数提取、变量重命名、类重构等"
    
    async def extract_function(self, file_path: str, start_line: int, end_line: int, 
                              function_name: str) -> ToolResult:
        """
        提取函数 - 将代码块提取为独立函数
        
        Args:
            file_path: 源文件路径
            start_line: 开始行号
            end_line: 结束行号
            function_name: 新函数名
            
        Returns:
            ToolResult: 重构结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if start_line < 1 or end_line > len(lines) or start_line > end_line:
                return ToolResult(
                    success=False,
                    error="行号范围无效"
                )
            
            # 提取要重构的代码块
            code_block = ''.join(lines[start_line-1:end_line])
            
            # 分析代码块的依赖
            analysis = self._analyze_code_block(code_block, lines, start_line)
            
            # 生成新函数
            new_function = self._generate_extracted_function(
                function_name, code_block, analysis
            )
            
            # 生成函数调用
            function_call = self._generate_function_call(function_name, analysis)
            
            # 创建重构后的文件内容
            refactored_lines = (
                lines[:start_line-1] +
                [function_call + '\n'] +
                lines[end_line:] +
                ['\n\n'] +
                [new_function]
            )
            
            # 备份原文件
            backup_path = file_path + '.backup'
            shutil.copy2(file_path, backup_path)
            
            # 写入重构后的代码
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(refactored_lines)
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'function_name': function_name,
                    'extracted_lines': f"{start_line}-{end_line}",
                    'backup_path': backup_path,
                    'analysis': analysis,
                    'new_function': new_function.strip()
                },
                message=f"成功提取函数 {function_name}"
            )
            
        except Exception as e:
            self.log_error(e, f"函数提取: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def rename_variable(self, file_path: str, old_name: str, new_name: str,
                             scope: str = "file") -> ToolResult:
        """
        变量重命名
        
        Args:
            file_path: 文件路径
            old_name: 旧变量名
            new_name: 新变量名
            scope: 重命名范围 (file, function, class)
            
        Returns:
            ToolResult: 重命名结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            # 查找变量使用位置
            variable_finder = VariableFinder(old_name)
            variable_finder.visit(tree)
            
            if not variable_finder.occurrences:
                return ToolResult(
                    success=False,
                    error=f"未找到变量 {old_name}"
                )
            
            # 执行重命名
            lines = content.split('\n')
            renamed_count = 0
            
            for occurrence in variable_finder.occurrences:
                line_no = occurrence['line'] - 1
                col_offset = occurrence['col_offset']
                
                if line_no < len(lines):
                    line = lines[line_no]
                    # 确保是完整的变量名匹配
                    if self._is_valid_rename_position(line, col_offset, old_name):
                        lines[line_no] = (
                            line[:col_offset] + 
                            new_name + 
                            line[col_offset + len(old_name):]
                        )
                        renamed_count += 1
            
            if renamed_count == 0:
                return ToolResult(
                    success=False,
                    error="没有找到可重命名的变量实例"
                )
            
            # 备份并写入新内容
            backup_path = file_path + '.backup'
            shutil.copy2(file_path, backup_path)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'old_name': old_name,
                    'new_name': new_name,
                    'renamed_count': renamed_count,
                    'backup_path': backup_path,
                    'occurrences': variable_finder.occurrences
                },
                message=f"成功重命名 {renamed_count} 个 {old_name} 为 {new_name}"
            )
            
        except Exception as e:
            self.log_error(e, f"变量重命名: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def refactor_class(self, file_path: str, class_name: str, 
                            refactor_type: str = "split") -> ToolResult:
        """
        类重构
        
        Args:
            file_path: 文件路径
            class_name: 类名
            refactor_type: 重构类型 (split, extract_interface, simplify)
            
        Returns:
            ToolResult: 重构结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # 查找目标类
            class_finder = ClassFinder(class_name)
            class_finder.visit(tree)
            
            if not class_finder.class_node:
                return ToolResult(
                    success=False,
                    error=f"未找到类 {class_name}"
                )
            
            class_node = class_finder.class_node
            
            # 分析类结构
            class_analysis = self._analyze_class_structure(class_node)
            
            # 根据重构类型执行不同的重构
            if refactor_type == "split":
                refactor_result = self._split_class(class_node, class_analysis)
            elif refactor_type == "extract_interface":
                refactor_result = self._extract_interface(class_node, class_analysis)
            elif refactor_type == "simplify":
                refactor_result = self._simplify_class(class_node, class_analysis)
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的重构类型: {refactor_type}"
                )
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'class_name': class_name,
                    'refactor_type': refactor_type,
                    'analysis': class_analysis,
                    'refactor_result': refactor_result
                },
                message=f"成功重构类 {class_name}"
            )
            
        except Exception as e:
            self.log_error(e, f"类重构: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def eliminate_duplicates(self, project_path: str, 
                                  similarity_threshold: float = 0.8) -> ToolResult:
        """
        消除重复代码
        
        Args:
            project_path: 项目路径
            similarity_threshold: 相似度阈值
            
        Returns:
            ToolResult: 重复代码消除结果
        """
        try:
            # 查找Python文件
            python_files = list(Path(project_path).rglob('*.py'))
            
            # 检测重复代码
            duplicate_detector = DuplicateCodeDetector(similarity_threshold)
            duplicates = duplicate_detector.find_duplicates(python_files)
            
            if not duplicates:
                return ToolResult(
                    success=True,
                    data={'duplicates': [], 'eliminated_count': 0},
                    message="未发现重复代码"
                )
            
            # 生成重构建议
            refactor_suggestions = []
            for duplicate_group in duplicates:
                suggestion = self._generate_duplicate_elimination_suggestion(duplicate_group)
                refactor_suggestions.append(suggestion)
            
            return ToolResult(
                success=True,
                data={
                    'duplicates': duplicates,
                    'refactor_suggestions': refactor_suggestions,
                    'total_duplicate_groups': len(duplicates)
                },
                message=f"发现 {len(duplicates)} 组重复代码"
            )
            
        except Exception as e:
            self.log_error(e, f"重复代码消除: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def optimize_structure(self, file_path: str) -> ToolResult:
        """
        优化代码结构
        
        Args:
            file_path: 文件路径
            
        Returns:
            ToolResult: 结构优化结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # 分析代码结构
            structure_analyzer = StructureAnalyzer()
            structure_analyzer.visit(tree)
            
            # 生成优化建议
            optimizations = []
            
            # 检查函数长度
            for func in structure_analyzer.functions:
                if func['line_count'] > 50:
                    optimizations.append({
                        'type': 'function_too_long',
                        'target': func['name'],
                        'suggestion': f"函数 {func['name']} 过长 ({func['line_count']} 行)，建议拆分",
                        'priority': 'high'
                    })
            
            # 检查类复杂度
            for cls in structure_analyzer.classes:
                if len(cls['methods']) > 15:
                    optimizations.append({
                        'type': 'class_too_complex',
                        'target': cls['name'],
                        'suggestion': f"类 {cls['name']} 方法过多 ({len(cls['methods'])})，考虑拆分",
                        'priority': 'medium'
                    })
            
            # 检查嵌套深度
            if structure_analyzer.max_nesting_depth > 4:
                optimizations.append({
                    'type': 'deep_nesting',
                    'target': 'code_structure',
                    'suggestion': f"代码嵌套过深 (最大 {structure_analyzer.max_nesting_depth} 层)，建议重构",
                    'priority': 'medium'
                })
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'structure_analysis': {
                        'functions': structure_analyzer.functions,
                        'classes': structure_analyzer.classes,
                        'max_nesting_depth': structure_analyzer.max_nesting_depth,
                        'total_lines': len(content.split('\n'))
                    },
                    'optimizations': optimizations,
                    'optimization_count': len(optimizations)
                },
                message=f"结构分析完成，发现 {len(optimizations)} 个优化建议"
            )
            
        except Exception as e:
            self.log_error(e, f"结构优化: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _analyze_code_block(self, code_block: str, all_lines: List[str], 
                           start_line: int) -> Dict:
        """分析代码块的依赖和变量"""
        analysis = {
            'input_variables': set(),
            'output_variables': set(),
            'local_variables': set(),
            'imports_needed': set(),
            'indentation': ''
        }
        
        # 检测缩进
        first_line = code_block.split('\n')[0]
        analysis['indentation'] = first_line[:len(first_line) - len(first_line.lstrip())]
        
        # 简单的变量分析（实际应该使用AST）
        lines = code_block.split('\n')
        for line in lines:
            # 查找赋值语句
            if '=' in line and not line.strip().startswith('#'):
                parts = line.split('=')
                if len(parts) >= 2:
                    var_name = parts[0].strip().split()[-1]
                    if var_name.isidentifier():
                        analysis['local_variables'].add(var_name)
        
        return analysis
    
    def _generate_extracted_function(self, function_name: str, code_block: str, 
                                   analysis: Dict) -> str:
        """生成提取的函数"""
        # 简化实现
        indentation = analysis.get('indentation', '')
        
        function_def = f"def {function_name}():\n"
        
        # 添加代码块（调整缩进）
        lines = code_block.split('\n')
        function_lines = [function_def]
        
        for line in lines:
            if line.strip():
                # 移除原有缩进，添加函数内缩进
                clean_line = line.lstrip()
                function_lines.append(f"    {clean_line}")
            else:
                function_lines.append("")
        
        return '\n'.join(function_lines)
    
    def _generate_function_call(self, function_name: str, analysis: Dict) -> str:
        """生成函数调用"""
        indentation = analysis.get('indentation', '')
        return f"{indentation}{function_name}()"
    
    def _is_valid_rename_position(self, line: str, col_offset: int, old_name: str) -> bool:
        """检查是否是有效的重命名位置"""
        # 确保前后不是字母数字字符（完整单词匹配）
        before_char = line[col_offset - 1] if col_offset > 0 else ' '
        after_char = line[col_offset + len(old_name)] if col_offset + len(old_name) < len(line) else ' '
        
        return not (before_char.isalnum() or before_char == '_') and not (after_char.isalnum() or after_char == '_')
    
    def _analyze_class_structure(self, class_node: ast.ClassDef) -> Dict:
        """分析类结构"""
        methods = []
        properties = []
        
        for node in class_node.body:
            if isinstance(node, ast.FunctionDef):
                methods.append({
                    'name': node.name,
                    'line': node.lineno,
                    'is_private': node.name.startswith('_'),
                    'is_property': any(isinstance(d, ast.Name) and d.id == 'property' 
                                     for d in node.decorator_list)
                })
            elif isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        properties.append({
                            'name': target.id,
                            'line': node.lineno
                        })
        
        return {
            'name': class_node.name,
            'line': class_node.lineno,
            'methods': methods,
            'properties': properties,
            'method_count': len(methods),
            'property_count': len(properties)
        }
    
    def _split_class(self, class_node: ast.ClassDef, analysis: Dict) -> Dict:
        """拆分类"""
        # 简化实现：基于方法分组建议拆分
        public_methods = [m for m in analysis['methods'] if not m['is_private']]
        private_methods = [m for m in analysis['methods'] if m['is_private']]
        
        return {
            'original_class': analysis['name'],
            'suggested_splits': [
                {
                    'new_class_name': f"{analysis['name']}Core",
                    'methods': public_methods[:len(public_methods)//2],
                    'reason': '核心功能'
                },
                {
                    'new_class_name': f"{analysis['name']}Utils",
                    'methods': public_methods[len(public_methods)//2:] + private_methods,
                    'reason': '辅助功能'
                }
            ]
        }
    
    def _extract_interface(self, class_node: ast.ClassDef, analysis: Dict) -> Dict:
        """提取接口"""
        public_methods = [m for m in analysis['methods'] if not m['is_private']]
        
        interface_methods = []
        for method in public_methods:
            interface_methods.append({
                'name': method['name'],
                'signature': f"def {method['name']}(self): pass"
            })
        
        return {
            'interface_name': f"I{analysis['name']}",
            'methods': interface_methods,
            'original_class': analysis['name']
        }
    
    def _simplify_class(self, class_node: ast.ClassDef, analysis: Dict) -> Dict:
        """简化类"""
        return {
            'simplification_suggestions': [
                f"移除未使用的方法",
                f"合并相似的方法",
                f"提取常量到类变量"
            ]
        }
    
    def _generate_duplicate_elimination_suggestion(self, duplicate_group: Dict) -> Dict:
        """生成重复代码消除建议"""
        return {
            'type': 'extract_common_function',
            'suggested_function_name': f"common_function_{len(duplicate_group['instances'])}",
            'instances': duplicate_group['instances'],
            'similarity': duplicate_group['similarity']
        }
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            file_path = args[0]
            return await self.optimize_structure(file_path)
        else:
            return ToolResult(
                success=False,
                error="缺少文件路径参数"
            )


class VariableFinder(ast.NodeVisitor):
    """变量查找器"""
    
    def __init__(self, variable_name: str):
        self.variable_name = variable_name
        self.occurrences = []
    
    def visit_Name(self, node):
        if node.id == self.variable_name:
            self.occurrences.append({
                'line': node.lineno,
                'col_offset': node.col_offset,
                'context': node.__class__.__name__
            })
        self.generic_visit(node)


class ClassFinder(ast.NodeVisitor):
    """类查找器"""
    
    def __init__(self, class_name: str):
        self.class_name = class_name
        self.class_node = None
    
    def visit_ClassDef(self, node):
        if node.name == self.class_name:
            self.class_node = node
        self.generic_visit(node)


class StructureAnalyzer(ast.NodeVisitor):
    """结构分析器"""
    
    def __init__(self):
        self.functions = []
        self.classes = []
        self.max_nesting_depth = 0
        self.current_depth = 0
    
    def visit_FunctionDef(self, node):
        self.current_depth += 1
        self.max_nesting_depth = max(self.max_nesting_depth, self.current_depth)
        
        self.functions.append({
            'name': node.name,
            'line': node.lineno,
            'line_count': getattr(node, 'end_lineno', node.lineno) - node.lineno + 1
        })
        
        self.generic_visit(node)
        self.current_depth -= 1
    
    def visit_ClassDef(self, node):
        methods = []
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                methods.append(item.name)
        
        self.classes.append({
            'name': node.name,
            'line': node.lineno,
            'methods': methods
        })
        
        self.generic_visit(node)


class DuplicateCodeDetector:
    """重复代码检测器"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        self.similarity_threshold = similarity_threshold
    
    def find_duplicates(self, file_paths: List[Path]) -> List[Dict]:
        """查找重复代码"""
        # 简化实现
        duplicates = []
        
        # 这里应该实现更复杂的代码相似度算法
        # 目前返回示例数据
        if len(file_paths) > 1:
            duplicates.append({
                'similarity': 0.85,
                'instances': [
                    {'file': str(file_paths[0]), 'lines': '10-20'},
                    {'file': str(file_paths[1]), 'lines': '15-25'}
                ]
            })
        
        return duplicates
