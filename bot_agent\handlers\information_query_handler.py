"""
信息查询处理器 - 专门处理信息查询类任务
"""

import logging
import os
import re
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class InformationQueryHandler:
    """
    信息查询处理器
    
    专门处理用户的信息查询请求，如：
    - 查看项目配置
    - 列出使用的技术栈
    - 显示文件结构
    - 获取环境信息
    """
    
    def __init__(self):
        """初始化信息查询处理器"""
        logger.info("InformationQueryHandler initialized")
    
    async def handle_query(self, task: Dict[str, Any], project_path: str) -> str:
        """
        处理信息查询任务
        
        Args:
            task: 任务信息
            project_path: 项目路径
            
        Returns:
            str: 查询结果
        """
        title = task.get("title", "")
        description = task.get("description", "")
        query_content = f"{title}\n{description}".lower()
        
        logger.info(f"处理信息查询: {title}")
        
        try:
            # 根据查询内容确定查询类型
            if self._is_docker_image_query(query_content):
                return await self._handle_docker_image_query(project_path)
            elif self._is_gitlab_config_query(query_content):
                return await self._handle_gitlab_config_query(project_path)
            elif self._is_dependency_query(query_content):
                return await self._handle_dependency_query(project_path)
            elif self._is_project_structure_query(query_content):
                return await self._handle_project_structure_query(project_path)
            elif self._is_environment_query(query_content):
                return await self._handle_environment_query(project_path)
            elif self._is_git_info_query(query_content):
                return await self._handle_git_info_query(project_path)
            else:
                return await self._handle_general_query(query_content, project_path)
                
        except Exception as e:
            logger.error(f"处理信息查询失败: {e}", exc_info=True)
            return f"查询失败: {str(e)}"
    
    def _is_docker_image_query(self, content: str) -> bool:
        """检查是否是Docker镜像查询"""
        keywords = ["镜像", "image", "docker", "容器", "container"]
        return any(keyword in content for keyword in keywords)
    
    def _is_gitlab_config_query(self, content: str) -> bool:
        """检查是否是GitLab配置查询"""
        keywords = ["gitlab", "ci/cd", "pipeline", "构建", "部署"]
        return any(keyword in content for keyword in keywords)
    
    def _is_dependency_query(self, content: str) -> bool:
        """检查是否是依赖查询"""
        keywords = ["依赖", "dependency", "requirements", "package", "库", "模块"]
        return any(keyword in content for keyword in keywords)
    
    def _is_project_structure_query(self, content: str) -> bool:
        """检查是否是项目结构查询"""
        keywords = ["结构", "structure", "目录", "文件", "组织", "架构"]
        return any(keyword in content for keyword in keywords)
    
    def _is_environment_query(self, content: str) -> bool:
        """检查是否是环境信息查询"""
        keywords = ["环境", "environment", "配置", "config", "设置", "版本"]
        return any(keyword in content for keyword in keywords)
    
    def _is_git_info_query(self, content: str) -> bool:
        """检查是否是Git信息查询"""
        keywords = ["git", "分支", "branch", "提交", "commit", "版本控制"]
        return any(keyword in content for keyword in keywords)
    
    async def _handle_docker_image_query(self, project_path: str) -> str:
        """处理Docker镜像查询"""
        logger.info("处理Docker镜像查询")
        
        result = "## 🐳 项目中使用的Docker镜像\n\n"
        
        # 查找Docker相关文件
        docker_files = []
        
        # 检查Dockerfile
        dockerfile_path = Path(project_path) / "Dockerfile"
        if dockerfile_path.exists():
            docker_files.append(("Dockerfile", dockerfile_path))
        
        # 检查docker-compose.yml
        compose_path = Path(project_path) / "docker-compose.yml"
        if compose_path.exists():
            docker_files.append(("docker-compose.yml", compose_path))
        
        # 检查.gitlab-ci.yml
        gitlab_ci_path = Path(project_path) / ".gitlab-ci.yml"
        if gitlab_ci_path.exists():
            docker_files.append((".gitlab-ci.yml", gitlab_ci_path))
        
        if not docker_files:
            result += "❌ 未找到Docker相关配置文件\n\n"
            result += "建议检查以下文件：\n"
            result += "- Dockerfile\n"
            result += "- docker-compose.yml\n"
            result += "- .gitlab-ci.yml\n"
            return result
        
        # 分析每个文件中的镜像
        found_images = set()
        
        for file_name, file_path in docker_files:
            result += f"### 📄 {file_name}\n"
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 提取镜像名称
                images = self._extract_docker_images(content)
                
                if images:
                    for image in images:
                        found_images.add(image)
                        result += f"- `{image}`\n"
                else:
                    result += "- 未找到镜像引用\n"
                
                result += "\n"
                
            except Exception as e:
                result += f"- ❌ 读取文件失败: {e}\n\n"
        
        # 总结
        if found_images:
            result += f"### 📊 总计发现 {len(found_images)} 个不同的镜像：\n"
            for image in sorted(found_images):
                result += f"- `{image}`\n"
        else:
            result += "### ⚠️ 未发现任何Docker镜像引用\n"
        
        return result
    
    def _extract_docker_images(self, content: str) -> List[str]:
        """从文件内容中提取Docker镜像名称"""
        images = []
        
        # 常见的镜像引用模式
        patterns = [
            r'FROM\s+([^\s\n]+)',  # Dockerfile FROM
            r'image:\s*([^\s\n]+)',  # docker-compose image
            r'image:\s+([^\s\n]+)',  # GitLab CI image
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                # 清理镜像名称
                image = match.strip().strip('"\'')
                if image and not image.startswith('$'):  # 排除变量
                    images.append(image)
        
        return list(set(images))  # 去重
    
    async def _handle_gitlab_config_query(self, project_path: str) -> str:
        """处理GitLab配置查询"""
        logger.info("处理GitLab配置查询")
        
        result = "## 🦊 GitLab CI/CD 配置信息\n\n"
        
        gitlab_ci_path = Path(project_path) / ".gitlab-ci.yml"
        
        if not gitlab_ci_path.exists():
            result += "❌ 未找到 `.gitlab-ci.yml` 文件\n\n"
            result += "建议创建GitLab CI/CD配置文件来启用自动化构建和部署。\n"
            return result
        
        try:
            with open(gitlab_ci_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分析配置内容
            result += "### 📄 配置文件内容：\n"
            result += f"```yaml\n{content}\n```\n\n"
            
            # 提取关键信息
            stages = re.findall(r'stages:\s*\n((?:\s*-\s*.+\n?)+)', content)
            if stages:
                result += "### 🔄 构建阶段：\n"
                stage_lines = stages[0].strip().split('\n')
                for line in stage_lines:
                    stage = line.strip().lstrip('- ').strip()
                    if stage:
                        result += f"- {stage}\n"
                result += "\n"
            
            # 提取使用的镜像
            images = self._extract_docker_images(content)
            if images:
                result += "### 🐳 使用的镜像：\n"
                for image in images:
                    result += f"- `{image}`\n"
                result += "\n"
            
            # 提取作业
            jobs = re.findall(r'^([a-zA-Z_][a-zA-Z0-9_-]*):$', content, re.MULTILINE)
            if jobs:
                # 过滤掉stages等关键字
                actual_jobs = [job for job in jobs if job not in ['stages', 'variables', 'cache', 'before_script', 'after_script']]
                if actual_jobs:
                    result += "### ⚙️ 定义的作业：\n"
                    for job in actual_jobs:
                        result += f"- {job}\n"
                    result += "\n"
            
        except Exception as e:
            result += f"❌ 读取配置文件失败: {e}\n"
        
        return result
    
    async def _handle_dependency_query(self, project_path: str) -> str:
        """处理依赖查询"""
        logger.info("处理依赖查询")
        
        result = "## 📦 项目依赖信息\n\n"
        
        # 检查不同类型的依赖文件
        dependency_files = [
            ("requirements.txt", "Python依赖"),
            ("pyproject.toml", "Python项目配置"),
            ("setup.py", "Python安装脚本"),
            ("package.json", "Node.js依赖"),
            ("Cargo.toml", "Rust依赖"),
            ("go.mod", "Go模块"),
            ("pom.xml", "Maven依赖"),
            ("build.gradle", "Gradle依赖"),
        ]
        
        found_files = []
        
        for filename, description in dependency_files:
            file_path = Path(project_path) / filename
            if file_path.exists():
                found_files.append((filename, description, file_path))
        
        if not found_files:
            result += "❌ 未找到依赖配置文件\n"
            return result
        
        for filename, description, file_path in found_files:
            result += f"### 📄 {description} ({filename})\n"
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 限制显示长度
                if len(content) > 1000:
                    result += f"```\n{content[:1000]}...\n[文件太长，已截断]\n```\n\n"
                else:
                    result += f"```\n{content}\n```\n\n"
                    
            except Exception as e:
                result += f"❌ 读取文件失败: {e}\n\n"
        
        return result
    
    async def _handle_project_structure_query(self, project_path: str) -> str:
        """处理项目结构查询"""
        logger.info("处理项目结构查询")
        
        result = "## 📁 项目结构\n\n"
        
        try:
            # 生成目录树
            tree_output = self._generate_directory_tree(project_path)
            result += f"```\n{tree_output}\n```\n\n"
            
            # 统计信息
            stats = self._get_project_stats(project_path)
            result += "### 📊 项目统计\n"
            for key, value in stats.items():
                result += f"- {key}: {value}\n"
            
        except Exception as e:
            result += f"❌ 生成项目结构失败: {e}\n"
        
        return result
    
    async def _handle_environment_query(self, project_path: str) -> str:
        """处理环境信息查询"""
        logger.info("处理环境信息查询")
        
        result = "## 🖥️ 环境信息\n\n"
        
        # 系统信息
        result += "### 系统环境\n"
        result += f"- 操作系统: {os.name}\n"
        result += f"- 当前工作目录: {os.getcwd()}\n"
        result += f"- 项目路径: {project_path}\n\n"
        
        # 环境变量（只显示相关的）
        relevant_env_vars = [
            "PYTHON_VERSION", "NODE_VERSION", "JAVA_VERSION",
            "OPENROUTER_API_KEY", "GITLAB_TOKEN", "PROJECTS_DIR",
            "AIDER_CHAT_LANGUAGE", "PATH"
        ]
        
        result += "### 相关环境变量\n"
        for var in relevant_env_vars:
            value = os.getenv(var)
            if value:
                # 隐藏敏感信息
                if "key" in var.lower() or "token" in var.lower():
                    display_value = f"{value[:8]}..." if len(value) > 8 else "***"
                else:
                    display_value = value
                result += f"- {var}: {display_value}\n"
        
        return result
    
    async def _handle_git_info_query(self, project_path: str) -> str:
        """处理Git信息查询"""
        logger.info("处理Git信息查询")
        
        result = "## 🌿 Git 信息\n\n"
        
        try:
            # 切换到项目目录
            original_cwd = os.getcwd()
            os.chdir(project_path)
            
            # 获取当前分支
            current_branch = subprocess.check_output(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                text=True, encoding="utf-8"
            ).strip()
            result += f"### 当前分支: `{current_branch}`\n\n"
            
            # 获取远程仓库
            remotes = subprocess.check_output(
                ["git", "remote", "-v"],
                text=True, encoding="utf-8"
            ).strip()
            if remotes:
                result += "### 远程仓库\n"
                result += f"```\n{remotes}\n```\n\n"
            
            # 获取最近的提交
            recent_commits = subprocess.check_output(
                ["git", "log", "--oneline", "-5"],
                text=True, encoding="utf-8"
            ).strip()
            if recent_commits:
                result += "### 最近5次提交\n"
                result += f"```\n{recent_commits}\n```\n\n"
            
        except subprocess.CalledProcessError as e:
            result += f"❌ 获取Git信息失败: {e}\n"
        except Exception as e:
            result += f"❌ 处理Git信息时出错: {e}\n"
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)
        
        return result
    
    async def _handle_general_query(self, query_content: str, project_path: str) -> str:
        """处理通用查询"""
        logger.info("处理通用查询")
        
        result = "## 🔍 查询结果\n\n"
        result += f"您的查询: `{query_content.strip()}`\n\n"
        result += "我理解您想要查询项目信息。以下是我能提供的信息类型：\n\n"
        result += "- 🐳 **Docker镜像**: 查询项目中使用的Docker镜像\n"
        result += "- 🦊 **GitLab配置**: 查看CI/CD配置信息\n"
        result += "- 📦 **项目依赖**: 列出项目的依赖包\n"
        result += "- 📁 **项目结构**: 显示目录和文件结构\n"
        result += "- 🖥️ **环境信息**: 查看系统和环境配置\n"
        result += "- 🌿 **Git信息**: 查看版本控制信息\n\n"
        result += "请提供更具体的查询内容，例如：\n"
        result += "- \"列出项目中使用的Docker镜像\"\n"
        result += "- \"显示GitLab CI配置\"\n"
        result += "- \"查看项目依赖\"\n"
        
        return result
    
    def _generate_directory_tree(self, path: str, max_depth: int = 3) -> str:
        """生成目录树"""
        def _tree_helper(current_path: Path, prefix: str = "", depth: int = 0) -> List[str]:
            if depth > max_depth:
                return []
            
            items = []
            try:
                entries = sorted(current_path.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))
                for i, entry in enumerate(entries):
                    if entry.name.startswith('.'):
                        continue
                    
                    is_last = i == len(entries) - 1
                    current_prefix = "└── " if is_last else "├── "
                    items.append(f"{prefix}{current_prefix}{entry.name}")
                    
                    if entry.is_dir() and depth < max_depth:
                        next_prefix = prefix + ("    " if is_last else "│   ")
                        items.extend(_tree_helper(entry, next_prefix, depth + 1))
            except PermissionError:
                pass
            
            return items
        
        tree_lines = [Path(path).name]
        tree_lines.extend(_tree_helper(Path(path)))
        return "\n".join(tree_lines)
    
    def _get_project_stats(self, path: str) -> Dict[str, Any]:
        """获取项目统计信息"""
        stats = {}
        
        try:
            total_files = 0
            total_dirs = 0
            file_types = {}
            
            for root, dirs, files in os.walk(path):
                # 跳过隐藏目录
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                total_dirs += len(dirs)
                total_files += len(files)
                
                for file in files:
                    if not file.startswith('.'):
                        ext = Path(file).suffix.lower()
                        if ext:
                            file_types[ext] = file_types.get(ext, 0) + 1
            
            stats["总文件数"] = total_files
            stats["总目录数"] = total_dirs
            
            # 显示主要文件类型
            if file_types:
                top_types = sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:5]
                stats["主要文件类型"] = ", ".join([f"{ext}({count})" for ext, count in top_types])
            
        except Exception as e:
            stats["统计错误"] = str(e)
        
        return stats
