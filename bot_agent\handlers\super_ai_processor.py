"""
超级AI处理器 - 集成超级AI Agent的处理器
"""

import logging
import os
from typing import Dict, Any

from bot_agent.engines.super_agent import SuperAIAgent
from bot_agent.handlers.ai_response_handler import AIResponseHandler

logger = logging.getLogger(__name__)


class SuperAIProcessor:
    """
    超级AI处理器
    
    特点：
    1. 使用超级AI Agent进行完全自主的任务处理
    2. 无需人类确认，直接执行代码修改
    3. 自动测试和验证
    4. 智能错误修复
    5. 完整的中文响应
    """
    
    def __init__(self):
        """初始化超级AI处理器"""
        self.super_agent = SuperAIAgent()
        self.response_handler = AIResponseHandler()
        
        # 初始化GitLab客户端
        from bot_agent.clients.gitlab_client import GitLabClient
        self.gitlab_client = GitLabClient()
        
        logger.info("超级AI处理器初始化完成")
    
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理任务 - 使用超级AI Agent
        
        Args:
            task: 任务对象
            
        Returns:
            Dict: 处理结果
        """
        try:
            task_id = task.get("id", "unknown")
            title = task.get("title", "")
            
            logger.info(f"🚀 超级AI处理器开始处理任务: {task_id} - {title}")
            
            # 发送开始通知
            await self._send_start_notification(task)
            
            # 使用超级AI Agent执行任务
            agent_result = await self.super_agent.execute_task(task)
            
            # 发送完成通知
            await self._send_completion_notification(task, agent_result)
            
            return {
                "status": "success",
                "message": f"超级AI Agent成功处理任务: {task_id}",
                "agent_result": agent_result
            }
            
        except Exception as e:
            logger.error(f"超级AI处理器执行失败: {e}", exc_info=True)
            
            # 发送错误通知
            await self._send_error_notification(task, str(e))
            
            return {
                "status": "error",
                "message": f"超级AI处理器执行失败: {str(e)}"
            }
    
    async def _send_start_notification(self, task: Dict[str, Any]):
        """发送开始通知"""
        try:
            title = task.get("title", "")
            task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")
            
            start_message = f"""
## 🚀 超级AI Agent开始执行任务

**任务标题**: {title}
**任务类型**: {task_type}
**执行模式**: 完全自主（无需人类确认）

### 🤖 执行计划：
1. 🧠 深度需求分析和架构设计
2. 📋 制定详细实施计划
3. 💻 自主代码实现
4. 🧪 自动测试和验证
5. 📚 自动文档生成
6. 🔄 Git提交和推送

**预计执行时间**: 3-5分钟

请稍候，超级AI Agent正在为您工作...
"""
            
            # 发送到GitLab
            await self.response_handler.process_and_send_response(task, start_message)
            
        except Exception as e:
            logger.warning(f"发送开始通知失败: {e}")
    
    async def _send_completion_notification(self, task: Dict[str, Any], agent_result: Dict[str, Any]):
        """发送完成通知"""
        try:
            if agent_result.get("status") == "success":
                # 使用Agent生成的报告
                completion_message = agent_result.get("report", "任务执行完成")
            else:
                # 生成错误报告
                error_msg = agent_result.get("error", "未知错误")
                completion_message = f"""
## ❌ 超级AI Agent执行失败

**错误信息**: {error_msg}

### 🔧 建议解决方案：
1. 检查项目配置和环境变量
2. 确认Git仓库状态
3. 验证API密钥配置
4. 查看详细日志信息

请联系技术支持或重新尝试。
"""
            
            # 发送到GitLab
            await self.response_handler.process_and_send_response(task, completion_message)
            
        except Exception as e:
            logger.warning(f"发送完成通知失败: {e}")
    
    async def _send_error_notification(self, task: Dict[str, Any], error_message: str):
        """发送错误通知"""
        try:
            error_notification = f"""
## ❌ 超级AI Agent执行异常

**错误信息**: {error_message}

### 🔍 可能的原因：
1. 网络连接问题
2. API配置错误
3. 项目环境问题
4. 系统资源不足

### 🛠️ 解决建议：
1. 检查网络连接和API密钥
2. 确认项目目录和Git配置
3. 查看系统日志获取详细信息
4. 稍后重新尝试

如果问题持续存在，请联系技术支持。
"""
            
            # 发送到GitLab
            await self.response_handler.process_and_send_response(task, error_notification)
            
        except Exception as e:
            logger.warning(f"发送错误通知失败: {e}")


class SuperAgentTaskRouter:
    """
    超级Agent任务路由器 - 专门用于路由到超级AI Agent
    """
    
    def __init__(self):
        """初始化超级Agent任务路由器"""
        self.super_processor = SuperAIProcessor()
        logger.info("超级Agent任务路由器初始化完成")
    
    async def route_task(
        self,
        title: str,
        description: str,
        source: str,
        source_id: str,
        labels: list = None,
        metadata: dict = None
    ) -> Dict[str, Any]:
        """
        路由任务到超级AI Agent
        
        Args:
            title: 任务标题
            description: 任务描述
            source: 任务来源
            source_id: 任务来源ID
            labels: 任务标签
            metadata: 元数据
            
        Returns:
            Dict: 路由结果
        """
        try:
            import uuid
            from datetime import datetime
            
            # 创建任务对象
            task = {
                "id": str(uuid.uuid4()),
                "title": title,
                "description": description,
                "source": source,
                "source_id": source_id,
                "labels": labels or [],
                "created_at": datetime.utcnow().isoformat(),
                "status": "pending",
                "analysis": {
                    "task_type": "CODE_GENERATION",  # 超级Agent可以处理所有类型
                    "priority": "HIGH",  # 超级Agent优先处理
                    "component": "super_agent"
                },
                "target_component": "super_agent",
                "metadata": metadata or {},
            }
            
            logger.info(f"路由任务到超级AI Agent: {task['id']} - {title}")
            
            # 使用超级AI处理器处理任务
            result = await self.super_processor.process_task(task)
            
            return {
                "task_id": task["id"],
                "target_component": "super_agent",
                "status": result["status"],
                "message": result["message"],
            }
            
        except Exception as e:
            logger.error(f"超级Agent任务路由失败: {e}", exc_info=True)
            return {
                "task_id": "unknown",
                "target_component": "super_agent",
                "status": "error",
                "message": f"任务路由失败: {str(e)}",
            }
