# 应用镜像：基于基础镜像，只添加应用代码
ARG BASE_IMAGE=***************:5050/docker/ci-images/aider-plus-base:latest
FROM ${BASE_IMAGE}

# 复制应用代码
COPY aider/ /app/aider/
COPY bot_agent/ /app/bot_agent/

# 设置工作目录
VOLUME /workspace
WORKDIR /app  # 将工作目录设置为 /app，确保模块可以被正确导入

# 暴露端口
EXPOSE 8000 8080

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动脚本
COPY start-unified.sh /app/
RUN chmod +x /app/start-unified.sh

# 启动命令
ENV PYTHONPATH=/app
CMD ["/app/start-unified.sh"]
