# AI模型配置示例文件
# 复制此文件为 .env 并填入你的配置

# OpenRouter API密钥
OPENROUTER_API_KEY=your-openrouter-api-key-here

# 推理分析模型 - 用于任务分析、推理、决策
# 推荐使用DeepSeek R1，专门优化的推理模型
ANALYSIS_MODEL=openrouter/deepseek/deepseek-r1:free

# 代码生成模型 - 用于Aider代码生成和修改
# 推荐使用DeepSeek Chat，专门优化的代码生成模型
AIDER_MODEL=openrouter/deepseek/deepseek-chat

# 通用对话模型 - 用于一般对话（可选）
CHAT_MODEL=openrouter/deepseek/deepseek-chat

# 其他可选配置
# GITLAB_TOKEN=your-gitlab-token
# GITLAB_URL=http://192.168.123.103:5050
# PROJECT_DOWNLOAD_DIR=E:\aider-git-repos
