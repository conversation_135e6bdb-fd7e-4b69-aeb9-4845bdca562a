#!/usr/bin/env python3
"""
测试增强的作业失败分析器
验证改进后的功能是否达到预期效果
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from bot_agent.handlers.enhanced_job_failure_analyzer import EnhancedJobFailureAnalyzer
from bot_agent.utils.logging_config import get_logger

logger = get_logger(__name__)


async def test_enhanced_analyzer():
    """测试增强分析器"""
    print("🚀 开始测试增强作业失败分析器")
    print("=" * 80)
    
    # 创建分析器实例
    analyzer = EnhancedJobFailureAnalyzer()
    
    # 测试参数 - 使用之前失败的任务ID
    job_id = 728  # 之前分析的作业ID
    project_id = 3  # ai-proxy项目
    
    print(f"📋 测试参数:")
    print(f"  - Job ID: {job_id}")
    print(f"  - Project ID: {project_id}")
    print()
    
    start_time = time.time()
    
    try:
        # 执行增强分析
        print("🔍 开始执行增强分析...")
        result = await analyzer.analyze_and_fix_job_failure(job_id, project_id)
        
        duration = time.time() - start_time
        
        print("\n" + "=" * 80)
        print("📊 分析结果")
        print("=" * 80)
        
        print(f"✅ 分析状态: {'成功' if result.success else '失败'}")
        print(f"⏱️  总耗时: {duration:.2f}秒")
        print(f"🏷️  作业名称: {result.job_name}")
        print(f"❌ 失败原因: {result.failure_reason}")
        print(f"🔍 错误数量: {len(result.error_details)}")
        print(f"🛠️  修复命令: {len(result.fix_commands)}")
        print(f"✅ 验证步骤: {len(result.verification_steps)}")
        
        print(f"\n📝 详细信息:")
        print(f"  - 消息: {result.message}")
        
        if result.error_details:
            print(f"\n🚨 识别的错误:")
            for i, error in enumerate(result.error_details[:5], 1):
                print(f"  {i}. {error}")
            if len(result.error_details) > 5:
                print(f"  ... 还有 {len(result.error_details) - 5} 个错误")
        
        if result.fix_commands:
            print(f"\n🛠️  修复命令:")
            for i, cmd in enumerate(result.fix_commands[:5], 1):
                print(f"  {i}. {cmd}")
            if len(result.fix_commands) > 5:
                print(f"  ... 还有 {len(result.fix_commands) - 5} 个命令")
        
        # 执行结果
        print(f"\n⚙️  执行结果:")
        exec_results = result.execution_results
        print(f"  - 状态: {exec_results.get('status', 'N/A')}")
        print(f"  - 消息: {exec_results.get('message', 'N/A')}")
        print(f"  - 成功率: {exec_results.get('success_count', 0)}/{exec_results.get('total_count', 0)}")
        
        # Git提交结果
        print(f"\n📤 Git提交结果:")
        git_results = result.git_commit_results
        print(f"  - 状态: {git_results.get('status', 'N/A')}")
        print(f"  - 消息: {git_results.get('message', 'N/A')}")
        print(f"  - 推送成功: {'✅' if git_results.get('push_success', False) else '❌'}")
        
        # 验证结果
        print(f"\n✅ 验证结果:")
        verify_results = result.verification_results
        print(f"  - 状态: {verify_results.get('status', 'N/A')}")
        print(f"  - 消息: {verify_results.get('message', 'N/A')}")
        print(f"  - 通过率: {verify_results.get('success_count', 0)}/{verify_results.get('total_count', 0)}")
        
        # 分析摘要
        print(f"\n📋 分析摘要:")
        print(result.analysis_summary)
        
        # 对比评估
        print(f"\n" + "=" * 80)
        print("🎯 改进效果评估")
        print("=" * 80)
        
        improvements = []
        
        # 检查是否真正获取了数据
        if len(result.error_details) > 1 and not any("未识别到具体错误" in error for error in result.error_details):
            improvements.append("✅ 成功识别了具体错误信息")
        else:
            improvements.append("❌ 仍未能识别具体错误")
        
        # 检查是否执行了命令
        if exec_results.get('total_count', 0) > 0:
            improvements.append(f"✅ 实际执行了 {exec_results.get('total_count', 0)} 个修复命令")
        else:
            improvements.append("❌ 没有执行任何修复命令")
        
        # 检查是否进行了验证
        if verify_results.get('total_count', 0) > 0:
            improvements.append(f"✅ 进行了 {verify_results.get('total_count', 0)} 个验证步骤")
        else:
            improvements.append("❌ 没有进行验证")
        
        # 检查Git操作
        if git_results.get('push_success', False):
            improvements.append("✅ 成功推送修复到Git仓库")
        elif len(git_results.get('commands', [])) > 0:
            improvements.append("⚠️ 执行了Git操作但未成功推送")
        else:
            improvements.append("❌ 没有执行Git操作")
        
        for improvement in improvements:
            print(f"  {improvement}")
        
        # 总体评分
        success_count = sum(1 for imp in improvements if imp.startswith("✅"))
        total_count = len(improvements)
        score = (success_count / total_count) * 100
        
        print(f"\n📈 总体改进评分: {score:.1f}% ({success_count}/{total_count})")
        
        if score >= 80:
            print("🎉 改进效果: 优秀！系统已显著改善")
        elif score >= 60:
            print("👍 改进效果: 良好，还有提升空间")
        elif score >= 40:
            print("⚠️ 改进效果: 一般，需要进一步优化")
        else:
            print("❌ 改进效果: 不佳，需要重新设计")
        
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        print(f"\n❌ 测试失败: {e}")
        print(f"⏱️  耗时: {duration:.2f}秒")
        return None


async def main():
    """主函数"""
    print("🧪 增强作业失败分析器测试")
    print("测试目标: 验证改进后的分析器是否真正解决了之前的问题")
    print()
    
    result = await test_enhanced_analyzer()
    
    print(f"\n" + "=" * 80)
    print("🏁 测试完成")
    print("=" * 80)
    
    if result and result.success:
        print("✅ 测试成功完成")
        print("💡 建议: 可以将此增强分析器集成到生产环境")
    else:
        print("❌ 测试未达到预期")
        print("💡 建议: 需要进一步调试和优化")


if __name__ == "__main__":
    asyncio.run(main())
