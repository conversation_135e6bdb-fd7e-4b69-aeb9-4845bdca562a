/* 对话分析系统自定义样式 */

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* 页面加载动画 */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* 侧边栏增强 */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

/* 卡片增强 */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

/* 指标卡片 */
.metric-card {
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.metric-value {
    font-family: 'Arial', sans-serif;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 状态徽章增强 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #b8dacc;
}

.status-failed {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #f1b0b7;
}

.status-progress {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border: 1px solid #b6dfea;
}

/* 对话轮次样式增强 */
.conversation-round {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.conversation-round:hover {
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    transform: translateX(5px);
}

.conversation-round.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #fff 0%, #f8fff9 100%);
}

.conversation-round.failed {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff 0%, #fff8f8 100%);
}

/* 代码块增强 */
.code-block {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.5;
    max-height: 400px;
    overflow-y: auto;
    position: relative;
}

.code-block::-webkit-scrollbar {
    width: 8px;
}

.code-block::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.code-block::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.code-block::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 搜索过滤器增强 */
.search-filters {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

/* 按钮增强 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

/* 表格增强 */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: scale(1.01);
}

/* 加载动画增强 */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

/* 响应式增强 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .metric-card {
        margin-bottom: 15px;
    }
    
    .conversation-round {
        margin: 15px 0;
        padding: 15px;
    }
    
    .code-block {
        font-size: 0.8em;
        max-height: 200px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .code-block {
        background: #1a202c;
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .search-filters {
        background: #2d3748;
        border-color: #4a5568;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .btn-toolbar,
    .search-filters {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .conversation-round {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}
