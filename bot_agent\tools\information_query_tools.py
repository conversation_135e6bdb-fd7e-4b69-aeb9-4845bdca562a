"""
信息查询工具 - 用于工具路由器的信息查询功能
"""

import logging
import os
import re
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Optional

from .base_tool import BaseTool, ToolResult

logger = logging.getLogger(__name__)


class InformationQueryTools(BaseTool):
    """
    信息查询工具
    
    提供各种项目信息查询功能，包括：
    - Docker镜像查询
    - 依赖信息查询
    - 项目结构查询
    - 配置文件查询
    """
    
    def __init__(self):
        super().__init__()
        self.name = "information_query"
        self.description = "查询项目信息，如Docker镜像、依赖、结构等"
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        执行信息查询
        
        Args:
            query_type: 查询类型 (docker, dependencies, structure, config)
            project_path: 项目路径
            **kwargs: 其他参数
            
        Returns:
            ToolResult: 查询结果
        """
        try:
            query_type = kwargs.get('query_type', 'auto')
            project_path = kwargs.get('project_path', '.')
            query_text = kwargs.get('query_text', '')
            
            logger.info(f"执行信息查询: {query_type} in {project_path}")
            
            # 自动检测查询类型
            if query_type == 'auto':
                query_type = self._detect_query_type(query_text)
            
            # 执行相应的查询
            if query_type == 'docker':
                result = await self._query_docker_images(project_path)
            elif query_type == 'dependencies':
                result = await self._query_dependencies(project_path)
            elif query_type == 'structure':
                result = await self._query_project_structure(project_path)
            elif query_type == 'config':
                result = await self._query_configurations(project_path)
            elif query_type == 'git':
                result = await self._query_git_info(project_path)
            else:
                result = await self._query_general_info(project_path)
            
            return ToolResult(
                success=True,
                data=result,
                message=f"信息查询完成: {query_type}",
                metadata={'query_type': query_type, 'project_path': project_path}
            )
            
        except Exception as e:
            logger.error(f"信息查询失败: {e}", exc_info=True)
            return ToolResult(
                success=False,
                error=str(e),
                message="信息查询失败"
            )
    
    def _detect_query_type(self, query_text: str) -> str:
        """检测查询类型"""
        query_lower = query_text.lower()
        
        if any(keyword in query_lower for keyword in ['镜像', 'image', 'docker', '容器']):
            return 'docker'
        elif any(keyword in query_lower for keyword in ['依赖', 'dependency', 'requirements', 'package']):
            return 'dependencies'
        elif any(keyword in query_lower for keyword in ['结构', 'structure', '目录', '文件']):
            return 'structure'
        elif any(keyword in query_lower for keyword in ['配置', 'config', 'gitlab', 'ci/cd']):
            return 'config'
        elif any(keyword in query_lower for keyword in ['git', '分支', 'branch', '提交']):
            return 'git'
        else:
            return 'general'
    
    async def _query_docker_images(self, project_path: str) -> Dict[str, Any]:
        """查询Docker镜像"""
        result = {
            'type': 'docker_images',
            'images': [],
            'files_analyzed': [],
            'summary': ''
        }
        
        # 检查Docker相关文件
        docker_files = [
            ('Dockerfile', 'Dockerfile'),
            ('docker-compose.yml', 'Docker Compose'),
            ('.gitlab-ci.yml', 'GitLab CI'),
            ('docker-compose.yaml', 'Docker Compose (yaml)')
        ]
        
        found_images = set()
        
        for filename, description in docker_files:
            file_path = Path(project_path) / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    images = self._extract_docker_images(content)
                    if images:
                        result['files_analyzed'].append({
                            'file': filename,
                            'description': description,
                            'images': images
                        })
                        found_images.update(images)
                        
                except Exception as e:
                    logger.warning(f"读取文件 {filename} 失败: {e}")
        
        result['images'] = list(found_images)
        result['summary'] = f"找到 {len(found_images)} 个不同的Docker镜像"
        
        return result
    
    def _extract_docker_images(self, content: str) -> List[str]:
        """从文件内容中提取Docker镜像"""
        images = []
        
        patterns = [
            r'FROM\s+([^\s\n]+)',  # Dockerfile FROM
            r'image:\s*([^\s\n]+)',  # docker-compose image
            r'image:\s+([^\s\n]+)',  # GitLab CI image
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                image = match.strip().strip('"\'')
                if image and not image.startswith('$'):
                    images.append(image)
        
        return list(set(images))
    
    async def _query_dependencies(self, project_path: str) -> Dict[str, Any]:
        """查询项目依赖"""
        result = {
            'type': 'dependencies',
            'files': [],
            'summary': ''
        }
        
        # 检查依赖文件
        dep_files = [
            ('requirements.txt', 'Python依赖'),
            ('pyproject.toml', 'Python项目配置'),
            ('package.json', 'Node.js依赖'),
            ('Cargo.toml', 'Rust依赖'),
            ('go.mod', 'Go模块'),
            ('pom.xml', 'Maven依赖'),
        ]
        
        total_deps = 0
        
        for filename, description in dep_files:
            file_path = Path(project_path) / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 简单计算依赖数量
                    if filename == 'requirements.txt':
                        deps = [line.strip() for line in content.split('\n') 
                               if line.strip() and not line.startswith('#')]
                        total_deps += len(deps)
                    
                    result['files'].append({
                        'file': filename,
                        'description': description,
                        'size': len(content),
                        'preview': content[:200] + '...' if len(content) > 200 else content
                    })
                    
                except Exception as e:
                    logger.warning(f"读取依赖文件 {filename} 失败: {e}")
        
        result['summary'] = f"找到 {len(result['files'])} 个依赖文件"
        if total_deps > 0:
            result['summary'] += f"，约 {total_deps} 个依赖包"
        
        return result
    
    async def _query_project_structure(self, project_path: str) -> Dict[str, Any]:
        """查询项目结构"""
        result = {
            'type': 'project_structure',
            'tree': '',
            'stats': {},
            'summary': ''
        }
        
        try:
            # 生成目录树
            tree_lines = []
            stats = {'files': 0, 'dirs': 0, 'types': {}}
            
            def walk_directory(path: Path, prefix: str = "", max_depth: int = 3, current_depth: int = 0):
                if current_depth > max_depth:
                    return
                
                try:
                    entries = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))
                    for i, entry in enumerate(entries):
                        if entry.name.startswith('.'):
                            continue
                        
                        is_last = i == len(entries) - 1
                        current_prefix = "└── " if is_last else "├── "
                        tree_lines.append(f"{prefix}{current_prefix}{entry.name}")
                        
                        if entry.is_file():
                            stats['files'] += 1
                            ext = entry.suffix.lower()
                            if ext:
                                stats['types'][ext] = stats['types'].get(ext, 0) + 1
                        elif entry.is_dir():
                            stats['dirs'] += 1
                            if current_depth < max_depth:
                                next_prefix = prefix + ("    " if is_last else "│   ")
                                walk_directory(entry, next_prefix, max_depth, current_depth + 1)
                                
                except PermissionError:
                    pass
            
            project_name = Path(project_path).name
            tree_lines.append(project_name)
            walk_directory(Path(project_path))
            
            result['tree'] = '\n'.join(tree_lines)
            result['stats'] = stats
            result['summary'] = f"项目包含 {stats['files']} 个文件，{stats['dirs']} 个目录"
            
        except Exception as e:
            logger.error(f"生成项目结构失败: {e}")
            result['summary'] = f"生成项目结构失败: {e}"
        
        return result
    
    async def _query_configurations(self, project_path: str) -> Dict[str, Any]:
        """查询配置文件"""
        result = {
            'type': 'configurations',
            'configs': [],
            'summary': ''
        }
        
        # 检查配置文件
        config_files = [
            ('.gitlab-ci.yml', 'GitLab CI/CD配置'),
            ('docker-compose.yml', 'Docker Compose配置'),
            ('config.py', 'Python配置'),
            ('settings.py', 'Django设置'),
            ('.env', '环境变量'),
            ('pyproject.toml', 'Python项目配置'),
        ]
        
        for filename, description in config_files:
            file_path = Path(project_path) / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    result['configs'].append({
                        'file': filename,
                        'description': description,
                        'size': len(content),
                        'preview': content[:300] + '...' if len(content) > 300 else content
                    })
                    
                except Exception as e:
                    logger.warning(f"读取配置文件 {filename} 失败: {e}")
        
        result['summary'] = f"找到 {len(result['configs'])} 个配置文件"
        return result
    
    async def _query_git_info(self, project_path: str) -> Dict[str, Any]:
        """查询Git信息"""
        result = {
            'type': 'git_info',
            'branch': '',
            'remotes': [],
            'recent_commits': [],
            'summary': ''
        }
        
        try:
            original_cwd = os.getcwd()
            os.chdir(project_path)
            
            # 获取当前分支
            try:
                branch = subprocess.check_output(
                    ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                    text=True, encoding="utf-8"
                ).strip()
                result['branch'] = branch
            except subprocess.CalledProcessError:
                pass
            
            # 获取远程仓库
            try:
                remotes_output = subprocess.check_output(
                    ["git", "remote", "-v"],
                    text=True, encoding="utf-8"
                ).strip()
                if remotes_output:
                    result['remotes'] = remotes_output.split('\n')
            except subprocess.CalledProcessError:
                pass
            
            # 获取最近提交
            try:
                commits_output = subprocess.check_output(
                    ["git", "log", "--oneline", "-5"],
                    text=True, encoding="utf-8"
                ).strip()
                if commits_output:
                    result['recent_commits'] = commits_output.split('\n')
            except subprocess.CalledProcessError:
                pass
            
            result['summary'] = f"当前分支: {result['branch']}, {len(result['remotes'])} 个远程仓库"
            
        except Exception as e:
            logger.error(f"获取Git信息失败: {e}")
            result['summary'] = f"获取Git信息失败: {e}"
        finally:
            os.chdir(original_cwd)
        
        return result
    
    async def _query_general_info(self, project_path: str) -> Dict[str, Any]:
        """查询通用信息"""
        result = {
            'type': 'general_info',
            'path': project_path,
            'exists': os.path.exists(project_path),
            'summary': f"项目路径: {project_path}"
        }
        
        if result['exists']:
            try:
                # 基本统计
                total_files = 0
                total_size = 0
                
                for root, dirs, files in os.walk(project_path):
                    # 跳过隐藏目录
                    dirs[:] = [d for d in dirs if not d.startswith('.')]
                    
                    for file in files:
                        if not file.startswith('.'):
                            file_path = os.path.join(root, file)
                            try:
                                total_files += 1
                                total_size += os.path.getsize(file_path)
                            except OSError:
                                pass
                
                result['total_files'] = total_files
                result['total_size'] = total_size
                result['summary'] += f", {total_files} 个文件"
                
            except Exception as e:
                logger.error(f"获取项目统计失败: {e}")
        
        return result
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力列表"""
        return [
            "查询Docker镜像",
            "查询项目依赖",
            "查询项目结构",
            "查询配置文件",
            "查询Git信息",
            "查询通用项目信息"
        ]
