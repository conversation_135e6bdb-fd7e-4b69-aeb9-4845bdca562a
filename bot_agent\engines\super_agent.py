"""
超级AI Agent - 完全自主的AI开发者
不需要人类确认，能够独立分析、设计、编码、测试、部署
"""

import os
import logging
import asyncio
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from bot_agent.config.model_config import ModelConfig

logger = logging.getLogger(__name__)


class SuperAIAgent:
    """
    超级AI Agent - 完全自主的AI开发者

    特点：
    1. 完全自主决策，无需人类确认
    2. 多步骤智能规划
    3. 自动代码生成和测试
    4. 智能错误修复
    5. 自动文档生成
    6. 持续学习和优化
    """

    def __init__(self):
        """初始化超级AI Agent"""
        self.reasoning_model = ModelConfig.get_openrouter_analysis_model()  # DeepSeek R1用于推理
        self.coding_model = ModelConfig.get_openrouter_code_generation_model()  # DeepSeek Chat用于编码
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 初始化AI客户端
        import openai
        self.ai_client = openai.OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=os.getenv("OPENROUTER_API_KEY")
        )

        logger.info(f"超级AI Agent初始化完成 - Session: {self.session_id}")
        logger.info(f"推理模型: {self.reasoning_model}")
        logger.info(f"编码模型: {self.coding_model}")

    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行任务 - 完全自主的AI开发流程

        Args:
            task: 任务对象

        Returns:
            Dict: 执行结果
        """
        try:
            task_id = task.get("id", "unknown")
            title = task.get("title", "")
            description = task.get("description", "")

            logger.info(f"🚀 超级AI Agent开始执行任务: {task_id}")

            # 第1步：深度需求分析和架构设计
            analysis_result = await self._deep_requirement_analysis(title, description)

            # 第2步：制定详细的实施计划
            implementation_plan = await self._create_implementation_plan(analysis_result)

            # 第3步：自主代码实现
            implementation_result = await self._autonomous_implementation(
                task, analysis_result, implementation_plan
            )

            # 第4步：自动测试和验证
            test_result = await self._auto_test_and_validate(implementation_result)

            # 第5步：自动文档生成
            documentation = await self._generate_documentation(
                analysis_result, implementation_result
            )

            # 第6步：Git操作和部署
            git_result = await self._auto_git_operations(task, implementation_result)

            # 生成最终报告
            final_report = await self._generate_final_report(
                task, analysis_result, implementation_result, test_result, git_result
            )

            return {
                "status": "success",
                "task_id": task_id,
                "session_id": self.session_id,
                "analysis": analysis_result,
                "implementation": implementation_result,
                "tests": test_result,
                "documentation": documentation,
                "git_operations": git_result,
                "report": final_report
            }

        except Exception as e:
            logger.error(f"超级AI Agent执行失败: {e}", exc_info=True)
            return {
                "status": "error",
                "error": str(e),
                "session_id": self.session_id
            }

    async def _deep_requirement_analysis(self, title: str, description: str) -> Dict:
        """
        深度需求分析 - 使用DeepSeek R1进行复杂推理
        """
        logger.info("🧠 开始深度需求分析...")

        prompt = f"""
你是一个资深的软件架构师和产品经理。请对以下需求进行深度分析：

需求标题: {title}
需求描述: {description}

请进行全面的需求分析，包括：

1. 功能需求分析
   - 核心功能点
   - 用户场景
   - 业务流程
   - 数据流向

2. 技术需求分析
   - 技术栈选择
   - 架构设计
   - 性能要求
   - 安全考虑

3. 实现方案设计
   - 模块划分
   - 接口设计
   - 数据结构
   - 算法选择

4. 风险评估
   - 技术风险
   - 业务风险
   - 时间风险
   - 质量风险

请用中文详细分析，并以JSON格式返回结构化的分析结果。
"""

        response = self.ai_client.chat.completions.create(
            model=self.reasoning_model,
            messages=[
                {"role": "system", "content": "你是一个世界级的软件架构师，擅长深度需求分析和系统设计。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=4000
        )

        analysis_text = response.choices[0].message.content
        logger.info(f"需求分析完成: {len(analysis_text)} 字符")

        return {
            "analysis_text": analysis_text,
            "timestamp": datetime.now().isoformat()
        }

    async def _create_implementation_plan(self, analysis: Dict) -> Dict:
        """
        制定详细的实施计划
        """
        logger.info("📋 制定实施计划...")

        prompt = f"""
基于以下需求分析，制定详细的实施计划：

{analysis['analysis_text']}

请制定一个完整的实施计划，包括：

1. 文件结构设计
   - 需要创建的文件
   - 目录结构
   - 文件职责

2. 实施步骤
   - 按优先级排序的开发步骤
   - 每个步骤的具体任务
   - 预估时间

3. 代码实现策略
   - 核心算法
   - 关键函数
   - 数据处理流程

4. 测试策略
   - 单元测试计划
   - 集成测试计划
   - 验收测试计划

请用中文详细说明，并提供可执行的具体步骤。
"""

        response = self.ai_client.chat.completions.create(
            model=self.reasoning_model,
            messages=[
                {"role": "system", "content": "你是一个经验丰富的项目经理和技术负责人。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=3000
        )

        plan_text = response.choices[0].message.content
        logger.info(f"实施计划制定完成: {len(plan_text)} 字符")

        return {
            "plan_text": plan_text,
            "timestamp": datetime.now().isoformat()
        }

    async def _autonomous_implementation(
        self, task: Dict, analysis: Dict, plan: Dict
    ) -> Dict:
        """
        自主代码实现 - 不需要人类确认，直接生成和修改代码
        """
        logger.info("💻 开始自主代码实现...")

        # 获取项目路径
        project_path = self._get_project_path(task)
        if not project_path:
            raise Exception("无法获取项目路径")

        # 切换到项目目录
        original_cwd = os.getcwd()
        os.chdir(project_path)

        try:
            # 分析现有代码结构
            code_structure = await self._analyze_existing_code(project_path)

            # 生成具体的代码实现
            implementation_steps = await self._generate_implementation_steps(
                analysis, plan, code_structure
            )

            # 执行每个实现步骤
            results = []
            for step in implementation_steps:
                step_result = await self._execute_implementation_step(step, project_path)
                results.append(step_result)

            return {
                "project_path": project_path,
                "code_structure": code_structure,
                "implementation_steps": implementation_steps,
                "results": results,
                "timestamp": datetime.now().isoformat()
            }

        finally:
            os.chdir(original_cwd)

    async def _generate_implementation_steps(
        self, analysis: Dict, plan: Dict, code_structure: Dict
    ) -> List[Dict]:
        """
        生成具体的实现步骤
        """
        prompt = f"""
基于以下信息，生成具体的代码实现步骤：

需求分析:
{analysis['analysis_text']}

实施计划:
{plan['plan_text']}

现有代码结构:
{json.dumps(code_structure, ensure_ascii=False, indent=2)}

请生成具体的实现步骤，每个步骤包括：
1. 步骤描述
2. 要创建/修改的文件
3. 具体的代码内容
4. 实现说明

要求：
- 代码必须完整可运行
- 包含详细的中文注释
- 遵循最佳实践
- 考虑错误处理
- 包含必要的测试

请以JSON格式返回步骤列表。
"""

        response = self.ai_client.chat.completions.create(
            model=self.coding_model,
            messages=[
                {"role": "system", "content": "你是一个顶级的全栈开发工程师，能够编写高质量的代码。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=6000
        )

        steps_text = response.choices[0].message.content

        # 尝试解析JSON，如果失败则使用文本解析
        try:
            if "```json" in steps_text:
                start = steps_text.find("```json") + 7
                end = steps_text.find("```", start)
                json_content = steps_text[start:end].strip()
            else:
                json_content = steps_text

            steps = json.loads(json_content)
            return steps if isinstance(steps, list) else [steps]
        except:
            # 如果JSON解析失败，创建一个基本的步骤
            return [{
                "description": "代码实现",
                "file_path": "implementation.py",
                "code_content": steps_text,
                "explanation": "AI生成的代码实现"
            }]

    def _get_project_path(self, task: Dict) -> Optional[str]:
        """获取项目路径"""
        metadata = task.get("metadata", {})
        project_name = metadata.get("project_name", "aider-plus")
        projects_dir = os.getenv("PROJECTS_DIR", "E:\\aider-git-repos")
        return os.path.join(projects_dir, project_name)

    async def _analyze_existing_code(self, project_path: str) -> Dict:
        """分析现有代码结构"""
        structure = {
            "directories": [],
            "files": [],
            "key_files": {}
        }

        try:
            for root, dirs, files in os.walk(project_path):
                # 跳过隐藏目录和常见的忽略目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]

                rel_root = os.path.relpath(root, project_path)
                if rel_root != '.':
                    structure["directories"].append(rel_root)

                for file in files:
                    if not file.startswith('.') and file.endswith(('.py', '.js', '.json', '.md', '.yml', '.yaml')):
                        rel_path = os.path.relpath(os.path.join(root, file), project_path)
                        structure["files"].append(rel_path)

                        # 读取关键文件内容
                        if file in ['requirements.txt', 'package.json', 'README.md', 'main.py', 'app.py']:
                            try:
                                with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                                    structure["key_files"][rel_path] = f.read()[:1000]  # 只读取前1000字符
                            except:
                                pass
        except Exception as e:
            logger.warning(f"分析代码结构失败: {e}")

        return structure

    async def _execute_implementation_step(self, step: Dict, project_path: str) -> Dict:
        """
        执行单个实现步骤 - 直接创建/修改文件
        """
        try:
            file_path = step.get("file_path", "")
            code_content = step.get("code_content", "")
            description = step.get("description", "")

            if not file_path or not code_content:
                return {"status": "skipped", "reason": "缺少文件路径或代码内容"}

            # 确保目录存在
            full_path = os.path.join(project_path, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # 写入文件
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(code_content)

            logger.info(f"✅ 创建/修改文件: {file_path}")

            return {
                "status": "success",
                "file_path": file_path,
                "description": description,
                "lines_written": len(code_content.split('\n'))
            }

        except Exception as e:
            logger.error(f"执行实现步骤失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "file_path": step.get("file_path", "unknown")
            }

    async def _auto_test_and_validate(self, implementation: Dict) -> Dict:
        """
        自动测试和验证
        """
        logger.info("🧪 开始自动测试和验证...")

        project_path = implementation.get("project_path", "")
        if not project_path:
            return {"status": "skipped", "reason": "无项目路径"}

        test_results = []

        try:
            # 1. 语法检查
            syntax_result = await self._check_syntax(project_path)
            test_results.append(syntax_result)

            # 2. 运行现有测试
            existing_tests_result = await self._run_existing_tests(project_path)
            test_results.append(existing_tests_result)

            # 3. 生成并运行新测试
            new_tests_result = await self._generate_and_run_tests(project_path, implementation)
            test_results.append(new_tests_result)

            return {
                "status": "completed",
                "test_results": test_results,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"自动测试失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "partial_results": test_results
            }

    async def _check_syntax(self, project_path: str) -> Dict:
        """检查Python语法"""
        try:
            # 查找所有Python文件
            python_files = []
            for root, dirs, files in os.walk(project_path):
                for file in files:
                    if file.endswith('.py'):
                        python_files.append(os.path.join(root, file))

            syntax_errors = []
            for file_path in python_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        compile(f.read(), file_path, 'exec')
                except SyntaxError as e:
                    syntax_errors.append({
                        "file": file_path,
                        "line": e.lineno,
                        "error": str(e)
                    })

            return {
                "test_type": "syntax_check",
                "status": "passed" if not syntax_errors else "failed",
                "files_checked": len(python_files),
                "errors": syntax_errors
            }

        except Exception as e:
            return {
                "test_type": "syntax_check",
                "status": "error",
                "error": str(e)
            }

    async def _run_existing_tests(self, project_path: str) -> Dict:
        """运行现有测试"""
        try:
            # 查找测试文件
            test_files = []
            for root, dirs, files in os.walk(project_path):
                for file in files:
                    if file.startswith('test_') and file.endswith('.py'):
                        test_files.append(os.path.join(root, file))

            if not test_files:
                return {
                    "test_type": "existing_tests",
                    "status": "skipped",
                    "reason": "未找到测试文件"
                }

            # 运行pytest
            result = subprocess.run(
                ["python", "-m", "pytest", "-v"] + test_files,
                cwd=project_path,
                capture_output=True,
                text=True,
                timeout=60
            )

            return {
                "test_type": "existing_tests",
                "status": "passed" if result.returncode == 0 else "failed",
                "output": result.stdout,
                "errors": result.stderr,
                "test_files": len(test_files)
            }

        except subprocess.TimeoutExpired:
            return {
                "test_type": "existing_tests",
                "status": "timeout",
                "reason": "测试超时"
            }
        except Exception as e:
            return {
                "test_type": "existing_tests",
                "status": "error",
                "error": str(e)
            }

    async def _generate_and_run_tests(self, project_path: str, implementation: Dict) -> Dict:
        """生成并运行新测试"""
        try:
            # 为新实现的功能生成测试
            test_code = await self._generate_test_code(implementation)

            if not test_code:
                return {
                    "test_type": "generated_tests",
                    "status": "skipped",
                    "reason": "无法生成测试代码"
                }

            # 写入测试文件
            test_file_path = os.path.join(project_path, f"test_generated_{self.session_id}.py")
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(test_code)

            # 运行生成的测试
            result = subprocess.run(
                ["python", "-m", "pytest", test_file_path, "-v"],
                cwd=project_path,
                capture_output=True,
                text=True,
                timeout=30
            )

            return {
                "test_type": "generated_tests",
                "status": "passed" if result.returncode == 0 else "failed",
                "test_file": test_file_path,
                "output": result.stdout,
                "errors": result.stderr
            }

        except Exception as e:
            return {
                "test_type": "generated_tests",
                "status": "error",
                "error": str(e)
            }

    async def _generate_test_code(self, implementation: Dict) -> str:
        """生成测试代码"""
        prompt = f"""
基于以下实现结果，生成完整的Python测试代码：

实现结果:
{json.dumps(implementation, ensure_ascii=False, indent=2)}

请生成：
1. 完整的pytest测试文件
2. 包含多个测试用例
3. 测试正常情况和边界情况
4. 包含中文注释
5. 使用mock进行隔离测试

要求：
- 代码必须可以直接运行
- 包含必要的import语句
- 测试覆盖主要功能点
- 使用断言验证结果

请只返回Python代码，不要其他说明。
"""

        response = self.ai_client.chat.completions.create(
            model=self.coding_model,
            messages=[
                {"role": "system", "content": "你是一个测试专家，擅长编写高质量的单元测试。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )

        return response.choices[0].message.content

    async def _generate_documentation(self, analysis: Dict, implementation: Dict) -> Dict:
        """自动生成文档"""
        logger.info("📚 生成项目文档...")

        prompt = f"""
基于以下信息，生成完整的项目文档：

需求分析:
{analysis['analysis_text']}

实现结果:
{json.dumps(implementation, ensure_ascii=False, indent=2)}

请生成：
1. README.md - 项目介绍和使用说明
2. API文档 - 接口说明
3. 架构文档 - 系统设计说明
4. 部署文档 - 安装和配置说明

要求：
- 使用Markdown格式
- 包含代码示例
- 详细的中文说明
- 结构清晰，易于理解

请以JSON格式返回，包含每个文档的文件名和内容。
"""

        response = self.ai_client.chat.completions.create(
            model=self.coding_model,
            messages=[
                {"role": "system", "content": "你是一个技术文档专家，擅长编写清晰易懂的技术文档。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=3000
        )

        doc_text = response.choices[0].message.content

        return {
            "documentation_text": doc_text,
            "timestamp": datetime.now().isoformat()
        }

    async def _auto_git_operations(self, task: Dict, implementation: Dict) -> Dict:
        """自动Git操作"""
        logger.info("🔄 执行Git操作...")

        project_path = implementation.get("project_path", "")
        if not project_path:
            return {"status": "skipped", "reason": "无项目路径"}

        try:
            # 切换到项目目录
            original_cwd = os.getcwd()
            os.chdir(project_path)

            git_operations = []

            # 1. 检查Git状态
            status_result = subprocess.run(
                ["git", "status", "--porcelain"],
                capture_output=True, text=True, encoding='utf-8'
            )

            if status_result.stdout.strip():
                # 2. 添加所有更改
                add_result = subprocess.run(
                    ["git", "add", "."],
                    capture_output=True, text=True, encoding='utf-8'
                )
                git_operations.append({"operation": "add", "status": "success" if add_result.returncode == 0 else "failed"})

                # 3. 提交更改
                commit_message = f"AI自动实现: {task.get('title', '未知任务')} - Session: {self.session_id}"
                commit_result = subprocess.run(
                    ["git", "commit", "-m", commit_message],
                    capture_output=True, text=True, encoding='utf-8'
                )
                git_operations.append({"operation": "commit", "status": "success" if commit_result.returncode == 0 else "failed", "message": commit_message})

                # 4. 推送到远程
                try:
                    # 获取当前分支
                    branch_result = subprocess.run(
                        ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                        capture_output=True, text=True, encoding='utf-8'
                    )
                    current_branch = branch_result.stdout.strip()

                    # 推送
                    push_result = subprocess.run(
                        ["git", "push", "origin", current_branch],
                        capture_output=True, text=True, encoding='utf-8'
                    )
                    git_operations.append({
                        "operation": "push",
                        "status": "success" if push_result.returncode == 0 else "failed",
                        "branch": current_branch
                    })

                except Exception as e:
                    git_operations.append({"operation": "push", "status": "error", "error": str(e)})
            else:
                git_operations.append({"operation": "status_check", "status": "no_changes"})

            return {
                "status": "completed",
                "operations": git_operations,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
        finally:
            os.chdir(original_cwd)

    async def _generate_final_report(
        self, task: Dict, analysis: Dict, implementation: Dict,
        test_result: Dict, git_result: Dict
    ) -> str:
        """生成最终报告"""

        task_title = task.get("title", "未知任务")
        task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")

        # 统计实现结果
        impl_results = implementation.get("results", [])
        success_count = len([r for r in impl_results if r.get("status") == "success"])
        total_count = len(impl_results)

        # 统计测试结果
        test_results = test_result.get("test_results", [])
        passed_tests = len([t for t in test_results if t.get("status") == "passed"])
        total_tests = len(test_results)

        # 统计Git操作
        git_ops = git_result.get("operations", [])
        successful_git_ops = len([op for op in git_ops if op.get("status") == "success"])

        report = f"""
## 🎯 超级AI Agent任务执行报告

### 📋 任务信息
- **任务标题**: {task_title}
- **任务类型**: {task_type}
- **执行会话**: {self.session_id}
- **执行时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### 🧠 智能分析结果
{analysis.get('analysis_text', '分析完成')[:500]}...

### 💻 代码实现结果
- **实现步骤**: {total_count}个
- **成功完成**: {success_count}个
- **成功率**: {(success_count/total_count*100) if total_count > 0 else 0:.1f}%

### 🧪 自动测试结果
- **测试项目**: {total_tests}个
- **通过测试**: {passed_tests}个
- **测试通过率**: {(passed_tests/total_tests*100) if total_tests > 0 else 0:.1f}%

### 🔄 Git操作结果
- **Git操作**: {len(git_ops)}个
- **成功操作**: {successful_git_ops}个
- **代码已提交**: {'✅' if any(op.get('operation') == 'commit' and op.get('status') == 'success' for op in git_ops) else '❌'}
- **代码已推送**: {'✅' if any(op.get('operation') == 'push' and op.get('status') == 'success' for op in git_ops) else '❌'}

### 🎉 执行总结
超级AI Agent已完全自主地完成了任务执行，包括：
- ✅ 深度需求分析和架构设计
- ✅ 详细实施计划制定
- ✅ 自主代码实现（无需人类确认）
- ✅ 自动测试和验证
- ✅ 自动文档生成
- ✅ Git提交和推送

### 📝 下一步建议
1. 检查生成的代码质量和功能完整性
2. 运行完整的集成测试
3. 进行代码审查（如需要）
4. 部署到测试环境验证

---
*本报告由超级AI Agent自动生成 - 完全自主的AI开发者*
"""

        return report
