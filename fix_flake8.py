#!/usr/bin/env python3
"""
修复 flake8 检查发现的问题
"""

import os
import re
import sys
from pathlib import Path


def fix_blank_line_whitespace(file_path):
    """修复空行中的空格 (W293)"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 替换空行中的空格
    fixed_content = re.sub(r'^\s+$', '', content, flags=re.MULTILINE)

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)


def fix_trailing_whitespace(file_path):
    """修复行尾空格 (W291)"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 替换行尾空格
    fixed_content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)


def fix_long_lines(file_path):
    """修复过长的行 (E501)"""
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    fixed_lines = []
    for line in lines:
        if len(line.rstrip('\n')) > 100 and not line.strip().startswith('#'):
            # 尝试在逗号、空格等处断行
            # 这只是一个简单的实现，可能需要更复杂的逻辑
            if ',' in line:
                parts = line.split(',')
                new_line = parts[0] + ','
                current_len = len(new_line)

                for part in parts[1:]:
                    if current_len + len(part) + 1 > 100:
                        new_line += '\n    ' + part.lstrip()
                        current_len = 4 + len(part.lstrip())
                    else:
                        new_line += ' ' + part.lstrip()
                        current_len += 1 + len(part.lstrip())

                fixed_lines.append(new_line)
            else:
                # 如果没有简单的断行点，保留原行
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)

    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)


def fix_unused_imports(file_path):
    """修复未使用的导入 (F401)"""
    # 这需要更复杂的分析，这里只是一个简单的实现
    # 在实际项目中，建议使用 autoflake 或 pyflakes 等工具
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 查找未使用的导入
    unused_imports = re.findall(r'# F401 (.*) imported but unused', content)

    for unused in unused_imports:
        # 移除未使用的导入
        pattern = rf'import {unused}\n'
        content = re.sub(pattern, '', content)

        pattern = rf'from .* import .*{unused}.*\n'
        content = re.sub(pattern, '', content)

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)


def fix_bare_except(file_path):
    """修复裸异常 (E722)"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 替换裸异常
    fixed_content = re.sub(r'except:', 'except Exception:', content)

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)


def fix_module_level_import(file_path):
    """修复模块级导入不在文件顶部 (E402)"""
    # 这需要更复杂的分析，这里只是一个简单的实现
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 收集所有导入语句
    imports = []
    non_imports = []

    for line in lines:
        if line.strip().startswith('import ') or line.strip().startswith('from '):
            imports.append(line)
        else:
            non_imports.append(line)

    # 重新组织文件，将所有导入放在顶部
    with open(file_path, 'w', encoding='utf-8') as f:
        # 写入文件头部注释和文档字符串
        header = []
        for line in non_imports:
            if line.strip().startswith('#') or line.strip().startswith('"""'):
                header.append(line)
            else:
                break

        f.writelines(header)
        f.writelines(imports)
        f.writelines([line for line in non_imports if line not in header])


def fix_file(file_path):
    """修复文件中的所有问题"""
    print(f"修复文件: {file_path}")

    fix_blank_line_whitespace(file_path)
    fix_trailing_whitespace(file_path)
    fix_long_lines(file_path)
    fix_unused_imports(file_path)
    fix_bare_except(file_path)
    fix_module_level_import(file_path)


def main():
    """主函数"""
    # 获取项目根目录
    root_dir = Path(__file__).parent

    # 要修复的目录
    dirs = ['aider', 'bot_agent']

    # 遍历所有 Python 文件
    for dir_name in dirs:
        dir_path = root_dir / dir_name
        for root, _, files in os.walk(dir_path):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    fix_file(file_path)

    print("完成修复！")


if __name__ == "__main__":
    main()
