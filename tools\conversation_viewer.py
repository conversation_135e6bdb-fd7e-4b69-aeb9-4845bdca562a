#!/usr/bin/env python3
"""
对话记录查看工具 - 查看和分析AI对话记录
"""

import os
import sys
import json
import argparse
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot_agent.utils.conversation_logger import ConversationLogger


def list_sessions(logger: ConversationLogger, args):
    """列出对话会话"""
    print("📋 对话会话列表\n")

    sessions = logger.search_sessions(
        task_type=args.task_type,
        status=args.status,
        date_from=args.date_from,
        date_to=args.date_to
    )

    if not sessions:
        print("未找到匹配的会话")
        return

    print(f"找到 {len(sessions)} 个会话:\n")

    for i, session in enumerate(sessions, 1):
        status_emoji = {
            'success': '✅',
            'failed': '❌',
            'in_progress': '🔄',
            'started': '🆕'
        }.get(session['status'], '❓')

        print(f"{i:2d}. {status_emoji} {session['session_id']}")
        print(f"    标题: {session['task_title']}")
        print(f"    类型: {session['task_type']}")
        print(f"    状态: {session['status']}")
        print(f"    轮次: {session['rounds_count']}")
        print(f"    时间: {session['started_at']}")
        print()


def show_session_detail(logger: ConversationLogger, session_id: str):
    """显示会话详情"""
    print(f"🔍 会话详情: {session_id}\n")

    # 尝试从文件加载会话
    session = logger._load_session_from_file(session_id)
    if not session:
        print(f"❌ 未找到会话: {session_id}")
        return

    # 显示会话基本信息
    print("📋 基本信息:")
    print(f"  会话ID: {session.session_id}")
    print(f"  任务ID: {session.task_id}")
    print(f"  标题: {session.task_title}")
    print(f"  类型: {session.task_type}")
    print(f"  项目路径: {session.project_path}")
    print(f"  开始时间: {session.started_at}")
    print(f"  结束时间: {session.ended_at or '进行中'}")
    print(f"  总时长: {f'{session.total_duration:.2f}秒' if session.total_duration else '计算中'}")
    print(f"  状态: {session.status if isinstance(session.status, str) else session.status.value}")
    print()

    # 显示对话轮次
    print("🔄 对话轮次:")
    for round_info in session.rounds:
        status_value = round_info.status if isinstance(round_info.status, str) else round_info.status.value
        status_emoji = {
            'success': '✅',
            'failed': '❌',
            'retried': '🔄',
            'fallback': '⚠️'
        }.get(status_value, '❓')

        print(f"\n  {status_emoji} 第{round_info.round_number}轮: {round_info.round_name}")
        print(f"    时间: {round_info.timestamp}")
        print(f"    模型: {round_info.model_name}")
        print(f"    时长: {round_info.duration:.2f}秒")
        print(f"    重试: {round_info.retry_count}次")

        if round_info.error_message:
            print(f"    错误: {round_info.error_message}")

        # 显示提示和响应的摘要
        prompt_preview = round_info.prompt[:100] + "..." if len(round_info.prompt) > 100 else round_info.prompt
        response_preview = round_info.response[:100] + "..." if len(round_info.response) > 100 else round_info.response

        print(f"    提示: {prompt_preview}")
        print(f"    响应: {response_preview}")

    # 显示最终结果
    if session.final_result:
        print(f"\n🎯 最终结果:")
        result_preview = session.final_result[:200] + "..." if len(session.final_result) > 200 else session.final_result
        print(f"  {result_preview}")

    # 显示错误摘要
    if session.error_summary:
        print(f"\n❌ 错误摘要:")
        print(f"  {session.error_summary}")


def show_session_full(logger: ConversationLogger, session_id: str):
    """显示会话完整内容"""
    print(f"📄 会话完整内容: {session_id}\n")

    # 查找Markdown文件
    log_dir = Path(logger.log_dir)
    md_file = None

    for date_dir in log_dir.iterdir():
        if date_dir.is_dir():
            potential_file = date_dir / f"{session_id}.md"
            if potential_file.exists():
                md_file = potential_file
                break

    if md_file:
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(content)
    else:
        print(f"❌ 未找到会话的Markdown文件: {session_id}")


def analyze_sessions(logger: ConversationLogger, args):
    """分析会话统计"""
    print("📊 对话会话分析\n")

    sessions = logger.search_sessions(
        date_from=args.date_from,
        date_to=args.date_to
    )

    if not sessions:
        print("未找到会话数据")
        return

    # 基本统计
    total_sessions = len(sessions)
    success_sessions = len([s for s in sessions if s['status'] == 'success'])
    failed_sessions = len([s for s in sessions if s['status'] == 'failed'])

    print(f"📋 基本统计:")
    print(f"  总会话数: {total_sessions}")
    print(f"  成功会话: {success_sessions} ({success_sessions/total_sessions*100:.1f}%)")
    print(f"  失败会话: {failed_sessions} ({failed_sessions/total_sessions*100:.1f}%)")
    print()

    # 按任务类型统计
    task_types = {}
    for session in sessions:
        task_type = session['task_type']
        if task_type not in task_types:
            task_types[task_type] = {'total': 0, 'success': 0, 'failed': 0}
        task_types[task_type]['total'] += 1
        if session['status'] == 'success':
            task_types[task_type]['success'] += 1
        elif session['status'] == 'failed':
            task_types[task_type]['failed'] += 1

    print(f"📊 按任务类型统计:")
    for task_type, stats in task_types.items():
        success_rate = stats['success'] / stats['total'] * 100 if stats['total'] > 0 else 0
        print(f"  {task_type}: {stats['total']} 个 (成功率 {success_rate:.1f}%)")
    print()

    # 按轮次统计
    total_rounds = sum(session['rounds_count'] for session in sessions)
    avg_rounds = total_rounds / total_sessions if total_sessions > 0 else 0

    print(f"🔄 对话轮次统计:")
    print(f"  总轮次: {total_rounds}")
    print(f"  平均轮次: {avg_rounds:.1f}")
    print()

    # 最近的失败会话
    failed_sessions_list = [s for s in sessions if s['status'] == 'failed']
    if failed_sessions_list:
        print(f"❌ 最近的失败会话:")
        for session in failed_sessions_list[:5]:
            print(f"  - {session['session_id']}: {session['task_title']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="对话记录查看工具")
    parser.add_argument("--log-dir", help="日志目录路径")

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 列出会话
    list_parser = subparsers.add_parser("list", help="列出对话会话")
    list_parser.add_argument("--task-type", help="按任务类型过滤")
    list_parser.add_argument("--status", help="按状态过滤")
    list_parser.add_argument("--date-from", help="开始日期 (YYYY-MM-DD)")
    list_parser.add_argument("--date-to", help="结束日期 (YYYY-MM-DD)")

    # 显示会话详情
    detail_parser = subparsers.add_parser("show", help="显示会话详情")
    detail_parser.add_argument("session_id", help="会话ID")

    # 显示完整内容
    full_parser = subparsers.add_parser("full", help="显示会话完整内容")
    full_parser.add_argument("session_id", help="会话ID")

    # 分析统计
    analyze_parser = subparsers.add_parser("analyze", help="分析会话统计")
    analyze_parser.add_argument("--date-from", help="开始日期 (YYYY-MM-DD)")
    analyze_parser.add_argument("--date-to", help="结束日期 (YYYY-MM-DD)")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 初始化对话记录器
    logger = ConversationLogger(args.log_dir)

    try:
        if args.command == "list":
            list_sessions(logger, args)
        elif args.command == "show":
            show_session_detail(logger, args.session_id)
        elif args.command == "full":
            show_session_full(logger, args.session_id)
        elif args.command == "analyze":
            analyze_sessions(logger, args)
        else:
            print(f"未知命令: {args.command}")

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
