name: Ubuntu Python Tests

on:
  push:
    paths-ignore:
      - 'aider/website/**'
      - 'README.md'
      - 'HISTORY.md'
      - '.github/workflows/*'
      - '!.github/workflows/ubuntu-tests.yml'
    branches:
      - main
  pull_request:
    paths-ignore:
      - 'aider/website/**'
      - 'README.md'
      - 'HISTORY.md'
      - '.github/workflows/*'
      - '!.github/workflows/ubuntu-tests.yml'
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12"]

    steps:
    - name: Check out repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libportaudio2

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest
        pip install .

    - name: Run tests
      env:
        AIDER_ANALYTICS: false
      run: |
        pytest
