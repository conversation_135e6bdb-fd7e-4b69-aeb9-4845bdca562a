"""
代码分析工具 - AI自动编码的核心工具
"""

import os
import ast
import re
import json
import subprocess
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class CodeAnalysisTools(BaseTool):
    """
    代码分析工具
    
    功能：
    1. AST语法分析
    2. 代码质量检查
    3. 复杂度分析
    4. 依赖关系分析
    5. 代码结构分析
    6. 重复代码检测
    """
    
    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'parse_ast',
            'analyze_code_quality',
            'calculate_complexity',
            'find_dependencies',
            'detect_duplicates',
            'analyze_structure',
            'suggest_improvements',
            'extract_functions'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "代码分析工具 - AST解析、质量检查、复杂度分析等"
    
    async def parse_ast(self, file_path: str) -> ToolResult:
        """
        解析Python文件的AST
        
        Args:
            file_path: Python文件路径
            
        Returns:
            ToolResult: AST分析结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 解析AST
            tree = ast.parse(source_code, filename=file_path)
            
            # 提取结构信息
            analyzer = ASTAnalyzer()
            structure = analyzer.analyze(tree)
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'structure': structure,
                    'line_count': len(source_code.splitlines()),
                    'char_count': len(source_code)
                },
                message=f"成功解析 {file_path} 的AST结构"
            )
            
        except SyntaxError as e:
            return ToolResult(
                success=False,
                error=f"语法错误: {e}",
                data={'line': e.lineno, 'offset': e.offset}
            )
        except Exception as e:
            self.log_error(e, f"解析AST: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def analyze_code_quality(self, project_path: str) -> ToolResult:
        """
        分析代码质量
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 代码质量分析结果
        """
        try:
            quality_results = {}
            
            # 使用pylint检查
            pylint_result = await self.terminal.execute_command(
                f'python -m pylint --output-format=json {project_path}',
                cwd=project_path
            )
            
            if pylint_result.success:
                try:
                    pylint_data = json.loads(pylint_result.data['stdout'])
                    quality_results['pylint'] = self._process_pylint_results(pylint_data)
                except json.JSONDecodeError:
                    quality_results['pylint'] = {'error': 'Failed to parse pylint output'}
            
            # 使用flake8检查
            flake8_result = await self.terminal.execute_command(
                f'python -m flake8 --format=json {project_path}',
                cwd=project_path
            )
            
            if flake8_result.success:
                quality_results['flake8'] = self._process_flake8_results(flake8_result.data['stdout'])
            
            # 计算总体质量分数
            quality_score = self._calculate_quality_score(quality_results)
            
            return ToolResult(
                success=True,
                data={
                    'quality_results': quality_results,
                    'quality_score': quality_score,
                    'recommendations': self._generate_quality_recommendations(quality_results)
                },
                message=f"代码质量分析完成，总分: {quality_score}/100"
            )
            
        except Exception as e:
            self.log_error(e, "代码质量分析")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def calculate_complexity(self, file_path: str) -> ToolResult:
        """
        计算代码复杂度
        
        Args:
            file_path: 文件路径
            
        Returns:
            ToolResult: 复杂度分析结果
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            tree = ast.parse(source_code, filename=file_path)
            
            complexity_analyzer = ComplexityAnalyzer()
            complexity_data = complexity_analyzer.analyze(tree)
            
            return ToolResult(
                success=True,
                data=complexity_data,
                message=f"复杂度分析完成: 平均复杂度 {complexity_data['average_complexity']:.2f}"
            )
            
        except Exception as e:
            self.log_error(e, f"复杂度分析: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def find_dependencies(self, project_path: str) -> ToolResult:
        """
        分析项目依赖关系
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 依赖分析结果
        """
        try:
            dependency_analyzer = DependencyAnalyzer()
            dependencies = dependency_analyzer.analyze_project(project_path)
            
            return ToolResult(
                success=True,
                data=dependencies,
                message=f"发现 {len(dependencies['internal_imports'])} 个内部依赖，{len(dependencies['external_imports'])} 个外部依赖"
            )
            
        except Exception as e:
            self.log_error(e, "依赖分析")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def detect_duplicates(self, project_path: str) -> ToolResult:
        """
        检测重复代码
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 重复代码检测结果
        """
        try:
            duplicate_detector = DuplicateDetector()
            duplicates = duplicate_detector.find_duplicates(project_path)
            
            return ToolResult(
                success=True,
                data=duplicates,
                message=f"发现 {len(duplicates['duplicate_blocks'])} 个重复代码块"
            )
            
        except Exception as e:
            self.log_error(e, "重复代码检测")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def suggest_improvements(self, file_path: str) -> ToolResult:
        """
        生成代码改进建议
        
        Args:
            file_path: 文件路径
            
        Returns:
            ToolResult: 改进建议
        """
        try:
            # 综合分析结果生成建议
            ast_result = await self.parse_ast(file_path)
            complexity_result = await self.calculate_complexity(file_path)
            
            suggestions = []
            
            if complexity_result.success:
                complexity_data = complexity_result.data
                for func in complexity_data['functions']:
                    if func['complexity'] > 10:
                        suggestions.append({
                            'type': 'complexity',
                            'message': f"函数 {func['name']} 复杂度过高 ({func['complexity']})，建议重构",
                            'line': func['line'],
                            'severity': 'high'
                        })
            
            if ast_result.success:
                structure = ast_result.data['structure']
                
                # 检查函数长度
                for func in structure.get('functions', []):
                    if func['line_count'] > 50:
                        suggestions.append({
                            'type': 'length',
                            'message': f"函数 {func['name']} 过长 ({func['line_count']} 行)，建议拆分",
                            'line': func['line'],
                            'severity': 'medium'
                        })
                
                # 检查类设计
                for cls in structure.get('classes', []):
                    if len(cls['methods']) > 20:
                        suggestions.append({
                            'type': 'design',
                            'message': f"类 {cls['name']} 方法过多 ({len(cls['methods'])})，考虑拆分职责",
                            'line': cls['line'],
                            'severity': 'medium'
                        })
            
            return ToolResult(
                success=True,
                data={
                    'suggestions': suggestions,
                    'total_count': len(suggestions),
                    'severity_counts': self._count_by_severity(suggestions)
                },
                message=f"生成了 {len(suggestions)} 条改进建议"
            )
            
        except Exception as e:
            self.log_error(e, "生成改进建议")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _process_pylint_results(self, pylint_data: List[Dict]) -> Dict:
        """处理pylint结果"""
        issues_by_type = {}
        for issue in pylint_data:
            issue_type = issue.get('type', 'unknown')
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            issues_by_type[issue_type].append(issue)
        
        return {
            'total_issues': len(pylint_data),
            'issues_by_type': issues_by_type,
            'score': 10 - min(len(pylint_data) * 0.1, 8)  # 简单评分
        }
    
    def _process_flake8_results(self, flake8_output: str) -> Dict:
        """处理flake8结果"""
        lines = flake8_output.strip().split('\n') if flake8_output.strip() else []
        issues = []
        
        for line in lines:
            if ':' in line:
                parts = line.split(':')
                if len(parts) >= 4:
                    issues.append({
                        'file': parts[0],
                        'line': parts[1],
                        'column': parts[2],
                        'message': ':'.join(parts[3:]).strip()
                    })
        
        return {
            'total_issues': len(issues),
            'issues': issues
        }
    
    def _calculate_quality_score(self, quality_results: Dict) -> float:
        """计算总体质量分数"""
        score = 100.0
        
        # pylint扣分
        if 'pylint' in quality_results and 'score' in quality_results['pylint']:
            pylint_score = quality_results['pylint']['score']
            score = min(score, pylint_score * 10)
        
        # flake8扣分
        if 'flake8' in quality_results:
            flake8_issues = quality_results['flake8']['total_issues']
            score -= min(flake8_issues * 2, 30)
        
        return max(score, 0)
    
    def _generate_quality_recommendations(self, quality_results: Dict) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        if 'pylint' in quality_results:
            pylint_data = quality_results['pylint']
            if pylint_data.get('total_issues', 0) > 0:
                recommendations.append("运行 pylint 修复代码风格问题")
        
        if 'flake8' in quality_results:
            flake8_data = quality_results['flake8']
            if flake8_data.get('total_issues', 0) > 0:
                recommendations.append("运行 flake8 修复代码格式问题")
        
        return recommendations
    
    def _count_by_severity(self, suggestions: List[Dict]) -> Dict:
        """按严重程度统计建议"""
        counts = {'high': 0, 'medium': 0, 'low': 0}
        for suggestion in suggestions:
            severity = suggestion.get('severity', 'low')
            counts[severity] = counts.get(severity, 0) + 1
        return counts
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            file_path = args[0]
            if file_path.endswith('.py'):
                return await self.parse_ast(file_path)
            else:
                return await self.find_dependencies(file_path)
        else:
            return ToolResult(
                success=False,
                error="缺少文件路径参数"
            )


class ASTAnalyzer:
    """AST分析器"""
    
    def analyze(self, tree: ast.AST) -> Dict:
        """分析AST结构"""
        structure = {
            'classes': [],
            'functions': [],
            'imports': [],
            'variables': []
        }
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                structure['classes'].append(self._analyze_class(node))
            elif isinstance(node, ast.FunctionDef):
                structure['functions'].append(self._analyze_function(node))
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                structure['imports'].append(self._analyze_import(node))
        
        return structure
    
    def _analyze_class(self, node: ast.ClassDef) -> Dict:
        """分析类定义"""
        methods = []
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                methods.append(item.name)
        
        return {
            'name': node.name,
            'line': node.lineno,
            'methods': methods,
            'bases': [base.id if isinstance(base, ast.Name) else str(base) for base in node.bases]
        }
    
    def _analyze_function(self, node: ast.FunctionDef) -> Dict:
        """分析函数定义"""
        return {
            'name': node.name,
            'line': node.lineno,
            'args': [arg.arg for arg in node.args.args],
            'line_count': getattr(node, 'end_lineno', node.lineno) - node.lineno + 1
        }
    
    def _analyze_import(self, node) -> Dict:
        """分析导入语句"""
        if isinstance(node, ast.Import):
            return {
                'type': 'import',
                'modules': [alias.name for alias in node.names],
                'line': node.lineno
            }
        elif isinstance(node, ast.ImportFrom):
            return {
                'type': 'from_import',
                'module': node.module,
                'names': [alias.name for alias in node.names],
                'line': node.lineno
            }


class ComplexityAnalyzer:
    """复杂度分析器"""
    
    def analyze(self, tree: ast.AST) -> Dict:
        """分析代码复杂度"""
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_cyclomatic_complexity(node)
                functions.append({
                    'name': node.name,
                    'line': node.lineno,
                    'complexity': complexity
                })
        
        avg_complexity = sum(f['complexity'] for f in functions) / len(functions) if functions else 0
        
        return {
            'functions': functions,
            'average_complexity': avg_complexity,
            'max_complexity': max((f['complexity'] for f in functions), default=0),
            'total_functions': len(functions)
        }
    
    def _calculate_cyclomatic_complexity(self, node: ast.FunctionDef) -> int:
        """计算圈复杂度"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity


class DependencyAnalyzer:
    """依赖分析器"""
    
    def analyze_project(self, project_path: str) -> Dict:
        """分析项目依赖"""
        internal_imports = set()
        external_imports = set()
        
        for py_file in Path(project_path).rglob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    tree = ast.parse(f.read())
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            if self._is_internal_module(alias.name, project_path):
                                internal_imports.add(alias.name)
                            else:
                                external_imports.add(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            if self._is_internal_module(node.module, project_path):
                                internal_imports.add(node.module)
                            else:
                                external_imports.add(node.module)
            except:
                continue
        
        return {
            'internal_imports': list(internal_imports),
            'external_imports': list(external_imports)
        }
    
    def _is_internal_module(self, module_name: str, project_path: str) -> bool:
        """判断是否为内部模块"""
        # 简单判断：如果模块路径存在于项目中，则为内部模块
        module_path = os.path.join(project_path, module_name.replace('.', os.sep))
        return os.path.exists(module_path + '.py') or os.path.exists(os.path.join(module_path, '__init__.py'))


class DuplicateDetector:
    """重复代码检测器"""
    
    def find_duplicates(self, project_path: str) -> Dict:
        """查找重复代码"""
        # 简化实现：基于行的重复检测
        code_blocks = {}
        duplicates = []
        
        for py_file in Path(project_path).rglob('*.py'):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 检查连续的代码块
                for i in range(len(lines) - 5):  # 至少5行
                    block = ''.join(lines[i:i+5]).strip()
                    if block and not block.startswith('#'):
                        if block in code_blocks:
                            duplicates.append({
                                'block': block[:100] + '...',
                                'files': [code_blocks[block], str(py_file)],
                                'lines': [code_blocks[block + '_line'], i + 1]
                            })
                        else:
                            code_blocks[block] = str(py_file)
                            code_blocks[block + '_line'] = i + 1
            except:
                continue
        
        return {
            'duplicate_blocks': duplicates,
            'total_duplicates': len(duplicates)
        }
