"""
数据库工具 - 支持多种数据库的统一操作接口
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union
from abc import ABC, abstractmethod
from urllib.parse import urlparse

from .base_tool import BaseTool, ToolResult

logger = logging.getLogger(__name__)


class DatabaseAdapter(ABC):
    """数据库适配器基类"""

    @abstractmethod
    async def connect(self, connection_string: str) -> bool:
        """连接数据库"""
        pass

    @abstractmethod
    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Dict:
        """执行查询"""
        pass

    @abstractmethod
    async def execute_command(self, command: str, params: Optional[Dict] = None) -> Dict:
        """执行命令（INSERT, UPDATE, DELETE等）"""
        pass

    @abstractmethod
    async def get_schema_info(self) -> Dict:
        """获取数据库架构信息"""
        pass

    @abstractmethod
    async def close(self):
        """关闭连接"""
        pass


class SQLiteAdapter(DatabaseAdapter):
    """SQLite数据库适配器"""

    def __init__(self):
        self.connection = None

    async def connect(self, connection_string: str) -> bool:
        try:
            import sqlite3
            # 从连接字符串中提取数据库文件路径
            if connection_string.startswith('sqlite://'):
                db_path = connection_string.replace('sqlite://', '')
            else:
                db_path = connection_string

            self.connection = sqlite3.connect(db_path)
            self.connection.row_factory = sqlite3.Row  # 返回字典格式
            return True
        except Exception as e:
            logger.error(f"SQLite连接失败: {e}")
            return False

    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Dict:
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            rows = cursor.fetchall()
            columns = [description[0] for description in cursor.description] if cursor.description else []

            return {
                "success": True,
                "rows": [dict(row) for row in rows],
                "columns": columns,
                "row_count": len(rows)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "rows": [],
                "columns": []
            }

    async def execute_command(self, command: str, params: Optional[Dict] = None) -> Dict:
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(command, params)
            else:
                cursor.execute(command)

            self.connection.commit()

            return {
                "success": True,
                "affected_rows": cursor.rowcount,
                "last_insert_id": cursor.lastrowid
            }
        except Exception as e:
            self.connection.rollback()
            return {
                "success": False,
                "error": str(e),
                "affected_rows": 0
            }

    async def get_schema_info(self) -> Dict:
        try:
            # 获取所有表
            tables_query = "SELECT name FROM sqlite_master WHERE type='table'"
            tables_result = await self.execute_query(tables_query)

            schema_info = {"tables": {}}

            for table_row in tables_result["rows"]:
                table_name = table_row["name"]

                # 获取表结构
                columns_query = f"PRAGMA table_info({table_name})"
                columns_result = await self.execute_query(columns_query)

                schema_info["tables"][table_name] = {
                    "columns": columns_result["rows"]
                }

            return schema_info
        except Exception as e:
            return {"error": str(e), "tables": {}}

    async def close(self):
        if self.connection:
            self.connection.close()


class PostgreSQLAdapter(DatabaseAdapter):
    """PostgreSQL数据库适配器"""

    def __init__(self):
        self.connection = None

    async def connect(self, connection_string: str) -> bool:
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor

            self.connection = psycopg2.connect(connection_string)
            return True
        except ImportError:
            logger.error("psycopg2未安装，无法连接PostgreSQL")
            return False
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {e}")
            return False

    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Dict:
        try:
            import psycopg2.extras

            cursor = self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query, params)

            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description] if cursor.description else []

            return {
                "success": True,
                "rows": [dict(row) for row in rows],
                "columns": columns,
                "row_count": len(rows)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "rows": [],
                "columns": []
            }

    async def execute_command(self, command: str, params: Optional[Dict] = None) -> Dict:
        try:
            cursor = self.connection.cursor()
            cursor.execute(command, params)
            self.connection.commit()

            return {
                "success": True,
                "affected_rows": cursor.rowcount
            }
        except Exception as e:
            self.connection.rollback()
            return {
                "success": False,
                "error": str(e),
                "affected_rows": 0
            }

    async def get_schema_info(self) -> Dict:
        try:
            # 获取所有表
            tables_query = """
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
            """
            tables_result = await self.execute_query(tables_query)

            schema_info = {"tables": {}}

            for table_row in tables_result["rows"]:
                table_name = table_row["table_name"]

                # 获取表结构
                columns_query = """
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = %s
                """
                columns_result = await self.execute_query(columns_query, (table_name,))

                schema_info["tables"][table_name] = {
                    "columns": columns_result["rows"]
                }

            return schema_info
        except Exception as e:
            return {"error": str(e), "tables": {}}

    async def close(self):
        if self.connection:
            self.connection.close()


class MySQLAdapter(DatabaseAdapter):
    """MySQL数据库适配器"""

    def __init__(self):
        self.connection = None

    async def connect(self, connection_string: str) -> bool:
        try:
            import pymysql

            # 解析连接字符串
            parsed = urlparse(connection_string)

            self.connection = pymysql.connect(
                host=parsed.hostname,
                port=parsed.port or 3306,
                user=parsed.username,
                password=parsed.password,
                database=parsed.path.lstrip('/'),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return True
        except ImportError:
            logger.error("pymysql未安装，无法连接MySQL")
            return False
        except Exception as e:
            logger.error(f"MySQL连接失败: {e}")
            return False

    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Dict:
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)

            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description] if cursor.description else []

            return {
                "success": True,
                "rows": rows,
                "columns": columns,
                "row_count": len(rows)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "rows": [],
                "columns": []
            }

    async def execute_command(self, command: str, params: Optional[Dict] = None) -> Dict:
        try:
            cursor = self.connection.cursor()
            cursor.execute(command, params)
            self.connection.commit()

            return {
                "success": True,
                "affected_rows": cursor.rowcount,
                "last_insert_id": cursor.lastrowid
            }
        except Exception as e:
            self.connection.rollback()
            return {
                "success": False,
                "error": str(e),
                "affected_rows": 0
            }

    async def get_schema_info(self) -> Dict:
        try:
            # 获取当前数据库名
            db_query = "SELECT DATABASE() as db_name"
            db_result = await self.execute_query(db_query)
            db_name = db_result["rows"][0]["db_name"]

            # 获取所有表
            tables_query = "SHOW TABLES"
            tables_result = await self.execute_query(tables_query)

            schema_info = {"tables": {}}

            for table_row in tables_result["rows"]:
                table_name = list(table_row.values())[0]

                # 获取表结构
                columns_query = f"DESCRIBE {table_name}"
                columns_result = await self.execute_query(columns_query)

                schema_info["tables"][table_name] = {
                    "columns": columns_result["rows"]
                }

            return schema_info
        except Exception as e:
            return {"error": str(e), "tables": {}}

    async def close(self):
        if self.connection:
            self.connection.close()


class MongoDBAdapter(DatabaseAdapter):
    """MongoDB数据库适配器"""

    def __init__(self):
        self.client = None
        self.database = None

    async def connect(self, connection_string: str) -> bool:
        try:
            import pymongo
            from urllib.parse import urlparse

            self.client = pymongo.MongoClient(connection_string)

            # 从连接字符串中提取数据库名
            parsed = urlparse(connection_string)
            if parsed.path and len(parsed.path) > 1:
                db_name = parsed.path.lstrip('/')
            else:
                db_name = 'test'  # 默认数据库

            self.database = self.client[db_name]

            # 测试连接
            self.client.admin.command('ping')
            return True

        except ImportError:
            logger.error("pymongo未安装，无法连接MongoDB")
            return False
        except Exception as e:
            logger.error(f"MongoDB连接失败: {e}")
            return False

    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Dict:
        """
        执行MongoDB查询
        query格式: "collection_name.operation"
        params: 查询参数
        """
        try:
            # 解析查询格式: collection.find, collection.aggregate等
            if '.' not in query:
                return {
                    "success": False,
                    "error": "查询格式错误，应为: collection_name.operation",
                    "rows": []
                }

            collection_name, operation = query.split('.', 1)
            collection = self.database[collection_name]

            # 根据操作类型执行不同的查询
            if operation.startswith('find'):
                filter_params = params or {}
                cursor = collection.find(filter_params)
                rows = list(cursor)

                # 转换ObjectId为字符串
                for row in rows:
                    if '_id' in row:
                        row['_id'] = str(row['_id'])

                return {
                    "success": True,
                    "rows": rows,
                    "row_count": len(rows)
                }

            elif operation.startswith('aggregate'):
                pipeline = params.get('pipeline', []) if params else []
                cursor = collection.aggregate(pipeline)
                rows = list(cursor)

                for row in rows:
                    if '_id' in row:
                        row['_id'] = str(row['_id'])

                return {
                    "success": True,
                    "rows": rows,
                    "row_count": len(rows)
                }

            elif operation.startswith('count'):
                filter_params = params or {}
                count = collection.count_documents(filter_params)

                return {
                    "success": True,
                    "rows": [{"count": count}],
                    "row_count": 1
                }

            else:
                return {
                    "success": False,
                    "error": f"不支持的操作: {operation}",
                    "rows": []
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "rows": []
            }

    async def execute_command(self, command: str, params: Optional[Dict] = None) -> Dict:
        """
        执行MongoDB命令 (insert, update, delete等)
        """
        try:
            if '.' not in command:
                return {
                    "success": False,
                    "error": "命令格式错误，应为: collection_name.operation"
                }

            collection_name, operation = command.split('.', 1)
            collection = self.database[collection_name]

            if operation.startswith('insert_one'):
                document = params.get('document', {}) if params else {}
                result = collection.insert_one(document)

                return {
                    "success": True,
                    "affected_rows": 1,
                    "inserted_id": str(result.inserted_id)
                }

            elif operation.startswith('insert_many'):
                documents = params.get('documents', []) if params else []
                result = collection.insert_many(documents)

                return {
                    "success": True,
                    "affected_rows": len(result.inserted_ids),
                    "inserted_ids": [str(id) for id in result.inserted_ids]
                }

            elif operation.startswith('update_one'):
                filter_params = params.get('filter', {}) if params else {}
                update_params = params.get('update', {}) if params else {}
                result = collection.update_one(filter_params, update_params)

                return {
                    "success": True,
                    "affected_rows": result.modified_count,
                    "matched_count": result.matched_count
                }

            elif operation.startswith('delete_one'):
                filter_params = params.get('filter', {}) if params else {}
                result = collection.delete_one(filter_params)

                return {
                    "success": True,
                    "affected_rows": result.deleted_count
                }

            else:
                return {
                    "success": False,
                    "error": f"不支持的命令: {operation}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "affected_rows": 0
            }

    async def get_schema_info(self) -> Dict:
        """获取MongoDB集合信息"""
        try:
            collections = self.database.list_collection_names()
            schema_info = {"collections": {}}

            for collection_name in collections:
                collection = self.database[collection_name]

                # 获取集合统计信息
                stats = self.database.command("collStats", collection_name)

                # 获取示例文档来推断结构
                sample_doc = collection.find_one()
                fields = list(sample_doc.keys()) if sample_doc else []

                schema_info["collections"][collection_name] = {
                    "document_count": stats.get("count", 0),
                    "size_bytes": stats.get("size", 0),
                    "sample_fields": fields
                }

            return schema_info

        except Exception as e:
            return {"error": str(e), "collections": {}}

    async def close(self):
        if self.client:
            self.client.close()


class RedisAdapter(DatabaseAdapter):
    """Redis数据库适配器"""

    def __init__(self):
        self.client = None

    async def connect(self, connection_string: str) -> bool:
        try:
            import redis
            from urllib.parse import urlparse

            # 解析Redis连接字符串
            if connection_string.startswith('redis://'):
                self.client = redis.from_url(connection_string)
            else:
                # 简单格式: host:port
                if ':' in connection_string:
                    host, port = connection_string.split(':')
                    port = int(port)
                else:
                    host = connection_string
                    port = 6379

                self.client = redis.Redis(host=host, port=port, decode_responses=True)

            # 测试连接
            self.client.ping()
            return True

        except ImportError:
            logger.error("redis未安装，无法连接Redis")
            return False
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return False

    async def execute_query(self, query: str, params: Optional[Dict] = None) -> Dict:
        """
        执行Redis查询
        query格式: "GET key" 或 "KEYS pattern" 等
        """
        try:
            # 解析Redis命令
            parts = query.strip().split()
            if not parts:
                return {
                    "success": False,
                    "error": "空查询",
                    "rows": []
                }

            command = parts[0].upper()
            args = parts[1:] if len(parts) > 1 else []

            # 执行不同的Redis命令
            if command == 'GET':
                if not args:
                    return {"success": False, "error": "GET命令需要key参数", "rows": []}

                value = self.client.get(args[0])
                return {
                    "success": True,
                    "rows": [{"key": args[0], "value": value}],
                    "row_count": 1
                }

            elif command == 'KEYS':
                pattern = args[0] if args else '*'
                keys = self.client.keys(pattern)
                rows = [{"key": key} for key in keys]

                return {
                    "success": True,
                    "rows": rows,
                    "row_count": len(rows)
                }

            elif command == 'HGETALL':
                if not args:
                    return {"success": False, "error": "HGETALL命令需要key参数", "rows": []}

                hash_data = self.client.hgetall(args[0])
                rows = [{"field": k, "value": v} for k, v in hash_data.items()]

                return {
                    "success": True,
                    "rows": rows,
                    "row_count": len(rows)
                }

            elif command == 'LRANGE':
                if len(args) < 3:
                    return {"success": False, "error": "LRANGE命令需要key、start、stop参数", "rows": []}

                key, start, stop = args[0], int(args[1]), int(args[2])
                list_data = self.client.lrange(key, start, stop)
                rows = [{"index": i, "value": v} for i, v in enumerate(list_data)]

                return {
                    "success": True,
                    "rows": rows,
                    "row_count": len(rows)
                }

            elif command == 'INFO':
                section = args[0] if args else None
                info = self.client.info(section)
                rows = [{"key": k, "value": str(v)} for k, v in info.items()]

                return {
                    "success": True,
                    "rows": rows,
                    "row_count": len(rows)
                }

            else:
                return {
                    "success": False,
                    "error": f"不支持的查询命令: {command}",
                    "rows": []
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "rows": []
            }

    async def execute_command(self, command: str, params: Optional[Dict] = None) -> Dict:
        """
        执行Redis命令 (SET, DEL, HSET等)
        """
        try:
            parts = command.strip().split()
            if not parts:
                return {
                    "success": False,
                    "error": "空命令"
                }

            cmd = parts[0].upper()
            args = parts[1:] if len(parts) > 1 else []

            if cmd == 'SET':
                if len(args) < 2:
                    return {"success": False, "error": "SET命令需要key和value参数"}

                key, value = args[0], args[1]
                # 支持额外参数
                extra_args = args[2:] if len(args) > 2 else []

                result = self.client.set(key, value, *extra_args)

                return {
                    "success": bool(result),
                    "affected_rows": 1 if result else 0
                }

            elif cmd == 'DEL':
                if not args:
                    return {"success": False, "error": "DEL命令需要key参数"}

                deleted_count = self.client.delete(*args)

                return {
                    "success": True,
                    "affected_rows": deleted_count
                }

            elif cmd == 'HSET':
                if len(args) < 3:
                    return {"success": False, "error": "HSET命令需要key、field、value参数"}

                key, field, value = args[0], args[1], args[2]
                result = self.client.hset(key, field, value)

                return {
                    "success": True,
                    "affected_rows": result
                }

            elif cmd == 'LPUSH':
                if len(args) < 2:
                    return {"success": False, "error": "LPUSH命令需要key和value参数"}

                key = args[0]
                values = args[1:]
                result = self.client.lpush(key, *values)

                return {
                    "success": True,
                    "affected_rows": result
                }

            elif cmd == 'EXPIRE':
                if len(args) < 2:
                    return {"success": False, "error": "EXPIRE命令需要key和seconds参数"}

                key, seconds = args[0], int(args[1])
                result = self.client.expire(key, seconds)

                return {
                    "success": bool(result),
                    "affected_rows": 1 if result else 0
                }

            else:
                return {
                    "success": False,
                    "error": f"不支持的命令: {cmd}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "affected_rows": 0
            }

    async def get_schema_info(self) -> Dict:
        """获取Redis信息"""
        try:
            # 获取Redis基本信息
            info = self.client.info()

            # 获取数据库信息
            db_info = {}
            for i in range(16):  # Redis默认有16个数据库
                try:
                    self.client.select(i)
                    db_size = self.client.dbsize()
                    if db_size > 0:
                        db_info[f"db{i}"] = {"keys": db_size}
                except:
                    break

            # 切回默认数据库
            self.client.select(0)

            # 获取一些示例键
            sample_keys = self.client.keys('*')[:10]  # 最多10个示例
            key_types = {}
            for key in sample_keys:
                key_type = self.client.type(key)
                key_types[key] = key_type

            return {
                "server_info": {
                    "redis_version": info.get("redis_version"),
                    "used_memory": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "uptime_in_seconds": info.get("uptime_in_seconds")
                },
                "databases": db_info,
                "sample_keys": key_types
            }

        except Exception as e:
            return {"error": str(e)}

    async def close(self):
        if self.client:
            self.client.close()


class DatabaseTools(BaseTool):
    """
    数据库工具

    功能：
    1. 多数据库连接支持
    2. SQL查询执行
    3. 数据库架构分析
    4. 数据迁移辅助
    5. 性能监控
    """

    def __init__(self):
        super().__init__()
        self.adapters = {
            'sqlite': SQLiteAdapter,
            'postgresql': PostgreSQLAdapter,
            'mysql': MySQLAdapter,
            'mongodb': MongoDBAdapter,
            'redis': RedisAdapter,
        }
        self.active_connections = {}

    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'connect_database',
            'execute_query',
            'execute_command',
            'get_schema_info',
            'analyze_performance',
            'backup_database',
            'migrate_data',
            'validate_data'
        ]

    def get_description(self) -> str:
        """获取工具描述"""
        return "数据库工具 - 支持SQLite、PostgreSQL、MySQL等多种数据库操作"

    async def connect_database(
        self,
        db_type: str,
        connection_string: str,
        connection_name: str = "default"
    ) -> ToolResult:
        """
        连接数据库

        Args:
            db_type: 数据库类型 (sqlite, postgresql, mysql)
            connection_string: 连接字符串
            connection_name: 连接名称

        Returns:
            ToolResult: 连接结果
        """
        try:
            if db_type not in self.adapters:
                return ToolResult(
                    success=False,
                    error=f"不支持的数据库类型: {db_type}",
                    message=f"支持的类型: {list(self.adapters.keys())}"
                )

            adapter_class = self.adapters[db_type]
            adapter = adapter_class()

            success = await adapter.connect(connection_string)

            if success:
                self.active_connections[connection_name] = {
                    'adapter': adapter,
                    'db_type': db_type,
                    'connection_string': connection_string
                }

                return ToolResult(
                    success=True,
                    data={
                        'connection_name': connection_name,
                        'db_type': db_type,
                        'status': 'connected'
                    },
                    message=f"成功连接到 {db_type} 数据库"
                )
            else:
                return ToolResult(
                    success=False,
                    error="数据库连接失败",
                    message="请检查连接字符串和数据库状态"
                )

        except Exception as e:
            self.log_error(e, f"连接数据库: {db_type}")
            return ToolResult(
                success=False,
                error=str(e),
                message="数据库连接异常"
            )

    async def execute_query(
        self,
        query: str,
        params: Optional[Dict] = None,
        connection_name: str = "default"
    ) -> ToolResult:
        """
        执行查询

        Args:
            query: SQL查询语句
            params: 查询参数
            connection_name: 连接名称

        Returns:
            ToolResult: 查询结果
        """
        try:
            if connection_name not in self.active_connections:
                return ToolResult(
                    success=False,
                    error=f"连接 '{connection_name}' 不存在",
                    message="请先建立数据库连接"
                )

            adapter = self.active_connections[connection_name]['adapter']
            result = await adapter.execute_query(query, params)

            return ToolResult(
                success=result['success'],
                data=result,
                error=result.get('error'),
                message=f"查询{'成功' if result['success'] else '失败'}"
            )

        except Exception as e:
            self.log_error(e, f"执行查询: {query}")
            return ToolResult(
                success=False,
                error=str(e),
                message="查询执行异常"
            )

    async def get_database_schema(self, connection_name: str = "default") -> ToolResult:
        """
        获取数据库架构信息

        Args:
            connection_name: 连接名称

        Returns:
            ToolResult: 架构信息
        """
        try:
            if connection_name not in self.active_connections:
                return ToolResult(
                    success=False,
                    error=f"连接 '{connection_name}' 不存在"
                )

            adapter = self.active_connections[connection_name]['adapter']
            schema_info = await adapter.get_schema_info()

            return ToolResult(
                success=True,
                data=schema_info,
                message=f"获取到 {len(schema_info.get('tables', {}))} 个表的架构信息"
            )

        except Exception as e:
            self.log_error(e, "获取数据库架构")
            return ToolResult(
                success=False,
                error=str(e),
                message="获取架构信息失败"
            )

    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        # 默认尝试连接SQLite数据库
        if args:
            db_path = args[0]
            return await self.connect_database('sqlite', db_path)
        else:
            return ToolResult(
                success=False,
                error="缺少数据库连接参数"
            )

    async def cleanup(self):
        """清理资源"""
        for connection_name, connection_info in self.active_connections.items():
            try:
                await connection_info['adapter'].close()
                logger.info(f"关闭数据库连接: {connection_name}")
            except Exception as e:
                logger.error(f"关闭连接失败: {e}")

        self.active_connections.clear()
