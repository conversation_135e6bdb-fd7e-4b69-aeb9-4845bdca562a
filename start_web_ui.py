#!/usr/bin/env python3
"""
对话分析Web界面启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'jinja2',
        'python-multipart'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("🔧 安装缺失的依赖包...")
        for package in missing_packages:
            print(f"   安装 {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
        print("✅ 依赖安装完成")

def create_directories():
    """创建必要的目录"""
    directories = [
        'web_ui/templates',
        'web_ui/static/css',
        'web_ui/static/js',
        'logs/conversations'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("📁 目录结构创建完成")

def main():
    """主函数"""
    print("🚀 启动对话分析Web界面")
    print("=" * 50)
    
    # 检查依赖
    check_dependencies()
    
    # 创建目录
    create_directories()
    
    # 启动Web服务
    print("\n🌐 启动Web服务...")
    print("📊 访问地址: http://localhost:8080")
    print("🔍 功能:")
    print("  - 📋 仪表板: 实时统计和概览")
    print("  - 📝 会话列表: 搜索和过滤对话记录")
    print("  - 👁️ 详情查看: 完整的对话内容")
    print("  - 📊 统计分析: 图表和趋势分析")
    print("  - 📤 数据导出: JSON和CSV格式")
    print("\n按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 启动FastAPI应用
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        subprocess.run([
            sys.executable, '-m', 'uvicorn',
            'web_ui.conversation_viewer_app:app',
            '--host', '0.0.0.0',
            '--port', '8080',
            '--reload'
        ])
    except KeyboardInterrupt:
        print("\n\n👋 Web服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保端口8080未被占用")
        print("2. 检查Python环境和依赖")
        print("3. 确保有足够的权限")

if __name__ == "__main__":
    main()
