version: '3.8'

services:
  # 统一服务 (包含 Bot 代理、Aider 和 Augment)
  aider-plus-unified:
    image: ${CI_REGISTRY_IMAGE}-unified:${IMAGE_TAG}
    container_name: aider-plus-unified
    restart: unless-stopped
    ports:
      - "8008:8000"  # Bot 代理服务端口 (映射到主机的 8008 端口)
      - "8088:8080"  # Aider 服务端口 (映射到主机的 8088 端口)
    environment:
      # Bot 代理服务环境变量
      - HOST=0.0.0.0
      - PORT=8000
      # 服务注册和健康检查配置
      - SERVICE_ADDRESS=***************  # 主机的外部 IP 地址
      - EXTERNAL_PORT=8008               # 映射到主机的外部端口
      # 使用外部 Consul 和 Redis
      - CONSUL_HOST=***************
      - CONSUL_PORT=8500
      - REDIS_HOST=***************
      - REDIS_PORT=6379
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - VALID_API_KEYS=${VALID_API_KEYS}
      - LOG_LEVEL=INFO
      # Aider 服务环境变量
      - AIDER_PORT=8080
      - AIDER_HOST=0.0.0.0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./workspace:/workspace
    networks:
      - aider-network  # 使用自定义网络，避免端口冲突
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  aider-network:
    driver: bridge
