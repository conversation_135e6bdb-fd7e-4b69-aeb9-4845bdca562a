"""
响应模型 - 定义 API 响应的数据模型
"""

from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误消息")
    detail: Optional[str] = Field(None, description="详细错误信息")
    code: Optional[str] = Field(None, description="错误代码")


class SuccessResponse(BaseModel):
    """成功响应模型"""
    message: str = Field(..., description="成功消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[Dict[str, Any]] = Field(..., description="项目列表")
    total: int = Field(..., description="总项目数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页项目数")
    pages: int = Field(..., description="总页数")


class TokenResponse(BaseModel):
    """令牌响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")
    expires_in: Optional[int] = Field(None, description="过期时间（秒）")


class ServiceResponse(BaseModel):
    """服务响应模型"""
    id: str = Field(..., description="服务 ID")
    name: str = Field(..., description="服务名称")
    host: str = Field(..., description="服务主机")
    port: int = Field(..., description="服务端口")
    tags: List[str] = Field(..., description="服务标签")
    status: str = Field(..., description="服务状态")


class ServiceHealthResponse(BaseModel):
    """服务健康状态响应模型"""
    healthy: List[ServiceResponse] = Field(..., description="健康的服务实例")
    unhealthy: List[ServiceResponse] = Field(..., description="不健康的服务实例")


class WebhookResponse(BaseModel):
    """Webhook 响应模型"""
    status: str = Field(..., description="处理状态")
    message: str = Field(..., description="处理消息")
    task_id: Optional[str] = Field(None, description="创建的任务 ID")
