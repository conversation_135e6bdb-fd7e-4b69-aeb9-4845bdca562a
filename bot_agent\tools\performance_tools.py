"""
性能监控工具 - AI自动编码的性能分析工具
"""

import os
import time
import psutil
import tracemalloc
import cProfile
import pstats
import io
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
import subprocess
import json

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class PerformanceTools(BaseTool):
    """
    性能监控工具
    
    功能：
    1. 代码执行性能分析
    2. 内存使用监控
    3. CPU使用率分析
    4. 性能瓶颈识别
    5. 优化建议生成
    6. 性能基准测试
    """
    
    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
        self.profiling_data = {}
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'profile_code',
            'monitor_memory',
            'analyze_performance',
            'benchmark_function',
            'detect_bottlenecks',
            'generate_optimization_suggestions',
            'measure_execution_time',
            'analyze_system_resources'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "性能监控工具 - 代码性能分析、内存监控、瓶颈识别"
    
    async def profile_code(self, file_path: str, function_name: Optional[str] = None) -> ToolResult:
        """
        分析代码性能
        
        Args:
            file_path: Python文件路径
            function_name: 特定函数名（可选）
            
        Returns:
            ToolResult: 性能分析结果
        """
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"文件不存在: {file_path}"
                )
            
            # 使用cProfile进行性能分析
            profiler = cProfile.Profile()
            
            # 准备执行环境
            exec_globals = {}
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # 开始性能分析
            profiler.enable()
            
            try:
                exec(code, exec_globals)
                
                # 如果指定了函数，尝试调用它
                if function_name and function_name in exec_globals:
                    func = exec_globals[function_name]
                    if callable(func):
                        func()
            except Exception as e:
                # 即使执行出错，也要停止profiler
                profiler.disable()
                return ToolResult(
                    success=False,
                    error=f"代码执行错误: {e}"
                )
            
            profiler.disable()
            
            # 分析结果
            stats_stream = io.StringIO()
            stats = pstats.Stats(profiler, stream=stats_stream)
            stats.sort_stats('cumulative')
            stats.print_stats(20)  # 显示前20个最耗时的函数
            
            profile_output = stats_stream.getvalue()
            
            # 提取关键性能指标
            performance_metrics = self._extract_performance_metrics(stats)
            
            return ToolResult(
                success=True,
                data={
                    'file_path': file_path,
                    'function_name': function_name,
                    'profile_output': profile_output,
                    'metrics': performance_metrics,
                    'top_functions': self._get_top_functions(stats, 10)
                },
                message=f"性能分析完成，总调用次数: {performance_metrics['total_calls']}"
            )
            
        except Exception as e:
            self.log_error(e, f"性能分析: {file_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def monitor_memory(self, duration_seconds: int = 10) -> ToolResult:
        """
        监控内存使用情况
        
        Args:
            duration_seconds: 监控持续时间（秒）
            
        Returns:
            ToolResult: 内存监控结果
        """
        try:
            # 开始内存追踪
            tracemalloc.start()
            
            memory_snapshots = []
            start_time = time.time()
            
            # 获取初始内存状态
            process = psutil.Process()
            initial_memory = process.memory_info()
            
            # 监控循环
            while time.time() - start_time < duration_seconds:
                current_memory = process.memory_info()
                current_snapshot = tracemalloc.take_snapshot()
                
                memory_snapshots.append({
                    'timestamp': time.time() - start_time,
                    'rss': current_memory.rss,  # 物理内存
                    'vms': current_memory.vms,  # 虚拟内存
                    'percent': process.memory_percent(),
                    'tracemalloc_size': sum(stat.size for stat in current_snapshot.statistics('filename'))
                })
                
                time.sleep(0.5)  # 每0.5秒采样一次
            
            # 停止内存追踪
            final_snapshot = tracemalloc.take_snapshot()
            tracemalloc.stop()
            
            # 分析内存使用趋势
            memory_analysis = self._analyze_memory_usage(memory_snapshots, initial_memory)
            
            # 获取内存使用最多的代码位置
            top_stats = final_snapshot.statistics('lineno')[:10]
            memory_hotspots = []
            for stat in top_stats:
                memory_hotspots.append({
                    'filename': stat.traceback.format()[0],
                    'size_mb': stat.size / 1024 / 1024,
                    'count': stat.count
                })
            
            return ToolResult(
                success=True,
                data={
                    'duration': duration_seconds,
                    'snapshots': memory_snapshots,
                    'analysis': memory_analysis,
                    'memory_hotspots': memory_hotspots,
                    'peak_memory_mb': max(s['rss'] for s in memory_snapshots) / 1024 / 1024
                },
                message=f"内存监控完成，峰值内存: {memory_analysis['peak_memory_mb']:.2f}MB"
            )
            
        except Exception as e:
            self.log_error(e, "内存监控")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def benchmark_function(self, file_path: str, function_name: str, iterations: int = 1000) -> ToolResult:
        """
        对特定函数进行基准测试
        
        Args:
            file_path: Python文件路径
            function_name: 函数名
            iterations: 测试迭代次数
            
        Returns:
            ToolResult: 基准测试结果
        """
        try:
            # 导入文件中的函数
            exec_globals = {}
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            exec(code, exec_globals)
            
            if function_name not in exec_globals:
                return ToolResult(
                    success=False,
                    error=f"函数 {function_name} 不存在"
                )
            
            func = exec_globals[function_name]
            if not callable(func):
                return ToolResult(
                    success=False,
                    error=f"{function_name} 不是可调用函数"
                )
            
            # 执行基准测试
            execution_times = []
            
            for i in range(iterations):
                start_time = time.perf_counter()
                try:
                    func()
                except Exception as e:
                    return ToolResult(
                        success=False,
                        error=f"函数执行错误: {e}"
                    )
                end_time = time.perf_counter()
                execution_times.append(end_time - start_time)
            
            # 计算统计信息
            avg_time = sum(execution_times) / len(execution_times)
            min_time = min(execution_times)
            max_time = max(execution_times)
            
            # 计算百分位数
            sorted_times = sorted(execution_times)
            p50 = sorted_times[len(sorted_times) // 2]
            p95 = sorted_times[int(len(sorted_times) * 0.95)]
            p99 = sorted_times[int(len(sorted_times) * 0.99)]
            
            benchmark_results = {
                'function_name': function_name,
                'iterations': iterations,
                'avg_time_ms': avg_time * 1000,
                'min_time_ms': min_time * 1000,
                'max_time_ms': max_time * 1000,
                'p50_ms': p50 * 1000,
                'p95_ms': p95 * 1000,
                'p99_ms': p99 * 1000,
                'ops_per_second': 1 / avg_time if avg_time > 0 else 0
            }
            
            return ToolResult(
                success=True,
                data=benchmark_results,
                message=f"基准测试完成，平均执行时间: {avg_time * 1000:.3f}ms"
            )
            
        except Exception as e:
            self.log_error(e, f"基准测试: {function_name}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def analyze_system_resources(self) -> ToolResult:
        """
        分析系统资源使用情况
        
        Returns:
            ToolResult: 系统资源分析结果
        """
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 内存信息
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # 磁盘信息
            disk_usage = psutil.disk_usage('/')
            
            # 网络信息
            network_io = psutil.net_io_counters()
            
            # 进程信息
            current_process = psutil.Process()
            process_info = {
                'pid': current_process.pid,
                'memory_percent': current_process.memory_percent(),
                'cpu_percent': current_process.cpu_percent(),
                'num_threads': current_process.num_threads(),
                'create_time': current_process.create_time()
            }
            
            system_analysis = {
                'cpu': {
                    'usage_percent': cpu_percent,
                    'core_count': cpu_count,
                    'frequency_mhz': cpu_freq.current if cpu_freq else None
                },
                'memory': {
                    'total_gb': memory.total / 1024**3,
                    'available_gb': memory.available / 1024**3,
                    'used_percent': memory.percent,
                    'swap_used_percent': swap.percent
                },
                'disk': {
                    'total_gb': disk_usage.total / 1024**3,
                    'free_gb': disk_usage.free / 1024**3,
                    'used_percent': (disk_usage.used / disk_usage.total) * 100
                },
                'network': {
                    'bytes_sent': network_io.bytes_sent,
                    'bytes_recv': network_io.bytes_recv,
                    'packets_sent': network_io.packets_sent,
                    'packets_recv': network_io.packets_recv
                },
                'current_process': process_info
            }
            
            # 生成资源使用建议
            recommendations = self._generate_resource_recommendations(system_analysis)
            
            return ToolResult(
                success=True,
                data={
                    'system_analysis': system_analysis,
                    'recommendations': recommendations
                },
                message=f"系统资源分析完成，CPU使用率: {cpu_percent}%，内存使用率: {memory.percent}%"
            )
            
        except Exception as e:
            self.log_error(e, "系统资源分析")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _extract_performance_metrics(self, stats: pstats.Stats) -> Dict:
        """提取性能指标"""
        total_calls = stats.total_calls
        total_time = stats.total_tt
        
        return {
            'total_calls': total_calls,
            'total_time_seconds': total_time,
            'calls_per_second': total_calls / total_time if total_time > 0 else 0
        }
    
    def _get_top_functions(self, stats: pstats.Stats, count: int) -> List[Dict]:
        """获取最耗时的函数"""
        top_functions = []
        
        # 获取统计数据
        for func, (cc, nc, tt, ct, callers) in stats.stats.items():
            filename, line, func_name = func
            top_functions.append({
                'function': func_name,
                'filename': filename,
                'line': line,
                'total_time': tt,
                'cumulative_time': ct,
                'call_count': nc
            })
        
        # 按累积时间排序
        top_functions.sort(key=lambda x: x['cumulative_time'], reverse=True)
        return top_functions[:count]
    
    def _analyze_memory_usage(self, snapshots: List[Dict], initial_memory) -> Dict:
        """分析内存使用趋势"""
        if not snapshots:
            return {}
        
        peak_memory = max(s['rss'] for s in snapshots)
        final_memory = snapshots[-1]['rss']
        memory_growth = final_memory - initial_memory.rss
        
        return {
            'initial_memory_mb': initial_memory.rss / 1024 / 1024,
            'peak_memory_mb': peak_memory / 1024 / 1024,
            'final_memory_mb': final_memory / 1024 / 1024,
            'memory_growth_mb': memory_growth / 1024 / 1024,
            'avg_memory_percent': sum(s['percent'] for s in snapshots) / len(snapshots)
        }
    
    def _generate_resource_recommendations(self, analysis: Dict) -> List[str]:
        """生成资源使用建议"""
        recommendations = []
        
        cpu_usage = analysis['cpu']['usage_percent']
        memory_usage = analysis['memory']['used_percent']
        disk_usage = analysis['disk']['used_percent']
        
        if cpu_usage > 80:
            recommendations.append("CPU使用率过高，考虑优化算法或增加并行处理")
        
        if memory_usage > 85:
            recommendations.append("内存使用率过高，检查是否有内存泄漏或优化数据结构")
        
        if disk_usage > 90:
            recommendations.append("磁盘空间不足，清理临时文件或扩展存储空间")
        
        if not recommendations:
            recommendations.append("系统资源使用正常")
        
        return recommendations
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            file_path = args[0]
            return await self.profile_code(file_path)
        else:
            return await self.analyze_system_resources()
