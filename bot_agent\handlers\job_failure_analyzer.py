"""
作业失败分析处理器
专门处理GitLab CI/CD作业失败分析任务，确保正确的执行流程
"""

import logging
import os
import re
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from bot_agent.clients.gitlab_client import GitLabClient
from bot_agent.tools.log_analysis_tools import LogAnalysisTools
from bot_agent.tools.terminal_tools import TerminalTools
from bot_agent.tools.testing_tools import TestingTools
from bot_agent.utils.logging_config import get_logger, global_enhanced_logger

logger = get_logger(__name__)


@dataclass
class JobFailureAnalysis:
    """作业失败分析结果"""
    job_id: int
    job_name: str
    failure_reason: str
    error_details: List[str]
    fix_commands: List[str]
    verification_steps: List[str]
    success: bool
    message: str


class JobFailureAnalyzer:
    """
    作业失败分析器

    专门处理GitLab CI/CD作业失败分析，确保：
    1. 获取真实的作业日志
    2. 分析具体的错误信息
    3. 提供针对性的修复方案
    4. 验证修复效果
    """

    def __init__(self):
        """初始化分析器"""
        self.gitlab_client = GitLabClient()
        self.log_analyzer = LogAnalysisTools()
        self.terminal = TerminalTools()
        self.testing = TestingTools()

        logger.info("JobFailureAnalyzer initialized")

    async def analyze_job_failure(self, job_id: int, project_id: int = None) -> JobFailureAnalysis:
        """
        分析作业失败

        Args:
            job_id: 作业ID
            project_id: 项目ID（可选）

        Returns:
            JobFailureAnalysis: 分析结果
        """
        logger.info(f"开始分析作业失败: Job {job_id}")

        # 记录任务开始
        task_id = f"job_failure_analysis_{job_id}"
        global_enhanced_logger.log_task_start(task_id, "job_failure_analysis")
        start_time = time.time()

        try:
            # 第1步：获取真实的作业信息和日志
            job_info = await self._get_job_info(job_id, project_id)
            if not job_info:
                return JobFailureAnalysis(
                    job_id=job_id,
                    job_name="unknown",
                    failure_reason="无法获取作业信息",
                    error_details=[],
                    fix_commands=[],
                    verification_steps=[],
                    success=False,
                    message="无法从GitLab获取作业信息"
                )

            job_log = await self._get_job_log(job_id, project_id)
            if not job_log:
                return JobFailureAnalysis(
                    job_id=job_id,
                    job_name=job_info.get('name', 'unknown'),
                    failure_reason=job_info.get('failure_reason', 'unknown'),
                    error_details=[],
                    fix_commands=[],
                    verification_steps=[],
                    success=False,
                    message="无法获取作业日志"
                )

            # 第2步：分析具体的错误信息
            error_analysis = await self._analyze_job_log(job_log, job_info)

            # 第3步：生成针对性的修复方案
            fix_plan = await self._generate_fix_plan(error_analysis, job_info)

            # 第4步：自动执行修复（新增）
            fix_execution_result = await self._execute_fix_commands(fix_plan, job_info, project_id)

            # 第5步：提交修复到Git（新增）
            git_commit_result = await self._commit_fixes_to_git(fix_execution_result, job_info, project_id)

            # 第6步：验证修复效果
            verification_steps = await self._generate_verification_steps(fix_plan, job_info)
            verification_result = await self._execute_verification(verification_steps, job_info, project_id)

            # 记录任务成功结束
            duration = time.time() - start_time
            global_enhanced_logger.log_task_end(task_id, True, duration)

            return JobFailureAnalysis(
                job_id=job_id,
                job_name=job_info.get('name', 'unknown'),
                failure_reason=job_info.get('failure_reason', 'unknown'),
                error_details=error_analysis['errors'],
                fix_commands=fix_plan['commands'],
                verification_steps=verification_steps,
                success=True,
                message=f"分析完成。修复执行: {fix_execution_result['status']}。Git提交: {git_commit_result['status']}。验证结果: {verification_result['status']}"
            )

        except Exception as e:
            # 记录任务失败结束
            duration = time.time() - start_time
            global_enhanced_logger.log_task_end(task_id, False, duration)
            global_enhanced_logger.log_exception(f"作业失败分析出错: Job {job_id}")

            logger.error(f"作业失败分析出错: {e}")
            return JobFailureAnalysis(
                job_id=job_id,
                job_name="unknown",
                failure_reason="分析失败",
                error_details=[str(e)],
                fix_commands=[],
                verification_steps=[],
                success=False,
                message=f"分析过程出错: {str(e)}"
            )

    async def _get_job_info(self, job_id: int, project_id: int = None) -> Optional[Dict[str, Any]]:
        """获取作业信息"""
        try:
            # 使用默认项目ID或传入的项目ID
            if project_id is None:
                project_id = 3  # 默认项目ID

            logger.info(f"正在获取作业信息: Project {project_id}, Job {job_id}")
            job_info = self.gitlab_client.get_job(project_id, job_id)

            if job_info:
                logger.info(f"✅ 成功获取作业信息: {job_info.get('name')} - {job_info.get('status')}")
                logger.info(f"作业详情: 阶段={job_info.get('stage')}, 失败原因={job_info.get('failure_reason')}")
                return job_info
            else:
                logger.warning(f"❌ 无法获取作业 {job_id} 的信息")
                return None
        except Exception as e:
            logger.error(f"❌ 获取作业信息失败: {e}")
            return None

    async def _get_job_log(self, job_id: int, project_id: int = None) -> Optional[str]:
        """获取作业日志"""
        try:
            # 使用默认项目ID或传入的项目ID
            if project_id is None:
                project_id = 3  # 默认项目ID

            logger.info(f"正在获取作业日志: Project {project_id}, Job {job_id}")
            job_log = self.gitlab_client.get_job_log(project_id, job_id)

            if job_log and job_log.strip():
                logger.info(f"✅ 成功获取作业日志: {len(job_log)} 字符")
                # 记录日志的关键信息用于调试
                lines = job_log.split('\n')
                logger.info(f"日志总行数: {len(lines)}")

                # 查找错误关键词
                error_keywords = ['error', 'failed', 'exception', 'traceback', 'fatal']
                error_lines = []
                for i, line in enumerate(lines):
                    if any(keyword in line.lower() for keyword in error_keywords):
                        error_lines.append(f"第{i+1}行: {line.strip()}")
                        if len(error_lines) >= 5:  # 最多记录5个错误行
                            break

                if error_lines:
                    logger.info(f"发现错误行: {error_lines}")
                else:
                    logger.info("未在日志中发现明显的错误关键词")

                return job_log
            else:
                logger.warning(f"❌ 作业 {job_id} 的日志为空或无法获取")
                return None
        except Exception as e:
            logger.error(f"❌ 获取作业日志失败: {e}")
            return None

    async def _analyze_job_log(self, job_log: str, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析作业日志"""
        try:
            # 将日志内容写入临时文件，然后使用LogAnalysisTools分析
            import tempfile
            import os

            analysis_result = None
            try:
                # 创建临时文件，指定UTF-8编码
                with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False, encoding='utf-8') as temp_file:
                    temp_file.write(job_log)
                    temp_log_path = temp_file.name

                # 使用LogAnalysisTools分析日志文件
                log_analysis = await self.log_analyzer.analyze_logs([temp_log_path])
                if log_analysis.success:
                    analysis_result = log_analysis.data

                # 清理临时文件
                os.unlink(temp_log_path)

            except Exception as e:
                logger.warning(f"使用LogAnalysisTools分析失败: {e}")

            # 根据作业类型进行特定分析
            job_name = job_info.get('name', '').lower()
            specific_errors = []

            if 'lint' in job_name:
                specific_errors = self._analyze_lint_errors(job_log)
            elif 'test' in job_name:
                specific_errors = self._analyze_test_errors(job_log)
            elif 'build' in job_name:
                specific_errors = self._analyze_build_errors(job_log)
            elif 'deploy' in job_name:
                specific_errors = self._analyze_deploy_errors(job_log)

            # 添加基础错误提取
            basic_errors = self._extract_basic_errors(job_log)

            # 合并所有错误
            all_errors = basic_errors + specific_errors
            unique_errors = list(dict.fromkeys(all_errors))  # 去重但保持顺序

            logger.info(f"错误分析完成: 基础错误{len(basic_errors)}个, 特定错误{len(specific_errors)}个, 总计{len(unique_errors)}个")

            return {
                'general_analysis': analysis_result,
                'basic_errors': basic_errors,
                'specific_errors': specific_errors,
                'all_errors': unique_errors,
                'errors': unique_errors if unique_errors else ['未识别到具体错误'],
                'error_count': len(unique_errors)
            }

        except Exception as e:
            logger.error(f"日志分析失败: {e}")
            return {
                'general_analysis': None,
                'specific_errors': [],
                'errors': [f"日志分析失败: {str(e)}"]
            }

    def _extract_basic_errors(self, log: str) -> List[str]:
        """提取基础错误信息"""
        errors = []

        # 通用错误模式
        error_patterns = [
            (r'ERROR:?\s*(.+)', 'ERROR'),
            (r'FAILED:?\s*(.+)', 'FAILED'),
            (r'FATAL:?\s*(.+)', 'FATAL'),
            (r'Exception:?\s*(.+)', 'Exception'),
            (r'Traceback.*?(\w+Error:.*?)(?=\n\w|\n$|\Z)', 'Traceback'),
            (r'(\w+Error):?\s*(.+)', 'Error'),
            (r'(\w+Exception):?\s*(.+)', 'Exception'),
            (r'Command failed:?\s*(.+)', 'Command Failed'),
            (r'Build failed:?\s*(.+)', 'Build Failed'),
            (r'Test failed:?\s*(.+)', 'Test Failed'),
        ]

        for pattern, error_type in error_patterns:
            matches = re.findall(pattern, log, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            for match in matches:
                if isinstance(match, tuple):
                    error_msg = ' '.join(match).strip()
                else:
                    error_msg = match.strip()

                if error_msg and len(error_msg) > 5:  # 过滤太短的错误
                    # 清理错误消息
                    error_msg = error_msg.replace('\n', ' ').strip()
                    if len(error_msg) > 200:  # 截断过长的错误
                        error_msg = error_msg[:200] + '...'

                    errors.append(f"{error_type}: {error_msg}")

        # 去重
        unique_errors = list(dict.fromkeys(errors))
        return unique_errors[:10]  # 最多返回10个错误

    def _analyze_lint_errors(self, log: str) -> List[str]:
        """分析lint错误"""
        errors = []

        # Black格式化错误
        black_errors = re.findall(r'would reformat (.+)', log)
        for file_path in black_errors:
            errors.append(f"Black格式化问题: {file_path}")

        # Flake8错误
        flake8_errors = re.findall(r'(.+):(\d+):(\d+): ([A-Z]\d+) (.+)', log)
        for file_path, line, col, code, message in flake8_errors:
            errors.append(f"Flake8错误 {code}: {file_path}:{line}:{col} - {message}")

        # MyPy错误
        mypy_errors = re.findall(r'(.+):(\d+): error: (.+)', log)
        for file_path, line, message in mypy_errors:
            errors.append(f"MyPy类型错误: {file_path}:{line} - {message}")

        return errors

    def _analyze_test_errors(self, log: str) -> List[str]:
        """分析测试错误"""
        errors = []

        # Pytest失败
        pytest_failures = re.findall(r'FAILED (.+) - (.+)', log)
        for test_name, reason in pytest_failures:
            errors.append(f"测试失败: {test_name} - {reason}")

        # 断言错误
        assertion_errors = re.findall(r'AssertionError: (.+)', log)
        for error in assertion_errors:
            errors.append(f"断言错误: {error}")

        return errors

    def _analyze_build_errors(self, log: str) -> List[str]:
        """分析构建错误"""
        errors = []

        # 编译错误
        compile_errors = re.findall(r'error: (.+)', log)
        for error in compile_errors:
            errors.append(f"编译错误: {error}")

        # 依赖错误
        dependency_errors = re.findall(r'ModuleNotFoundError: No module named \'(.+)\'', log)
        for module in dependency_errors:
            errors.append(f"缺少依赖: {module}")

        return errors

    def _analyze_deploy_errors(self, log: str) -> List[str]:
        """分析部署错误"""
        errors = []

        # 连接错误
        connection_errors = re.findall(r'Connection refused|Connection timeout', log)
        if connection_errors:
            errors.append("部署连接失败")

        # 权限错误
        permission_errors = re.findall(r'Permission denied', log)
        if permission_errors:
            errors.append("部署权限不足")

        return errors

    async def _generate_fix_plan(self, error_analysis: Dict[str, Any], job_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成修复方案"""
        commands = []
        job_name = job_info.get('name', '').lower()
        errors = error_analysis.get('specific_errors', [])

        for error in errors:
            if 'Black格式化问题' in error:
                file_path = error.split(': ')[1]
                commands.append(f"black {file_path}")
            elif 'Flake8错误' in error:
                # 提取具体的修复建议
                if 'E302' in error:
                    commands.append("在类定义前添加2个空行")
                elif 'E501' in error:
                    commands.append("缩短过长的代码行")
                elif 'F401' in error:
                    commands.append("移除未使用的导入")
            elif '缺少依赖' in error:
                module = error.split(': ')[1]
                commands.append(f"pip install {module}")
            elif '测试失败' in error:
                commands.append("检查测试逻辑并修复失败的测试用例")

        # 如果没有具体命令，提供通用建议
        if not commands:
            if 'lint' in job_name:
                commands.append("运行 flake8 . 查看具体的代码风格问题")
                commands.append("运行 black . 自动格式化代码")
            elif 'test' in job_name:
                commands.append("运行 pytest -v 查看详细的测试失败信息")
            elif 'build' in job_name:
                commands.append("检查依赖文件和构建配置")

        return {
            'commands': commands,
            'description': f"针对 {job_name} 作业的修复方案"
        }

    async def _generate_verification_steps(self, fix_plan: Dict[str, Any], job_info: Dict[str, Any]) -> List[str]:
        """生成验证步骤"""
        steps = []
        job_name = job_info.get('name', '').lower()

        if 'lint' in job_name:
            steps.extend([
                "运行 black --check . 验证格式化",
                "运行 flake8 . 验证代码风格",
                "运行 mypy . 验证类型注解"
            ])
        elif 'test' in job_name:
            steps.extend([
                "运行 pytest 验证所有测试通过",
                "检查测试覆盖率"
            ])
        elif 'build' in job_name:
            steps.extend([
                "重新构建项目",
                "验证构建产物"
            ])
        elif 'deploy' in job_name:
            steps.extend([
                "验证部署环境连接",
                "检查部署权限",
                "测试部署后的服务状态"
            ])

        return steps

    async def _execute_fix_commands(self, fix_plan: Dict[str, Any], job_info: Dict[str, Any], project_id: int) -> Dict[str, Any]:
        """执行修复命令"""
        try:
            from bot_agent.tools.terminal_tools import TerminalTools

            terminal_tools = TerminalTools()
            commands = fix_plan.get('commands', [])
            job_name = job_info.get('name', '').lower()

            # 确定项目工作目录
            project_dir = self._get_project_directory(project_id)
            logger.info(f"设置工作目录为: {project_dir}")

            # 检查和准备执行环境
            env_check_result = await self._prepare_execution_environment(terminal_tools, project_dir, job_name)

            if not commands:
                return {
                    'status': '跳过',
                    'message': '没有可执行的修复命令',
                    'executed_commands': [],
                    'results': []
                }

            executed_commands = []
            results = []

            # 对于lint作业，优化修复策略
            if 'lint' in job_name:
                # 检查是否有多个Black格式化问题
                black_commands = [cmd for cmd in commands if cmd.startswith('black ') and not cmd.endswith(' .')]

                if len(black_commands) > 5:  # 如果有超过5个文件需要格式化
                    # 使用批量命令替代单个文件命令
                    logger.info(f"检测到{len(black_commands)}个Black格式化问题，使用批量修复")
                    batch_command = "black ."

                    result = await terminal_tools.execute_command(batch_command, cwd=project_dir)
                    executed_commands.append(batch_command)
                    results.append({
                        'command': batch_command,
                        'success': result.success,
                        'output': result.data if result.success else result.error,
                        'description': f'批量格式化{len(black_commands)}个文件'
                    })
                else:
                    # 执行单个命令
                    for command in commands[:10]:  # 限制最多执行10个命令
                        if command.startswith('black '):
                            result = await terminal_tools.execute_command(command, cwd=project_dir)
                            executed_commands.append(command)
                            results.append({
                                'command': command,
                                'success': result.success,
                                'output': result.data if result.success else result.error
                            })
            elif 'test' in job_name:
                # 测试作业修复
                for command in commands[:8]:  # 测试可能需要更多命令
                    if any(cmd_type in command.lower() for cmd_type in ['pip install', 'pytest', 'python -m pytest']):
                        result = await terminal_tools.execute_command(command, cwd=project_dir)
                        executed_commands.append(command)
                        results.append({
                            'command': command,
                            'success': result.success,
                            'output': result.data if result.success else result.error
                        })
            elif 'build' in job_name:
                # 构建作业修复
                for command in commands[:6]:
                    if any(cmd_type in command.lower() for cmd_type in ['pip install', 'python setup.py', 'python -m build']):
                        result = await terminal_tools.execute_command(command, cwd=project_dir)
                        executed_commands.append(command)
                        results.append({
                            'command': command,
                            'success': result.success,
                            'output': result.data if result.success else result.error
                        })
            elif 'deploy' in job_name:
                # 部署作业修复
                for command in commands[:4]:  # 部署命令通常较少但重要
                    if any(cmd_type in command.lower() for cmd_type in ['docker', 'kubectl', 'ssh', 'scp']):
                        result = await terminal_tools.execute_command(command, cwd=project_dir)
                        executed_commands.append(command)
                        results.append({
                            'command': command,
                            'success': result.success,
                            'output': result.data if result.success else result.error
                        })
            else:
                # 通用作业修复
                for command in commands[:5]:  # 限制最多执行5个命令
                    if any(cmd_type in command.lower() for cmd_type in ['pip install', 'black', 'flake8', 'python', 'npm install']):
                        result = await terminal_tools.execute_command(command, cwd=project_dir)
                        executed_commands.append(command)
                        results.append({
                            'command': command,
                            'success': result.success,
                            'output': result.data if result.success else result.error
                        })

            success_count = sum(1 for r in results if r['success'])
            total_count = len(results)

            return {
                'status': f'已执行 {success_count}/{total_count} 个命令',
                'message': f'成功执行{success_count}个修复命令，失败{total_count - success_count}个',
                'executed_commands': executed_commands,
                'results': results
            }

        except Exception as e:
            logger.error(f"执行修复命令失败: {e}")
            return {
                'status': '执行失败',
                'message': f'修复命令执行出错: {str(e)}',
                'executed_commands': [],
                'results': []
            }

    async def _execute_verification(self, verification_steps: List[str], job_info: Dict[str, Any], project_id: int) -> Dict[str, Any]:
        """执行验证步骤"""
        try:
            from bot_agent.tools.terminal_tools import TerminalTools

            terminal_tools = TerminalTools()
            job_name = job_info.get('name', '').lower()

            # 获取项目工作目录
            project_dir = self._get_project_directory(project_id)

            if not verification_steps:
                return {
                    'status': '跳过',
                    'message': '没有验证步骤',
                    'results': []
                }

            results = []

            # 只执行关键的验证命令
            key_commands = []
            if 'lint' in job_name:
                key_commands = ['black --check .', 'flake8 . --count']
            elif 'test' in job_name:
                key_commands = ['pytest --tb=short', 'python -m pytest --collect-only']
            elif 'build' in job_name:
                key_commands = ['python -m py_compile *.py', 'python setup.py check']
            elif 'deploy' in job_name:
                key_commands = ['docker --version', 'kubectl version --client']
            elif 'security' in job_name:
                key_commands = ['bandit --version', 'safety --version']
            elif 'docs' in job_name:
                key_commands = ['sphinx-build --version', 'mkdocs --version']
            else:
                # 通用验证
                key_commands = ['python --version', 'pip list']

            for command in key_commands:
                try:
                    result = await terminal_tools.execute_command(command, cwd=project_dir)
                    results.append({
                        'command': command,
                        'success': result.success,
                        'output': result.data if result.success else result.error
                    })
                except Exception as e:
                    results.append({
                        'command': command,
                        'success': False,
                        'output': f'验证命令执行失败: {str(e)}'
                    })

            success_count = sum(1 for r in results if r['success'])
            total_count = len(results)

            return {
                'status': f'验证 {success_count}/{total_count} 通过',
                'message': f'验证完成，{success_count}个通过，{total_count - success_count}个失败',
                'results': results
            }

        except Exception as e:
            logger.error(f"执行验证失败: {e}")
            return {
                'status': '验证失败',
                'message': f'验证执行出错: {str(e)}',
                'results': []
            }

    async def _commit_fixes_to_git(self, fix_execution_result: Dict[str, Any], job_info: Dict[str, Any], project_id: int) -> Dict[str, Any]:
        """提交修复到Git仓库"""
        try:
            from bot_agent.tools.terminal_tools import TerminalTools

            terminal_tools = TerminalTools()
            job_name = job_info.get('name', 'unknown')
            job_id = job_info.get('id', 'unknown')

            # 获取项目工作目录
            project_dir = self._get_project_directory(project_id)

            # 检查是否有修复被执行
            if not fix_execution_result.get('executed_commands'):
                return {
                    'status': '跳过',
                    'message': '没有执行修复命令，跳过Git提交',
                    'commands': []
                }

            # 检查修复是否成功
            success_count = sum(1 for r in fix_execution_result.get('results', []) if r.get('success'))
            if success_count == 0:
                return {
                    'status': '跳过',
                    'message': '修复命令全部失败，跳过Git提交',
                    'commands': []
                }

            git_commands = []
            git_results = []

            # Git操作序列
            commit_message = f"🤖 自动修复 {job_name} 作业失败 (Job {job_id})"

            git_operations = [
                ("git add .", "添加修改的文件"),
                ("git status", "检查状态"),
                (f'git commit -m "{commit_message}"', "提交修复"),
                ("git push", "推送到远程仓库")
            ]

            for command, description in git_operations:
                try:
                    logger.info(f"执行Git操作: {description} - {command}")
                    result = await terminal_tools.execute_command(command, cwd=project_dir)

                    git_commands.append(command)
                    git_results.append({
                        'command': command,
                        'description': description,
                        'success': result.success,
                        'output': result.data['stdout'] if result.data else '',
                        'error': result.data['stderr'] if result.data else ''
                    })

                    # 如果git commit失败（可能没有变更），继续执行
                    if not result.success and 'commit' in command:
                        if 'nothing to commit' in (result.data.get('stdout', '') + result.data.get('stderr', '')):
                            logger.info("没有变更需要提交")
                            git_results[-1]['success'] = True
                            git_results[-1]['note'] = '没有变更需要提交'
                        else:
                            logger.warning(f"Git commit失败: {result.data}")
                            break
                    elif not result.success:
                        logger.error(f"Git操作失败: {command} - {result.data}")
                        break

                except Exception as e:
                    logger.error(f"Git操作异常: {command} - {e}")
                    git_results.append({
                        'command': command,
                        'description': description,
                        'success': False,
                        'error': str(e)
                    })
                    break

            success_count = sum(1 for r in git_results if r['success'])
            total_count = len(git_results)

            # 检查是否成功推送
            push_success = any(r['success'] and 'push' in r['command'] for r in git_results)

            if push_success:
                status = '✅ 已推送'
                message = f"成功提交并推送修复到远程仓库，触发新的CI/CD流程"
            elif success_count >= 3:  # add, status, commit成功
                status = '⚠️ 部分成功'
                message = f"修复已提交到本地仓库，但推送失败"
            else:
                status = '❌ 失败'
                message = f"Git操作失败，无法提交修复"

            return {
                'status': status,
                'message': message,
                'commands': git_commands,
                'results': git_results,
                'push_success': push_success
            }

        except Exception as e:
            logger.error(f"Git提交失败: {e}")
            return {
                'status': '❌ 异常',
                'message': f'Git提交过程出错: {str(e)}',
                'commands': [],
                'results': []
            }

    def _get_project_directory(self, project_id: int) -> str:
        """获取项目工作目录"""
        # 根据项目ID确定工作目录
        if project_id == 3:  # ai-proxy项目
            # 检查常见的项目路径
            possible_paths = [
                "E:\\aider-git-repos\\ai-proxy",
                "E:\\Projects\\ai-proxy",
                "/workspace/ai-proxy",
                "./ai-proxy",
                "."
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    logger.info(f"找到项目目录: {path}")
                    return path

            # 如果都不存在，使用当前目录
            logger.warning("未找到ai-proxy项目目录，使用当前目录")
            return "."
        else:
            # 其他项目使用当前目录
            return "."

    async def _prepare_execution_environment(self, terminal_tools, project_dir: str, job_name: str) -> Dict[str, Any]:
        """准备执行环境"""
        try:
            logger.info(f"准备执行环境: 项目目录={project_dir}, 作业类型={job_name}")

            # 检查工作目录
            if not os.path.exists(project_dir):
                return {
                    'success': False,
                    'message': f'项目目录不存在: {project_dir}'
                }

            # 检查必要的工具
            tool_checks = []

            if 'lint' in job_name:
                # 检查black是否可用
                black_check = await terminal_tools.execute_command("black --version", cwd=project_dir)
                tool_checks.append(('black', black_check.success))

                if not black_check.success:
                    # 尝试安装black
                    logger.info("Black不可用，尝试安装...")
                    install_result = await terminal_tools.execute_command("pip install black", cwd=project_dir)
                    if install_result.success:
                        # 重新检查
                        black_check2 = await terminal_tools.execute_command("black --version", cwd=project_dir)
                        tool_checks.append(('black_after_install', black_check2.success))

            elif 'test' in job_name:
                # 检查pytest
                pytest_check = await terminal_tools.execute_command("pytest --version", cwd=project_dir)
                tool_checks.append(('pytest', pytest_check.success))

            elif 'build' in job_name:
                # 检查python和pip
                python_check = await terminal_tools.execute_command("python --version", cwd=project_dir)
                tool_checks.append(('python', python_check.success))

            # 检查git
            git_check = await terminal_tools.execute_command("git --version", cwd=project_dir)
            tool_checks.append(('git', git_check.success))

            available_tools = [name for name, available in tool_checks if available]
            missing_tools = [name for name, available in tool_checks if not available]

            logger.info(f"可用工具: {available_tools}")
            if missing_tools:
                logger.warning(f"缺失工具: {missing_tools}")

            return {
                'success': len(available_tools) > 0,
                'project_dir': project_dir,
                'available_tools': available_tools,
                'missing_tools': missing_tools,
                'message': f'环境检查完成，可用工具: {len(available_tools)}个'
            }

        except Exception as e:
            logger.error(f"环境准备失败: {e}")
            return {
                'success': False,
                'message': f'环境准备异常: {str(e)}'
            }


# 全局实例
global_job_failure_analyzer = JobFailureAnalyzer()
