"""
Bot Agent 主应用 - 启动服务并连接所有组件
"""

import datetime
import logging
import os
import sys

from typing import Dict

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 确保当前目录也在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 打印Python路径，用于调试
print("Python path:", sys.path)



import uvicorn
from fastapi import FastAPI, HTTPException, Request

# 尝试直接导入模块
try:
    from api.app import app
    from api.docs import router as docs_router
    from api.routes import router as api_router
    from api.websocket import router as websocket_router
    from dispatcher.router import TaskRouter
    from webhook.gitlab import router as gitlab_router
    print("成功导入本地模块")
except ImportError as e:
    print(f"本地导入失败: {e}")
    # 如果直接导入失败，尝试使用完整路径
    from bot_agent.api.app import app
    from bot_agent.api.docs import router as docs_router
    from bot_agent.api.routes import router as api_router
    from bot_agent.api.websocket import router as websocket_router
    from bot_agent.dispatcher.router import TaskRouter
    from bot_agent.webhook.gitlab import router as gitlab_router
    print("成功导入完整路径模块")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# 创建任务路由器
task_router = TaskRouter()

# 注册路由
print("注册 GitLab webhook 路由...")
# 现在在这里设置前缀，因为 gitlab_router 已经被修改为不使用前缀
app.include_router(gitlab_router, prefix="/webhook/gitlab")
print(f"GitLab webhook 路由已注册，路径前缀: /webhook/gitlab")

# 添加一个直接的 webhook 测试路由
@app.get("/webhook/gitlab/direct-test")
async def direct_test():
    """直接的 webhook 测试路由"""
    logger.info("直接的 webhook 测试路由被访问")
    return {"status": "success", "message": "直接的 webhook 测试路由正常工作"}

# 添加一个直接的 webhook 处理函数，用于测试
@app.post("/webhook/gitlab/direct")
async def direct_webhook(request: Request):
    """直接的 webhook 处理函数，用于测试"""
    logger.info("=== 直接的 webhook 处理函数被访问 ===")
    logger.info(f"请求方法: {request.method}")
    logger.info(f"请求路径: {request.url.path}")
    logger.info(f"请求头: {dict(request.headers)}")

    try:
        # 获取请求体
        payload = await request.json()
        logger.info(f"请求体: {payload}")

        return {
            "status": "success",
            "message": "直接的 webhook 处理函数正常工作",
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"处理请求时出错: {e}")
        return {
            "status": "error",
            "message": f"处理请求时出错: {str(e)}",
            "timestamp": datetime.datetime.now().isoformat()
        }

print("注册 API 路由...")
app.include_router(api_router)
print("注册 WebSocket 路由...")
app.include_router(websocket_router)
print("注册文档路由...")
app.include_router(docs_router)

# 打印所有注册的路由，用于调试
print("\n=== 已注册的路由 ===")
for route in app.routes:
    print(f"路由: {route.path}, 方法: {getattr(route, 'methods', ['GET'])}")

# 特别检查 webhook 路由
webhook_routes = [route for route in app.routes if "/webhook/gitlab" in route.path]
print(f"\n=== 找到 {len(webhook_routes)} 个 webhook 路由 ===")
for route in webhook_routes:
    print(f"Webhook 路由: {route.path}, 方法: {getattr(route, 'methods', ['GET'])}")

# 添加路由信息端点，用于调试
@app.get("/debug/routes")
async def debug_routes():
    """
    路由信息端点，用于调试

    Returns:
        Dict: 路由信息
    """
    routes = [{"path": route.path, "methods": list(getattr(route, "methods", ["GET"]))} for route in app.routes]
    return {
        "service": "Bot Agent API",
        "version": "0.1.0",
        "status": "running",
        "routes": routes,
        "timestamp": datetime.datetime.now().isoformat()
    }


@app.post("/api/tasks")
async def create_task(request: Request) -> Dict:
    """
    创建新任务的 API 端点

    Args:
        request: FastAPI 请求对象

    Returns:
        Dict: 任务创建结果
    """
    try:
        payload = await request.json()

        # 验证必要字段
        required_fields = ["title", "description", "source", "source_id"]
        for field in required_fields:
            if field not in payload:
                raise HTTPException(
                    status_code=400,
                    detail=f"Missing required field: {field}",
                )

        # 路由任务
        result = await task_router.route_task(
            title=payload["title"],
            description=payload["description"],
            source=payload["source"],
            source_id=payload["source_id"],
            labels=payload.get("labels"),
            metadata=payload.get("metadata"),
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating task: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error creating task: {str(e)}",
        )




def start():
    """启动应用"""
    # 获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))

    # 添加直接打印，确保输出可见
    print(f"=== 启动 Bot Agent 服务 ===")
    print(f"主机: {host}")
    print(f"端口: {port}")
    print(f"GitLab Webhook URL: http://{os.getenv('SERVICE_ADDRESS', host)}:{os.getenv('EXTERNAL_PORT', str(port))}/webhook/gitlab/")
    print(f"GitLab Webhook Token: {os.getenv('GITLAB_WEBHOOK_TOKEN', '未设置')}")
    print(f"日志级别: {os.getenv('LOG_LEVEL', 'INFO')}")



    print(f"=== 服务启动中，请等待... ===")

    # 启动服务
    logger.info(f"Starting Bot Agent on {host}:{port}")
    uvicorn.run(app, host=host, port=port)


if __name__ == "__main__":
    start()
