"""
全局记忆管理器 - 管理用户的全局工作习惯和偏好
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class GlobalMemoryManager:
    """
    全局记忆管理器

    负责管理用户的全局工作习惯、偏好设置、常用模式等
    存储在aider-plus项目的memory目录下
    """

    def __init__(self, memory_dir: str = None):
        """
        初始化全局记忆管理器

        Args:
            memory_dir: 记忆存储目录，默认为当前项目的memory目录
        """
        if memory_dir is None:
            # 使用当前项目的memory目录
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            memory_dir = os.path.join(current_dir, "memory", "global")

        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(parents=True, exist_ok=True)

        # 记忆文件路径
        self.habits_file = self.memory_dir / "work_habits.json"
        self.preferences_file = self.memory_dir / "preferences.json"
        self.patterns_file = self.memory_dir / "common_patterns.json"
        self.environment_file = self.memory_dir / "environment.json"

        logger.info(f"GlobalMemoryManager initialized with directory: {self.memory_dir}")

    def load_memory(self) -> Dict[str, Any]:
        """
        加载全局记忆

        Returns:
            Dict: 包含所有全局记忆的字典
        """
        memory = {
            "work_habits": self._load_json_file(self.habits_file),
            "preferences": self._load_json_file(self.preferences_file),
            "common_patterns": self._load_json_file(self.patterns_file),
            "environment": self._load_json_file(self.environment_file)
        }

        logger.info("Global memory loaded")
        return memory

    def save_work_habit(self, habit_type: str, habit_data: Dict[str, Any]) -> None:
        """
        保存工作习惯

        Args:
            habit_type: 习惯类型 (coding_style, naming_convention, etc.)
            habit_data: 习惯数据
        """
        habits = self._load_json_file(self.habits_file)

        if habit_type not in habits:
            habits[habit_type] = []

        # 添加时间戳
        habit_data["recorded_at"] = datetime.now().isoformat()
        habit_data["frequency"] = habit_data.get("frequency", 1)

        # 检查是否已存在相似习惯
        existing_habit = self._find_similar_habit(habits[habit_type], habit_data)
        if existing_habit:
            # 增加频率
            existing_habit["frequency"] += 1
            existing_habit["last_seen"] = datetime.now().isoformat()
        else:
            habits[habit_type].append(habit_data)

        self._save_json_file(self.habits_file, habits)
        logger.info(f"Work habit saved: {habit_type}")

    def save_preference(self, pref_category: str, pref_data: Dict[str, Any]) -> None:
        """
        保存用户偏好

        Args:
            pref_category: 偏好类别 (tools, languages, frameworks, etc.)
            pref_data: 偏好数据
        """
        preferences = self._load_json_file(self.preferences_file)

        if pref_category not in preferences:
            preferences[pref_category] = {}

        preferences[pref_category].update(pref_data)
        preferences[pref_category]["updated_at"] = datetime.now().isoformat()

        self._save_json_file(self.preferences_file, preferences)
        logger.info(f"Preference saved: {pref_category}")

    def save_common_pattern(self, pattern_type: str, pattern_data: Dict[str, Any]) -> None:
        """
        保存常用模式

        Args:
            pattern_type: 模式类型 (task_patterns, solution_patterns, etc.)
            pattern_data: 模式数据
        """
        patterns = self._load_json_file(self.patterns_file)

        if pattern_type not in patterns:
            patterns[pattern_type] = []

        pattern_data["recorded_at"] = datetime.now().isoformat()
        pattern_data["usage_count"] = pattern_data.get("usage_count", 1)

        # 检查是否已存在相似模式
        existing_pattern = self._find_similar_pattern(patterns[pattern_type], pattern_data)
        if existing_pattern:
            existing_pattern["usage_count"] += 1
            existing_pattern["last_used"] = datetime.now().isoformat()
        else:
            patterns[pattern_type].append(pattern_data)

        self._save_json_file(self.patterns_file, patterns)
        logger.info(f"Common pattern saved: {pattern_type}")

    def save_environment_info(self, env_data: Dict[str, Any]) -> None:
        """
        保存环境信息

        Args:
            env_data: 环境数据
        """
        environment = self._load_json_file(self.environment_file)
        environment.update(env_data)
        environment["updated_at"] = datetime.now().isoformat()

        self._save_json_file(self.environment_file, environment)
        logger.info("Environment info saved")

    def get_relevant_habits(self, context: str) -> List[Dict[str, Any]]:
        """
        获取相关的工作习惯

        Args:
            context: 上下文信息

        Returns:
            List: 相关的工作习惯列表
        """
        habits = self._load_json_file(self.habits_file)
        relevant_habits = []

        for habit_type, habit_list in habits.items():
            for habit in habit_list:
                if self._is_habit_relevant(habit, context):
                    relevant_habits.append({
                        "type": habit_type,
                        "data": habit
                    })

        # 按频率排序
        relevant_habits.sort(key=lambda x: x["data"].get("frequency", 0), reverse=True)
        return relevant_habits[:10]  # 返回最相关的10个

    def get_preferences_summary(self) -> Dict[str, Any]:
        """
        获取偏好设置摘要

        Returns:
            Dict: 偏好设置摘要
        """
        preferences = self._load_json_file(self.preferences_file)
        return {
            "summary": self._generate_preferences_summary(preferences),
            "details": preferences
        }

    def _load_json_file(self, file_path: Path) -> Dict[str, Any]:
        """加载JSON文件"""
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"Failed to load {file_path}: {e}")
        return {}

    def _save_json_file(self, file_path: Path, data: Dict[str, Any]) -> None:
        """保存JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except IOError as e:
            logger.error(f"Failed to save {file_path}: {e}")

    def _find_similar_habit(self, habits: List[Dict], new_habit: Dict) -> Optional[Dict]:
        """查找相似的习惯"""
        for habit in habits:
            if self._habits_similar(habit, new_habit):
                return habit
        return None

    def _find_similar_pattern(self, patterns: List[Dict], new_pattern: Dict) -> Optional[Dict]:
        """查找相似的模式"""
        for pattern in patterns:
            if self._patterns_similar(pattern, new_pattern):
                return pattern
        return None

    def _habits_similar(self, habit1: Dict, habit2: Dict) -> bool:
        """判断两个习惯是否相似"""
        # 简单的相似性判断，可以根据需要改进
        key_fields = ["description", "pattern", "rule"]
        for field in key_fields:
            if field in habit1 and field in habit2:
                if habit1[field] == habit2[field]:
                    return True
        return False

    def _patterns_similar(self, pattern1: Dict, pattern2: Dict) -> bool:
        """判断两个模式是否相似"""
        # 简单的相似性判断，可以根据需要改进
        key_fields = ["name", "description", "template"]
        for field in key_fields:
            if field in pattern1 and field in pattern2:
                if pattern1[field] == pattern2[field]:
                    return True
        return False

    def _is_habit_relevant(self, habit: Dict, context: str) -> bool:
        """判断习惯是否与当前上下文相关"""
        # 简单的相关性判断，可以根据需要改进
        context_lower = context.lower()
        habit_text = str(habit).lower()

        # 检查关键词匹配
        common_words = set(context_lower.split()) & set(habit_text.split())
        return len(common_words) > 0

    def _generate_preferences_summary(self, preferences: Dict) -> str:
        """生成偏好设置摘要"""
        summary_parts = []

        for category, prefs in preferences.items():
            if isinstance(prefs, dict) and prefs:
                summary_parts.append(f"{category}: {', '.join(str(v) for v in prefs.values() if v)}")

        return "; ".join(summary_parts) if summary_parts else "No preferences recorded"

    def export_memory_summary(self) -> str:
        """
        导出记忆摘要，用于AI参考

        Returns:
            str: 格式化的记忆摘要
        """
        memory = self.load_memory()

        summary = "# 全局工作记忆摘要\n\n"

        # 工作习惯
        if memory["work_habits"]:
            summary += "## 工作习惯\n"
            for habit_type, habits in memory["work_habits"].items():
                if habits:
                    # 获取最常用的习惯
                    top_habits = sorted(habits, key=lambda x: x.get("frequency", 0), reverse=True)[:3]
                    summary += f"### {habit_type}\n"
                    for habit in top_habits:
                        summary += f"- {habit.get('description', 'N/A')} (使用频率: {habit.get('frequency', 1)})\n"
            summary += "\n"

        # 偏好设置
        if memory["preferences"]:
            summary += "## 偏好设置\n"
            for category, prefs in memory["preferences"].items():
                if prefs and isinstance(prefs, dict):
                    summary += f"### {category}\n"
                    for key, value in prefs.items():
                        if key != "updated_at" and value:
                            summary += f"- {key}: {value}\n"
            summary += "\n"

        # 常用模式
        if memory["common_patterns"]:
            summary += "## 常用模式\n"
            for pattern_type, patterns in memory["common_patterns"].items():
                if patterns:
                    top_patterns = sorted(patterns, key=lambda x: x.get("usage_count", 0), reverse=True)[:3]
                    summary += f"### {pattern_type}\n"
                    for pattern in top_patterns:
                        summary += f"- {pattern.get('name', 'N/A')}: {pattern.get('description', 'N/A')} (使用次数: {pattern.get('usage_count', 1)})\n"
            summary += "\n"

        # 环境信息
        if memory["environment"]:
            summary += "## 环境信息\n"
            env = memory["environment"]
            for key, value in env.items():
                if key != "updated_at" and value:
                    summary += f"- {key}: {value}\n"
            summary += "\n"

        return summary
