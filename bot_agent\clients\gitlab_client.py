"""
GitLab API 客户端 - 提供与 GitLab API 交互的功能
"""

import logging
import os
from typing import Dict, List, Optional, Union

import requests
from requests.exceptions import RequestException

logger = logging.getLogger(__name__)


class GitLabClient:
    """
    GitLab API 客户端，提供与 GitLab API 交互的接口
    """

    def __init__(
        self,
        base_url: Optional[str] = None,
        api_token: Optional[str] = None,
        timeout: int = 30,
        max_retries: int = 1,
        fallback_mode: bool = True,

    ):
        """
        初始化 GitLab API 客户端

        Args:
            base_url: GitLab API 的基础 URL，如果为 None，则从环境变量 GITLAB_API_URL 获取
            api_token: GitLab API 令牌，如果为 None，则从环境变量 GITLAB_API_TOKEN 获取
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            fallback_mode: 是否启用回退模式（当 API 不可用时不抛出异常）

        """
        self.base_url = base_url or os.getenv("GITLAB_API_URL", "http://192.168.123.103/api/v4")
        self.api_token = api_token or os.getenv("GITLAB_API_TOKEN")
        self.timeout = timeout
        self.max_retries = max_retries
        self.fallback_mode = fallback_mode
        # 移除默认项目ID支持 - 强制要求明确的项目ID
        # self.default_project_id = default_project_id or os.getenv("GITLAB_DEFAULT_PROJECT_ID")
        self.default_project_id = None
        self.is_available = None  # 尚未测试连接

        if not self.api_token:
            logger.warning("GitLab API token not provided, some operations may fail")

        logger.info(f"Initialized GitLab client with base URL: {self.base_url}")

        # 尝试连接 GitLab API
        self._check_connection()

    def _get_headers(self) -> Dict[str, str]:
        """
        获取请求头

        Returns:
            Dict[str, str]: 请求头字典
        """
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        if self.api_token:
            headers["PRIVATE-TOKEN"] = self.api_token

        return headers

    def _check_connection(self) -> bool:
        """
        检查与 GitLab API 的连接

        Returns:
            bool: 连接是否成功
        """
        try:
            # 尝试连接 API 的版本端点
            url = f"{self.base_url}/version"
            logger.info(f"检查GitLab API连接: {url}")
            response = requests.get(url, headers=self._get_headers(), timeout=5)
            response.raise_for_status()
            self.is_available = True
            logger.info(f"Successfully connected to GitLab API: {response.json()}")
            return True
        except Exception as e:
            # 不要立即将API标记为不可用，而是设置为未知状态
            # 这样后续请求会尝试连接，而不是直接跳过
            self.is_available = None
            logger.warning(f"Failed to connect to GitLab API: {e}")
            logger.warning("API可用性设置为未知状态，后续请求将尝试连接")
            return False

    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,

    ) -> Union[Dict, List, None]:
        """
        发送 HTTP 请求

        Args:
            method: HTTP 方法 (GET, POST, PUT, DELETE)
            endpoint: API 端点
            data: 请求数据
            params: 查询参数


        Returns:
            Union[Dict, List, None]: API 响应

        Raises:
            Exception: 如果请求失败且回退模式未启用
        """
        # 只有在确定API不可用且启用了回退模式时，才跳过请求
        # 如果API状态未知，仍然尝试请求
        if self.is_available is False and self.fallback_mode:
            logger.warning(f"Skipping request to {endpoint} as GitLab API is known to be unavailable")
            # 但是，对于关键操作，我们仍然尝试请求
            if "repository/branches" in endpoint or "projects/" in endpoint and not "/" in endpoint.replace("projects/", "", 1):
                logger.info(f"尝试关键操作，即使API可能不可用: {endpoint}")
            else:
                return None

        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = self._get_headers()

        logger.info(f"Making {method} request to {url}")
        if data:
            logger.info(f"Request data: {data}")
        if params:
            logger.info(f"Request params: {params}")

        # 移除默认项目ID回退逻辑 - 强制要求明确的项目ID

        for attempt in range(self.max_retries + 1):
            try:
                response = requests.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params,
                    timeout=self.timeout,
                )

                logger.info(f"Response status code: {response.status_code}")

                # 记录响应内容（仅用于调试）
                if response.content:
                    content_preview = response.content[:500]
                    logger.debug(f"Response content preview: {content_preview}")

                response.raise_for_status()

                # 连接成功，更新状态
                self.is_available = True

                if response.content:
                    result = response.json()
                    logger.info(f"Response JSON: {result}")
                    return result
                return None

            except RequestException as e:
                logger.error(f"GitLab API request failed (attempt {attempt+1}/{self.max_retries+1}): {e}")

                # 尝试解析错误响应
                error_detail = str(e)
                if hasattr(e, "response") and e.response is not None:
                    logger.error(f"Error response status code: {e.response.status_code}")
                    try:
                        error_data = e.response.json()
                        error_detail = error_data.get("message", str(e))
                        logger.error(f"Error response data: {error_data}")
                    except (ValueError, KeyError):
                        logger.error(f"Could not parse error response as JSON")
                        if hasattr(e.response, "text"):
                            logger.error(f"Error response text: {e.response.text[:500]}")

                # 移除默认项目ID回退逻辑 - 直接处理404错误

                # 如果还有重试机会，继续重试
                if attempt < self.max_retries:
                    logger.info(f"Retrying request to {endpoint}...")
                    continue

                # 只有在连接错误时才标记 API 为不可用，404等错误不应该导致API被标记为不可用
                if hasattr(e, "response") and e.response is not None and e.response.status_code >= 500:
                    logger.error(f"服务器错误，标记 API 为不可用: {e.response.status_code}")
                    self.is_available = False
                else:
                    # 对于404等客户端错误，API仍然是可用的
                    logger.warning(f"客户端错误，API仍然可用: {e}")
                    self.is_available = True

                # 如果启用了回退模式，返回 None
                if self.fallback_mode:
                    logger.warning(f"Request to {endpoint} failed, returning None due to fallback mode")
                    return None

                # 否则抛出异常
                raise Exception(f"GitLab API request failed: {error_detail}")

    def get_project(self, project_id: Union[int, str]) -> Dict:
        """
        获取项目信息

        Args:
            project_id: 项目 ID

        Returns:
            Dict: 项目信息
        """
        return self._make_request("GET", f"projects/{project_id}")

    def get_issue(self, project_id: Union[int, str], issue_iid: Union[int, str]) -> Dict:
        """
        获取 Issue 信息

        Args:
            project_id: 项目 ID
            issue_iid: Issue IID (项目内 ID)

        Returns:
            Dict: Issue 信息
        """
        return self._make_request("GET", f"projects/{project_id}/issues/{issue_iid}")

    def create_issue_comment(
        self,
        project_id: Union[int, str],
        issue_iid: Union[int, str],
        body: str
    ) -> Dict:
        """
        创建 Issue 评论

        Args:
            project_id: 项目 ID
            issue_iid: Issue IID (项目内 ID)
            body: 评论内容

        Returns:
            Dict: 创建的评论信息
        """
        data = {"body": body}
        return self._make_request(
            "POST",
            f"projects/{project_id}/issues/{issue_iid}/notes",
            data=data
        )

    def create_merge_request_comment(
        self,
        project_id: Union[int, str],
        merge_request_iid: Union[int, str],
        body: str
    ) -> Dict:
        """
        创建合并请求评论

        Args:
            project_id: 项目 ID
            merge_request_iid: 合并请求 IID (项目内 ID)
            body: 评论内容

        Returns:
            Dict: 创建的评论信息
        """
        data = {"body": body}
        return self._make_request(
            "POST",
            f"projects/{project_id}/merge_requests/{merge_request_iid}/notes",
            data=data
        )

    def update_issue(
        self,
        project_id: Union[int, str],
        issue_iid: Union[int, str],
        data: Dict
    ) -> Dict:
        """
        更新 Issue

        Args:
            project_id: 项目 ID
            issue_iid: Issue IID (项目内 ID)
            data: 更新数据，可包含 title, description, state_event 等

        Returns:
            Dict: 更新后的 Issue 信息
        """
        return self._make_request(
            "PUT",
            f"projects/{project_id}/issues/{issue_iid}",
            data=data
        )

    def create_branch(
        self,
        project_id: Union[int, str],
        branch_name: str,
        ref: str
    ) -> Dict:
        """
        创建分支

        Args:
            project_id: 项目 ID
            branch_name: 分支名称
            ref: 基于哪个引用创建分支

        Returns:
            Dict: 创建的分支信息
        """
        data = {
            "branch": branch_name,
            "ref": ref
        }
        logger.info(f"Creating branch {branch_name} from {ref} in project {project_id}")
        return self._make_request(
            "POST",
            f"projects/{project_id}/repository/branches",
            data=data
        )

    def get_branch(
        self,
        project_id: Union[int, str],
        branch_name: str
    ) -> Dict:
        """
        获取分支信息

        Args:
            project_id: 项目 ID
            branch_name: 分支名称

        Returns:
            Dict: 分支信息
        """
        logger.info(f"Getting branch {branch_name} in project {project_id}")
        return self._make_request(
            "GET",
            f"projects/{project_id}/repository/branches/{branch_name}"
        )

    def get_pipeline(self, project_id: Union[int, str], pipeline_id: Union[int, str]) -> Dict:
        """
        获取Pipeline信息

        Args:
            project_id: 项目 ID
            pipeline_id: Pipeline ID

        Returns:
            Dict: Pipeline信息
        """
        logger.info(f"Getting pipeline {pipeline_id} in project {project_id}")
        return self._make_request(
            "GET",
            f"projects/{project_id}/pipelines/{pipeline_id}"
        )

    def get_project_pipelines(
        self,
        project_id: Union[int, str],
        updated_after: Optional[str] = None,
        per_page: int = 20
    ) -> List[Dict]:
        """
        获取项目的Pipeline列表

        Args:
            project_id: 项目 ID
            updated_after: 获取此时间之后更新的Pipeline (ISO 8601格式)
            per_page: 每页返回的数量

        Returns:
            List[Dict]: Pipeline列表
        """
        params = {"per_page": per_page}
        if updated_after:
            params["updated_after"] = updated_after

        logger.info(f"Getting pipelines for project {project_id}")
        return self._make_request(
            "GET",
            f"projects/{project_id}/pipelines",
            params=params
        ) or []

    def get_pipeline_jobs(self, project_id: Union[int, str], pipeline_id: Union[int, str]) -> List[Dict]:
        """
        获取Pipeline的作业列表

        Args:
            project_id: 项目 ID
            pipeline_id: Pipeline ID

        Returns:
            List[Dict]: 作业列表
        """
        logger.info(f"Getting jobs for pipeline {pipeline_id} in project {project_id}")
        return self._make_request(
            "GET",
            f"projects/{project_id}/pipelines/{pipeline_id}/jobs"
        ) or []

    def get_job(self, project_id: Union[int, str], job_id: Union[int, str]) -> Dict:
        """
        获取作业信息

        Args:
            project_id: 项目 ID
            job_id: 作业 ID

        Returns:
            Dict: 作业信息
        """
        logger.info(f"Getting job {job_id} in project {project_id}")
        try:
            result = self._make_request(
                "GET",
                f"projects/{project_id}/jobs/{job_id}"
            )
            if result:
                logger.info(f"成功获取作业信息: Job {job_id} - {result.get('name', 'unknown')} ({result.get('status', 'unknown')})")
                return result
            else:
                logger.warning(f"未能获取作业 {job_id} 的信息")
                return None
        except Exception as e:
            logger.error(f"获取作业信息失败: {e}")
            if self.fallback_mode:
                return None
            raise

    def get_job_log(self, project_id: Union[int, str], job_id: Union[int, str]) -> str:
        """
        获取作业日志

        Args:
            project_id: 项目 ID
            job_id: 作业 ID

        Returns:
            str: 作业日志内容
        """
        logger.info(f"Getting log for job {job_id} in project {project_id}")

        # 日志API返回的是纯文本，不是JSON
        url = f"{self.base_url}/projects/{project_id}/jobs/{job_id}/trace"
        headers = self._get_headers()

        try:
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()

            # 连接成功，更新状态
            self.is_available = True

            # 返回纯文本内容
            log_content = response.text

            # 验证日志内容
            if log_content and log_content.strip():
                logger.info(f"成功获取作业日志: Job {job_id}, 长度: {len(log_content)} 字符")
                # 记录日志的前几行用于调试
                lines = log_content.split('\n')[:5]
                logger.debug(f"日志前5行: {lines}")
                return log_content
            else:
                logger.warning(f"作业 {job_id} 的日志为空")
                return ""

        except RequestException as e:
            logger.error(f"获取作业日志失败: {e}")

            if self.fallback_mode:
                logger.warning(f"回退模式：返回空日志")
                return ""

            raise Exception(f"获取作业日志失败: {str(e)}")

    def get_project_environments(self, project_id: Union[int, str]) -> List[Dict]:
        """
        获取项目的环境列表

        Args:
            project_id: 项目 ID

        Returns:
            List[Dict]: 环境列表
        """
        logger.info(f"Getting environments for project {project_id}")
        return self._make_request(
            "GET",
            f"projects/{project_id}/environments"
        ) or []

    def retry_job(self, project_id: Union[int, str], job_id: Union[int, str]) -> Dict:
        """
        重试作业

        Args:
            project_id: 项目 ID
            job_id: 作业 ID

        Returns:
            Dict: 重试后的作业信息
        """
        logger.info(f"Retrying job {job_id} in project {project_id}")
        return self._make_request(
            "POST",
            f"projects/{project_id}/jobs/{job_id}/retry"
        )

    def cancel_job(self, project_id: Union[int, str], job_id: Union[int, str]) -> Dict:
        """
        取消作业

        Args:
            project_id: 项目 ID
            job_id: 作业 ID

        Returns:
            Dict: 取消后的作业信息
        """
        logger.info(f"Canceling job {job_id} in project {project_id}")
        return self._make_request(
            "POST",
            f"projects/{project_id}/jobs/{job_id}/cancel"
        )
