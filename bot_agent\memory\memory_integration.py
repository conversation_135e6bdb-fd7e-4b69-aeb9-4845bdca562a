"""
记忆集成模块 - 将记忆系统集成到aider-plus工作流中
"""

import logging
import os
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from .global_memory import GlobalMemoryManager
from .project_memory import ProjectMemoryManager
from .memory_analyzer import MemoryAnalyzer

logger = logging.getLogger(__name__)


class MemoryIntegration:
    """
    记忆集成模块

    负责将记忆系统集成到aider-plus的工作流中，
    在任务执行前提供上下文，在任务完成后学习和记录
    """

    def __init__(self):
        """初始化记忆集成模块"""
        self.global_memory = GlobalMemoryManager()
        self.memory_analyzer = MemoryAnalyzer()
        self.project_memories = {}  # 缓存项目记忆管理器

        logger.info("MemoryIntegration initialized")

    def get_project_memory(self, project_path: str) -> ProjectMemoryManager:
        """
        获取项目记忆管理器（带缓存）

        Args:
            project_path: 项目路径

        Returns:
            ProjectMemoryManager: 项目记忆管理器
        """
        if project_path not in self.project_memories:
            self.project_memories[project_path] = ProjectMemoryManager(project_path)
        return self.project_memories[project_path]

    def prepare_context_for_task(self, task: Dict[str, Any], project_path: str = None) -> str:
        """
        为任务准备上下文信息，包含相关的记忆内容

        Args:
            task: 任务信息
            project_path: 项目路径

        Returns:
            str: 格式化的上下文信息
        """
        context_parts = []

        # 添加全局记忆上下文
        global_context = self._get_global_context(task)
        if global_context:
            context_parts.append("# 全局工作记忆\n" + global_context)

        # 添加项目记忆上下文
        if project_path:
            project_context = self._get_project_context(task, project_path)
            if project_context:
                context_parts.append("# 项目记忆\n" + project_context)

        # 添加环境信息
        env_context = self._get_environment_context()
        if env_context:
            context_parts.append("# 环境信息\n" + env_context)

        if context_parts:
            full_context = "\n\n".join(context_parts)
            logger.info(f"Prepared context for task: {len(full_context)} characters")
            return full_context
        else:
            logger.info("No relevant memory context found for task")
            return ""

    async def learn_from_task_completion(self, task: Dict[str, Any], result: Dict[str, Any],
                                 project_path: str = None) -> None:
        """
        从任务完成中学习，更新记忆系统

        Args:
            task: 任务信息
            result: 执行结果
            project_path: 项目路径
        """
        try:
            # 使用AI分析器提取记忆
            global_memories, project_memories = self.memory_analyzer.analyze_task_completion(task, result)

            # 保存全局记忆
            for memory in global_memories:
                self._save_global_memory(memory)

            # 保存项目记忆
            if project_path and project_memories:
                project_memory = self.get_project_memory(project_path)
                for memory in project_memories:
                    self._save_project_memory(project_memory, memory)

            # 更新环境信息
            self._update_environment_info(task, result)

            logger.info(f"Learned from task completion: {len(global_memories)} global, {len(project_memories)} project memories")

        except Exception as e:
            logger.error(f"Error learning from task completion: {e}", exc_info=True)

    async def learn_from_user_feedback(self, feedback: str, context: Dict[str, Any],
                                project_path: str = None) -> None:
        """
        从用户反馈中学习

        Args:
            feedback: 用户反馈内容
            context: 反馈上下文
            project_path: 项目路径
        """
        try:
            # 使用AI分析器提取记忆
            global_memories, project_memories = self.memory_analyzer.analyze_user_feedback(feedback, context)

            # 保存全局记忆
            for memory in global_memories:
                self._save_global_memory(memory)

            # 保存项目记忆
            if project_path and project_memories:
                project_memory = self.get_project_memory(project_path)
                for memory in project_memories:
                    self._save_project_memory(project_memory, memory)

            logger.info(f"Learned from user feedback: {len(global_memories)} global, {len(project_memories)} project memories")

        except Exception as e:
            logger.error(f"Error learning from user feedback: {e}", exc_info=True)

    def get_memory_summary_for_ai(self, project_path: str = None) -> str:
        """
        获取用于AI参考的记忆摘要

        Args:
            project_path: 项目路径

        Returns:
            str: 格式化的记忆摘要
        """
        summary_parts = []

        # 全局记忆摘要
        global_summary = self.global_memory.export_memory_summary()
        if global_summary.strip():
            summary_parts.append(global_summary)

        # 项目记忆摘要
        if project_path:
            project_memory = self.get_project_memory(project_path)
            project_summary = project_memory.export_memory_summary()
            if project_summary.strip():
                summary_parts.append(project_summary)

        return "\n\n---\n\n".join(summary_parts)

    def _get_global_context(self, task: Dict[str, Any]) -> str:
        """获取全局上下文"""
        try:
            # 获取相关的工作习惯
            task_context = f"{task.get('title', '')} {task.get('description', '')}"
            relevant_habits = self.global_memory.get_relevant_habits(task_context)

            if not relevant_habits:
                return ""

            context = "## 相关工作习惯\n"
            for habit in relevant_habits[:5]:  # 最多5个相关习惯
                habit_data = habit["data"]
                context += f"- {habit['type']}: {habit_data.get('description', 'N/A')} (频率: {habit_data.get('frequency', 1)})\n"

            # 添加偏好设置摘要
            preferences = self.global_memory.get_preferences_summary()
            if preferences["summary"] != "No preferences recorded":
                context += f"\n## 偏好设置\n{preferences['summary']}\n"

            return context

        except Exception as e:
            logger.error(f"Error getting global context: {e}")
            return ""

    def _get_project_context(self, task: Dict[str, Any], project_path: str) -> str:
        """获取项目上下文"""
        try:
            project_memory = self.get_project_memory(project_path)

            # 获取架构摘要
            arch_summary = project_memory.get_architecture_summary()

            context = ""
            if arch_summary != "No architecture information recorded":
                context += f"## 项目架构\n{arch_summary}\n\n"

            # 获取相关编码规范
            task_files = self._extract_file_types_from_task(task)
            for file_type in task_files:
                standards = project_memory.get_relevant_standards(file_type)
                if standards:
                    context += f"## {file_type} 编码规范\n"
                    for standard in standards[:3]:  # 最多3个相关规范
                        standard_data = standard["data"]
                        context += f"- {standard_data.get('description', 'N/A')}\n"
                    context += "\n"

            return context

        except Exception as e:
            logger.error(f"Error getting project context: {e}")
            return ""

    def _get_environment_context(self) -> str:
        """获取环境上下文"""
        try:
            memory = self.global_memory.load_memory()
            env_info = memory.get("environment", {})

            if not env_info:
                return ""

            context = "## 环境配置\n"
            for key, value in env_info.items():
                if key != "updated_at" and value:
                    context += f"- {key}: {value}\n"

            return context

        except Exception as e:
            logger.error(f"Error getting environment context: {e}")
            return ""

    def _save_global_memory(self, memory: Dict[str, Any]) -> None:
        """保存全局记忆"""
        category = memory.get("category")
        data = memory.get("data", {})

        if category == "work_habits":
            habit_type = memory.get("type", "general")
            self.global_memory.save_work_habit(habit_type, data)
        elif category == "preferences":
            pref_category = memory.get("type", "general")
            self.global_memory.save_preference(pref_category, data)
        elif category == "coding_patterns":
            pattern_type = memory.get("type", "general")
            self.global_memory.save_common_pattern(pattern_type, data)

    def _save_project_memory(self, project_memory: ProjectMemoryManager, memory: Dict[str, Any]) -> None:
        """保存项目记忆"""
        category = memory.get("category")
        data = memory.get("data", {})

        if category == "architecture":
            project_memory.save_architecture_info(data)
        elif category == "coding_standards":
            standard_type = memory.get("type", "general")
            project_memory.save_coding_standard(standard_type, data)
        elif category == "decisions":
            project_memory.save_decision(data)
        elif category == "project_patterns":
            pattern_type = memory.get("type", "general")
            project_memory.save_project_pattern(pattern_type, data)

    def _update_environment_info(self, task: Dict[str, Any], result: Dict[str, Any]) -> None:
        """更新环境信息"""
        env_data = {}

        # 从任务和结果中提取环境信息
        if "project_path" in result:
            env_data["last_project_path"] = result["project_path"]

        # 记录最后使用的工具
        if "aider" in str(result).lower():
            env_data["last_used_tool"] = "aider"

        # 记录任务类型统计
        task_type = task.get("analysis", {}).get("task_type", "UNKNOWN")
        env_data["last_task_type"] = task_type

        if env_data:
            self.global_memory.save_environment_info(env_data)

    def _extract_file_types_from_task(self, task: Dict[str, Any]) -> List[str]:
        """从任务中提取文件类型"""
        file_types = []
        text = f"{task.get('title', '')} {task.get('description', '')}".lower()

        type_patterns = {
            "python": [".py", "python", "django", "flask"],
            "javascript": [".js", "javascript", "node", "react"],
            "typescript": [".ts", "typescript"],
            "java": [".java", "java"],
            "go": [".go", "golang"],
            "rust": [".rs", "rust"],
            "html": [".html", "html"],
            "css": [".css", "css"],
            "json": [".json", "json"],
            "yaml": [".yml", ".yaml", "yaml"],
            "markdown": [".md", "markdown"]
        }

        for file_type, patterns in type_patterns.items():
            if any(pattern in text for pattern in patterns):
                file_types.append(file_type)

        return file_types if file_types else ["general"]
