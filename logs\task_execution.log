2025-05-28 09:25:22,713 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:53 - __init__ - JobFailureAnalyzer initialized
2025-05-28 09:25:23,120 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:25:23,121 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 838209ea-2ba6-4d5f-b226-599aec2699f2
2025-05-28 09:25:27,483 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:25:38,183 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:25:38,248 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_provider_security.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_failure_analysis_error.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_job_lint_analysis.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_provider_initialization.py', 'tests\\test_job_analysis_boundary.py', 'api_proxy\\config.py', 'setup.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_sensitive_data.py', 'api_proxy\\__init__.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_failure_analysis_unit.py', 'api_proxy\\providers\\__init__.py', 'api_proxy\\models.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_proxy_service_unit.py', 'requirements.txt', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'tests\\test_job_analysis_integration.py']
2025-05-28 09:25:39,931 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - lint (Job 709)
2025-05-28 09:25:39,932 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:25:39,932 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:25:39,933 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 709
2025-05-28 09:25:39,934 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:25:39,934 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 709
2025-05-28 09:25:41,013 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: lint - failed
2025-05-28 09:25:41,721 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 8052 字符
2025-05-28 09:25:41,759 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:25:41,760 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=lint
2025-05-28 09:25:46,150 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:725 - _prepare_execution_environment - Black不可用，尝试安装...
2025-05-28 09:26:03,629 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['git']
2025-05-28 09:26:03,629 - bot_agent.handlers.job_failure_analyzer - WARNING - job_failure_analyzer.py:751 - _prepare_execution_environment - 缺失工具: ['black']
2025-05-28 09:26:03,630 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:410 - _execute_fix_commands - 检测到37个Black格式化问题，使用批量修复
2025-05-28 09:26:09,590 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:610 - _commit_fixes_to_git - 执行Git操作: 添加修改的文件 - git add .
2025-05-28 09:26:12,680 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:610 - _commit_fixes_to_git - 执行Git操作: 检查状态 - git status
2025-05-28 09:26:15,337 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:610 - _commit_fixes_to_git - 执行Git操作: 提交修复 - git commit -m "🤖 自动修复 lint 作业失败 (Job 709)"
2025-05-28 09:26:18,567 - bot_agent.handlers.job_failure_analyzer - WARNING - job_failure_analyzer.py:629 - _commit_fixes_to_git - Git commit失败: {'stdout': '', 'stderr': '', 'return_code': 1, 'command': 'powershell -Command "git commit -m "🤖 自动修复 lint 作业失败 (Job 709)""', 'cwd': '.'}
2025-05-28 09:26:25,705 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 709)
2025-05-28 09:26:33,359 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:27:44,684 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:53 - __init__ - JobFailureAnalyzer initialized
2025-05-28 09:27:45,052 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:27:45,054 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 1b197628-896b-4b68-84ff-39a43981c0b8
2025-05-28 09:27:53,830 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:28:03,881 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:28:03,939 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:28:05,482 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - test (Job 710)
2025-05-28 09:28:05,483 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:28:05,484 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:28:05,484 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 710
2025-05-28 09:28:05,485 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:28:05,485 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 710
2025-05-28 09:28:06,563 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: test - failed
2025-05-28 09:28:07,938 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 2561 字符
2025-05-28 09:28:07,953 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:28:07,954 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=test
2025-05-28 09:28:14,500 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['pytest', 'git']
2025-05-28 09:28:25,414 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - test (Job 710)
2025-05-28 09:28:30,807 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:29:55,538 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:29:55,539 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 a2b2f440-6a11-4c49-b149-57e27e6cc55e
2025-05-28 09:30:02,068 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:30:02,084 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:30:02,131 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:30:03,540 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - lint (Job 711)
2025-05-28 09:30:03,541 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:30:03,541 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:30:03,542 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 711
2025-05-28 09:30:03,542 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:30:03,543 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 711
2025-05-28 09:30:04,572 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: lint - failed
2025-05-28 09:30:05,211 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 6610 字符
2025-05-28 09:30:05,244 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:30:05,245 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=lint
2025-05-28 09:30:10,990 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['black', 'git']
2025-05-28 09:30:17,185 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 711)
2025-05-28 09:30:21,907 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:30:36,658 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:30:36,658 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 e6d9de24-0bf1-44da-a7e7-83de7dfb4ff3
2025-05-28 09:30:41,978 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:30:41,990 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:30:42,027 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:30:43,350 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - lint (Job 714)
2025-05-28 09:30:43,351 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:30:43,352 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:30:43,352 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 714
2025-05-28 09:30:43,352 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:30:43,353 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 714
2025-05-28 09:30:44,746 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: lint - failed
2025-05-28 09:30:45,149 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 6610 字符
2025-05-28 09:30:45,169 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:30:45,170 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=lint
2025-05-28 09:30:50,605 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['black', 'git']
2025-05-28 09:30:56,652 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 714)
2025-05-28 09:31:00,640 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:32:19,443 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:32:19,444 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 b65bafdf-942c-4085-a50a-812a3943791a
2025-05-28 09:32:21,844 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:32:21,871 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:32:21,933 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:32:23,574 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - test (Job 713)
2025-05-28 09:32:23,576 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:32:23,576 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:32:23,577 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 713
2025-05-28 09:32:23,577 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:32:23,578 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 713
2025-05-28 09:32:24,648 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: test - failed
2025-05-28 09:32:24,972 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 2561 字符
2025-05-28 09:32:24,983 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:32:24,984 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=test
2025-05-28 09:32:31,151 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['pytest', 'git']
2025-05-28 09:32:42,412 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - test (Job 713)
2025-05-28 09:32:46,272 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:34:13,562 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:34:13,563 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 ba2e0a8d-373f-4a60-8ac6-80375131ddb0
2025-05-28 09:34:19,439 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:34:19,454 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:34:19,509 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:34:20,878 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - test (Job 716)
2025-05-28 09:34:20,879 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:34:20,879 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:34:20,880 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 716
2025-05-28 09:34:20,880 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:34:20,880 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 716
2025-05-28 09:34:22,299 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: test - failed
2025-05-28 09:34:23,343 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 2561 字符
2025-05-28 09:34:23,354 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:34:23,355 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=test
2025-05-28 09:34:29,375 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['pytest', 'git']
2025-05-28 09:34:40,382 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - test (Job 716)
2025-05-28 09:34:47,033 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:36:16,955 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:36:16,956 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 1b32a947-e014-4b25-aca0-2f6dc44fdc00
2025-05-28 09:36:18,738 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:36:18,762 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:36:18,831 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:36:20,510 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - test (Job 719)
2025-05-28 09:36:20,511 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:36:20,511 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:36:20,512 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 719
2025-05-28 09:36:20,512 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:36:20,513 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 719
2025-05-28 09:36:21,555 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: test - failed
2025-05-28 09:36:22,275 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 2561 字符
2025-05-28 09:36:22,288 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:36:22,289 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=test
2025-05-28 09:36:29,167 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['pytest', 'git']
2025-05-28 09:36:41,387 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - test (Job 719)
2025-05-28 09:36:49,622 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:37:05,577 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:37:05,577 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 88bc6fbe-5a29-46b4-aa36-e205120fe71e
2025-05-28 09:37:11,097 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:37:11,113 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:37:11,171 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:37:12,886 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - lint (Job 717)
2025-05-28 09:37:12,887 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:37:12,888 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:37:12,888 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 717
2025-05-28 09:37:12,888 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:37:12,889 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 717
2025-05-28 09:37:13,392 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: lint - failed
2025-05-28 09:37:13,704 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 6610 字符
2025-05-28 09:37:13,734 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:37:13,735 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=lint
2025-05-28 09:37:20,876 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['black', 'git']
2025-05-28 09:37:28,386 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 717)
2025-05-28 09:37:32,182 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:38:53,636 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:38:53,637 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 592ecac9-abc2-41f3-8c76-3d6a4307d3b9
2025-05-28 09:38:59,721 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:38:59,734 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:38:59,790 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:39:01,379 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - test (Job 725)
2025-05-28 09:39:01,380 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:39:01,380 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:39:01,381 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 725
2025-05-28 09:39:01,381 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:39:01,382 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 725
2025-05-28 09:39:02,252 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: test - failed
2025-05-28 09:39:02,617 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 2561 字符
2025-05-28 09:39:02,632 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:39:02,632 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=test
2025-05-28 09:39:09,139 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['pytest', 'git']
2025-05-28 09:39:20,224 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - test (Job 725)
2025-05-28 09:39:23,957 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:40:43,798 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:40:43,799 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 c0b2a252-9541-4d70-92be-91c4807a62a8
2025-05-28 09:40:49,081 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:40:49,100 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:40:49,156 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:40:50,696 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - lint (Job 723)
2025-05-28 09:40:50,697 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:40:50,697 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:40:50,697 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 723
2025-05-28 09:40:50,698 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:40:50,698 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 723
2025-05-28 09:40:51,501 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: lint - failed
2025-05-28 09:40:51,908 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 6609 字符
2025-05-28 09:40:51,941 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:40:51,941 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=lint
2025-05-28 09:40:57,990 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['black', 'git']
2025-05-28 09:41:04,820 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 723)
2025-05-28 09:41:09,713 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:41:26,340 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:41:26,340 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 69cac594-1ea3-44f8-8b31-562b06d00131
2025-05-28 09:41:28,354 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:41:28,363 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:41:28,396 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:41:29,856 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - lint (Job 720)
2025-05-28 09:41:29,856 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:41:29,857 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:41:29,857 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 720
2025-05-28 09:41:29,857 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:41:29,857 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 720
2025-05-28 09:41:30,424 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: lint - failed
2025-05-28 09:41:30,739 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 6610 字符
2025-05-28 09:41:30,762 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:41:30,762 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=lint
2025-05-28 09:41:37,507 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['black', 'git']
2025-05-28 09:41:44,689 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 720)
2025-05-28 09:41:48,888 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:42:59,853 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:42:59,854 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 c98566aa-52bf-48b0-8eec-1dd7c8b7501d
2025-05-28 09:43:07,506 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:43:07,528 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:43:07,568 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:43:09,042 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - lint (Job 726)
2025-05-28 09:43:09,043 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:43:09,044 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:43:09,044 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 726
2025-05-28 09:43:09,045 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:43:09,045 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 726
2025-05-28 09:43:09,696 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: lint - failed
2025-05-28 09:43:10,150 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 6610 字符
2025-05-28 09:43:10,178 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:43:10,178 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=lint
2025-05-28 09:43:16,915 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['black', 'git']
2025-05-28 09:43:24,145 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - lint (Job 726)
2025-05-28 09:43:28,394 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:44:56,675 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:44:56,676 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 4efcc10f-dff5-4f17-acd6-baac8bc2872e
2025-05-28 09:45:01,671 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:45:01,690 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:45:01,749 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:45:03,256 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - test (Job 722)
2025-05-28 09:45:03,257 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:45:03,258 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:45:03,258 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 722
2025-05-28 09:45:03,259 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:45:03,260 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 722
2025-05-28 09:45:04,194 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: test - failed
2025-05-28 09:45:04,548 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 2561 字符
2025-05-28 09:45:04,561 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:45:04,562 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=test
2025-05-28 09:45:10,744 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['pytest', 'git']
2025-05-28 09:45:21,535 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - test (Job 722)
2025-05-28 09:45:26,295 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
2025-05-28 09:45:49,296 - bot_agent.engines.task_executor - INFO - task_executor.py:47 - __init__ - AiderBasedTaskExecutor initialized with projects_dir: E:\aider-git-repos\
2025-05-28 09:45:49,297 - bot_agent.engines.task_executor - INFO - task_executor.py:60 - execute_task - 开始使用Aider执行任务 c6c5e38c-b463-4175-bca3-af2ad99e559e
2025-05-28 09:45:54,023 - bot_agent.engines.task_executor - INFO - task_executor.py:232 - _execute_with_aider - 使用代码生成模型: openrouter/deepseek/deepseek-chat-v3-0324:free
2025-05-28 09:45:54,048 - bot_agent.engines.task_executor - INFO - task_executor.py:363 - _smart_file_discovery - 🔍 开始智能文件发现...
2025-05-28 09:45:54,096 - bot_agent.engines.task_executor - INFO - task_executor.py:384 - _smart_file_discovery - 智能发现了 29 个相关文件: ['tests\\test_lint_analysis_boundary.py', 'tests\\test_proxy_service_error.py', 'tests\\test_job_failure_analysis_boundary.py', 'tests\\test_job_analysis_error.py', 'api_proxy\\config.py', 'requirements.txt', 'tests\\test_job_failure_analysis_integration.py', 'tests\\test_provider_initialization.py', 'tests\\test_lint_analysis_unit.py', 'tests\\test_job_analysis_integration.py', 'tests\\test_lint_analysis_error.py', 'tests\\test_proxy_service_boundary.py', 'setup.py', 'api_proxy\\models.py', 'api_proxy\\providers\\__init__.py', 'tests\\test_proxy_service_unit.py', 'tests\\test_job_failure_analysis.py', 'tests\\test_job_failure_analysis_unit.py', 'tests\\test_job_lint_analysis.py', 'api_proxy\\__init__.py', 'tests\\test_provider_security.py', 'tests\\test_health_check.py', 'tests\\test_lint_analysis_integration.py', 'tests\\test_job_analysis_boundary.py', 'tests\\test_provider_boundary.py', 'tests\\test_job_analysis_unit.py', 'tests\\test_proxy_service_integration.py', 'tests\\test_sensitive_data.py', 'tests\\test_job_failure_analysis_error.py']
2025-05-28 09:45:55,442 - bot_agent.engines.task_executor - INFO - task_executor.py:266 - _execute_with_aider - 使用智能Aider执行任务: 作业失败分析 - test (Job 728)
2025-05-28 09:45:55,442 - bot_agent.engines.task_executor - INFO - task_executor.py:721 - _intelligent_task_execution - 🤖 开始智能任务执行...
2025-05-28 09:45:55,443 - bot_agent.engines.task_executor - INFO - task_executor.py:725 - _intelligent_task_execution - 🔍 检测到作业失败分析任务，使用专门的分析器
2025-05-28 09:45:55,443 - bot_agent.engines.task_executor - INFO - task_executor.py:1002 - _handle_job_failure_analysis - 提取到Job ID: 728
2025-05-28 09:45:55,443 - bot_agent.engines.task_executor - INFO - task_executor.py:1008 - _handle_job_failure_analysis - 从任务metadata中获取到项目ID: 9
2025-05-28 09:45:55,445 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:66 - analyze_job_failure - 开始分析作业失败: Job 728
2025-05-28 09:45:56,001 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:159 - _get_job_info - 获取到作业信息: test - failed
2025-05-28 09:45:56,782 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:177 - _get_job_log - 获取到作业日志，长度: 2561 字符
2025-05-28 09:45:56,792 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:387 - _execute_fix_commands - 设置工作目录为: .
2025-05-28 09:45:56,792 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:706 - _prepare_execution_environment - 准备执行环境: 项目目录=., 作业类型=test
2025-05-28 09:46:03,936 - bot_agent.handlers.job_failure_analyzer - INFO - job_failure_analyzer.py:749 - _prepare_execution_environment - 可用工具: ['pytest', 'git']
2025-05-28 09:46:15,137 - bot_agent.engines.task_executor - INFO - task_executor.py:280 - _execute_with_aider - 代码已提交: AI自动修改: 作业失败分析 - test (Job 728)
2025-05-28 09:46:18,801 - bot_agent.engines.task_executor - INFO - task_executor.py:306 - _execute_with_aider - 代码已推送到远程分支: aider-plus-dev
