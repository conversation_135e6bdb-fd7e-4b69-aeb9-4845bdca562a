#!/usr/bin/env python3
"""
Aider行为分析系统启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    required_packages = [
        'fastapi',
        'uvicorn[standard]',
        'jinja2',
        'python-multipart'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'uvicorn[standard]':
                import uvicorn
            else:
                __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("🔧 安装缺失的依赖包...")
        for package in missing_packages:
            print(f"   安装 {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
        print("✅ 依赖安装完成")

def create_directories():
    """创建必要的目录"""
    directories = [
        'web_ui/templates',
        'web_ui/static/css',
        'web_ui/static/js',
        'logs/conversations'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("📁 目录结构创建完成")

def show_banner():
    """显示启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🤖 Aider行为分析系统 v2.0                                  ║
║    AI编程助手行为分析和优化平台                                ║
║                                                              ║
║    ✨ 全新美化界面                                            ║
║    📊 实时性能监控                                            ║
║    🔍 智能行为分析                                            ║
║    🚀 效率优化建议                                            ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def main():
    """主函数"""
    show_banner()
    
    print("🚀 正在启动Aider行为分析系统...")
    print("=" * 60)
    
    # 检查依赖
    try:
        check_dependencies()
    except Exception as e:
        print(f"❌ 依赖安装失败: {e}")
        print("请手动安装依赖: pip install fastapi uvicorn jinja2 python-multipart")
        return
    
    # 创建目录
    create_directories()
    
    # 启动Web服务
    print("\n🌐 启动Web服务...")
    print("📊 访问地址: http://localhost:8080")
    print("\n🎯 功能特性:")
    print("  📈 实时监控    - Aider执行状态和性能指标")
    print("  📝 行为记录    - 详细的AI交互历史")
    print("  📊 性能分析    - 成功率、效率趋势分析")
    print("  🔍 智能搜索    - 快速定位问题和优化点")
    print("  📤 数据导出    - 支持JSON、CSV格式")
    print("  🎨 美观界面    - 现代化设计，响应式布局")
    
    print("\n💡 使用建议:")
    print("  - 定期查看成功率趋势，识别性能问题")
    print("  - 分析失败案例，优化提示词和配置")
    print("  - 监控Token使用，控制API成本")
    print("  - 导出数据进行深度分析")
    
    print("\n按 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        # 切换到项目根目录
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # 启动FastAPI应用
        process = subprocess.Popen([
            sys.executable, '-m', 'uvicorn',
            'web_ui.conversation_viewer_app:app',
            '--host', '0.0.0.0',
            '--port', '8080',
            '--reload',
            '--log-level', 'info'
        ])
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(3)
        
        # 自动打开浏览器
        try:
            webbrowser.open('http://localhost:8080')
            print("🌐 已自动打开浏览器")
        except:
            print("💡 请手动打开浏览器访问: http://localhost:8080")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n👋 正在停止服务...")
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        print("✅ 服务已停止")
        
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保端口8080未被占用")
        print("2. 检查Python环境和依赖")
        print("3. 确保有足够的权限")
        print("4. 检查防火墙设置")
        
        print("\n📞 获取帮助:")
        print("- 查看日志文件了解详细错误")
        print("- 检查系统资源使用情况")
        print("- 尝试使用不同端口: --port 8081")

if __name__ == "__main__":
    main()
