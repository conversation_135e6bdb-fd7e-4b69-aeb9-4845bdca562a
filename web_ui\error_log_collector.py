"""
错误日志收集器 - 全面收集和分析系统错误日志
"""

import os
import json
import logging
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
import glob
import re

logger = logging.getLogger(__name__)


@dataclass
class ErrorLogEntry:
    """错误日志条目"""
    timestamp: str
    level: str
    logger_name: str
    message: str
    file_path: str
    line_number: int
    function_name: str
    exception_type: str
    exception_message: str
    traceback: str
    context: Dict[str, Any]
    source_file: str
    session_id: Optional[str] = None
    task_id: Optional[str] = None


class ErrorLogCollector:
    """
    错误日志收集器
    
    功能：
    1. 收集所有Python日志文件中的错误
    2. 解析异常堆栈信息
    3. 分类和统计错误
    4. 提供实时错误监控
    5. 生成错误趋势分析
    """
    
    def __init__(self):
        """初始化错误日志收集器"""
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.log_dirs = [
            os.path.join(self.project_root, "logs"),
            os.path.join(self.project_root, "logs", "conversations"),
            "/var/log",  # 系统日志目录
            "."  # 当前目录
        ]
        
        # 错误模式
        self.error_patterns = {
            'python_exception': re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - ([^-]+) - (ERROR|CRITICAL) - (.+)', re.MULTILINE),
            'traceback_start': re.compile(r'Traceback \(most recent call last\):'),
            'exception_line': re.compile(r'(\w+Error|\w+Exception): (.+)'),
            'file_line': re.compile(r'File "([^"]+)", line (\d+), in (\w+)'),
        }
        
        logger.info("ErrorLogCollector initialized")
    
    def collect_all_errors(self, hours: int = 24) -> List[ErrorLogEntry]:
        """
        收集所有错误日志
        
        Args:
            hours: 收集最近几小时的错误
            
        Returns:
            List[ErrorLogEntry]: 错误日志列表
        """
        logger.info(f"开始收集最近 {hours} 小时的错误日志")
        
        all_errors = []
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 收集所有日志文件
        log_files = self._find_all_log_files()
        logger.info(f"找到 {len(log_files)} 个日志文件")
        
        for log_file in log_files:
            try:
                file_errors = self._parse_log_file(log_file, cutoff_time)
                all_errors.extend(file_errors)
                logger.debug(f"从 {log_file} 收集到 {len(file_errors)} 个错误")
            except Exception as e:
                logger.error(f"解析日志文件 {log_file} 失败: {e}")
        
        # 按时间排序
        all_errors.sort(key=lambda x: x.timestamp, reverse=True)
        
        logger.info(f"总共收集到 {len(all_errors)} 个错误")
        return all_errors
    
    def _find_all_log_files(self) -> List[str]:
        """查找所有日志文件"""
        log_files = []
        
        # 日志文件模式
        log_patterns = [
            "*.log",
            "*.txt",
            "**/*.log",
            "**/*.txt"
        ]
        
        for log_dir in self.log_dirs:
            if not os.path.exists(log_dir):
                continue
                
            for pattern in log_patterns:
                search_pattern = os.path.join(log_dir, pattern)
                files = glob.glob(search_pattern, recursive=True)
                log_files.extend(files)
        
        # 去重并过滤
        unique_files = list(set(log_files))
        valid_files = []
        
        for file_path in unique_files:
            if (os.path.isfile(file_path) and 
                os.path.getsize(file_path) > 0 and
                not any(skip in file_path for skip in ['.git', '__pycache__', 'node_modules'])):
                valid_files.append(file_path)
        
        return valid_files
    
    def _parse_log_file(self, log_file: str, cutoff_time: datetime) -> List[ErrorLogEntry]:
        """解析单个日志文件"""
        errors = []
        
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 查找错误日志条目
            error_matches = self.error_patterns['python_exception'].finditer(content)
            
            for match in error_matches:
                try:
                    timestamp_str = match.group(1)
                    logger_name = match.group(2).strip()
                    level = match.group(3)
                    message = match.group(4)
                    
                    # 解析时间戳
                    try:
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                        if timestamp < cutoff_time:
                            continue
                    except ValueError:
                        timestamp = datetime.now()
                    
                    # 查找相关的堆栈信息
                    traceback_info = self._extract_traceback(content, match.end())
                    
                    error_entry = ErrorLogEntry(
                        timestamp=timestamp.isoformat(),
                        level=level,
                        logger_name=logger_name,
                        message=message,
                        file_path=traceback_info.get('file_path', ''),
                        line_number=traceback_info.get('line_number', 0),
                        function_name=traceback_info.get('function_name', ''),
                        exception_type=traceback_info.get('exception_type', ''),
                        exception_message=traceback_info.get('exception_message', ''),
                        traceback=traceback_info.get('traceback', ''),
                        context=self._extract_context(message),
                        source_file=log_file,
                        session_id=self._extract_session_id(message),
                        task_id=self._extract_task_id(message)
                    )
                    
                    errors.append(error_entry)
                    
                except Exception as e:
                    logger.debug(f"解析错误条目失败: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"读取日志文件 {log_file} 失败: {e}")
        
        return errors
    
    def _extract_traceback(self, content: str, start_pos: int) -> Dict[str, Any]:
        """提取堆栈信息"""
        traceback_info = {
            'file_path': '',
            'line_number': 0,
            'function_name': '',
            'exception_type': '',
            'exception_message': '',
            'traceback': ''
        }
        
        # 在错误消息后查找堆栈信息
        remaining_content = content[start_pos:start_pos + 2000]  # 限制搜索范围
        
        # 查找Traceback开始
        traceback_match = self.error_patterns['traceback_start'].search(remaining_content)
        if traceback_match:
            traceback_start = traceback_match.start()
            traceback_content = remaining_content[traceback_start:traceback_start + 1500]
            traceback_info['traceback'] = traceback_content
            
            # 提取文件信息
            file_matches = self.error_patterns['file_line'].findall(traceback_content)
            if file_matches:
                last_file = file_matches[-1]  # 取最后一个文件信息
                traceback_info['file_path'] = last_file[0]
                traceback_info['line_number'] = int(last_file[1])
                traceback_info['function_name'] = last_file[2]
            
            # 提取异常信息
            exception_match = self.error_patterns['exception_line'].search(traceback_content)
            if exception_match:
                traceback_info['exception_type'] = exception_match.group(1)
                traceback_info['exception_message'] = exception_match.group(2)
        
        return traceback_info
    
    def _extract_context(self, message: str) -> Dict[str, Any]:
        """提取上下文信息"""
        context = {}
        
        # 提取常见的上下文信息
        patterns = {
            'task_id': r'task[_\s]*(\w+)',
            'session_id': r'session[_\s]*(\w+)',
            'user_id': r'user[_\s]*(\w+)',
            'project_id': r'project[_\s]*(\w+)',
            'job_id': r'job[_\s]*(\w+)',
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                context[key] = match.group(1)
        
        return context
    
    def _extract_session_id(self, message: str) -> Optional[str]:
        """提取会话ID"""
        match = re.search(r'session[_\s]*([a-zA-Z0-9_]+)', message, re.IGNORECASE)
        return match.group(1) if match else None
    
    def _extract_task_id(self, message: str) -> Optional[str]:
        """提取任务ID"""
        match = re.search(r'task[_\s]*([a-zA-Z0-9_]+)', message, re.IGNORECASE)
        return match.group(1) if match else None
    
    def get_error_statistics(self, errors: List[ErrorLogEntry]) -> Dict[str, Any]:
        """获取错误统计信息"""
        if not errors:
            return {
                'total_errors': 0,
                'by_level': {},
                'by_logger': {},
                'by_exception_type': {},
                'by_hour': {},
                'recent_errors': [],
                'top_files': {},
                'error_trend': []
            }
        
        from collections import Counter
        
        # 按级别统计
        by_level = Counter(error.level for error in errors)
        
        # 按日志器统计
        by_logger = Counter(error.logger_name for error in errors)
        
        # 按异常类型统计
        by_exception_type = Counter(error.exception_type for error in errors if error.exception_type)
        
        # 按文件统计
        by_file = Counter(error.file_path for error in errors if error.file_path)
        
        # 按小时统计
        by_hour = Counter()
        for error in errors:
            try:
                dt = datetime.fromisoformat(error.timestamp)
                hour_key = dt.strftime('%Y-%m-%d %H:00')
                by_hour[hour_key] += 1
            except:
                continue
        
        # 最近错误
        recent_errors = errors[:10]
        
        # 错误趋势（最近24小时）
        now = datetime.now()
        trend_data = []
        for i in range(24):
            hour = now - timedelta(hours=i)
            hour_key = hour.strftime('%Y-%m-%d %H:00')
            count = by_hour.get(hour_key, 0)
            trend_data.append({
                'hour': hour.strftime('%H:00'),
                'count': count
            })
        trend_data.reverse()
        
        return {
            'total_errors': len(errors),
            'by_level': dict(by_level.most_common()),
            'by_logger': dict(by_logger.most_common(10)),
            'by_exception_type': dict(by_exception_type.most_common(10)),
            'by_hour': dict(by_hour.most_common(24)),
            'recent_errors': [asdict(error) for error in recent_errors],
            'top_files': dict(by_file.most_common(10)),
            'error_trend': trend_data
        }
    
    def search_errors(self, errors: List[ErrorLogEntry], 
                     query: str = None,
                     level: str = None,
                     logger_name: str = None,
                     exception_type: str = None,
                     hours: int = None) -> List[ErrorLogEntry]:
        """搜索错误日志"""
        filtered_errors = errors
        
        # 按时间过滤
        if hours:
            cutoff = datetime.now() - timedelta(hours=hours)
            filtered_errors = [
                error for error in filtered_errors
                if datetime.fromisoformat(error.timestamp) >= cutoff
            ]
        
        # 按级别过滤
        if level:
            filtered_errors = [
                error for error in filtered_errors
                if error.level.upper() == level.upper()
            ]
        
        # 按日志器过滤
        if logger_name:
            filtered_errors = [
                error for error in filtered_errors
                if logger_name.lower() in error.logger_name.lower()
            ]
        
        # 按异常类型过滤
        if exception_type:
            filtered_errors = [
                error for error in filtered_errors
                if exception_type.lower() in error.exception_type.lower()
            ]
        
        # 按查询字符串过滤
        if query:
            query_lower = query.lower()
            filtered_errors = [
                error for error in filtered_errors
                if (query_lower in error.message.lower() or
                    query_lower in error.exception_message.lower() or
                    query_lower in error.file_path.lower())
            ]
        
        return filtered_errors


# 全局实例
global_error_collector = ErrorLogCollector()
