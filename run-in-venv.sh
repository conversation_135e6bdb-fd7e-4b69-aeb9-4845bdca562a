#!/bin/bash
# 在虚拟环境中运行Bot Agent服务

# 检查是否存在虚拟环境
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export SERVICE_ADDRESS=$(hostname -I | awk '{print $1}')
export EXTERNAL_PORT=8000
export GITLAB_WEBHOOK_TOKEN="test-webhook-token"
export GITLAB_API_URL="http://***************:5050/api/v4"
export GITLAB_API_TOKEN="glpat..."
export ALLOWED_USERS="Longer"

echo "Service will run at $SERVICE_ADDRESS:8000"
echo "GitLab Webhook URL: http://$SERVICE_ADDRESS:8000/webhook/gitlab/"
echo "GitLab Webhook Token: $GITLAB_WEBHOOK_TOKEN"
echo "GitLab API URL: $GITLAB_API_URL"
echo "GitLab API Token: $GITLAB_API_TOKEN"

# 设置Python路径，确保能找到bot-agent目录
export PYTHONPATH=$(pwd)
echo "Setting PYTHONPATH to: $PYTHONPATH"

# 确保所有必要的依赖都已安装
echo "Checking and installing required dependencies..."
pip install -r requirements.txt
pip install -r bot-agent/requirements.txt

# 检查是否启用热重载
if [[ "$*" == *"--hot-reload"* ]]; then
    # 使用uvicorn直接启动，启用热重载
    echo "Running with hot reload enabled..."
    echo "Service will be available at http://localhost:8000"
    echo "Press Ctrl+C to stop the service"

    # 监视的目录
    watch_dirs="bot_agent,aider"

    # 启动uvicorn，启用热重载
    uvicorn bot_agent.api.app:app --reload --host 0.0.0.0 --port 8000 --reload-dir bot_agent --reload-dir aider
else
    # 直接运行main.py
    echo "Running: python bot-agent/main.py"
    echo "Service will be available at http://localhost:8000"
    echo "Press Ctrl+C to stop the service"
    echo "Tip: Run with --hot-reload flag to enable hot reloading"
    python bot-agent/main.py
fi
