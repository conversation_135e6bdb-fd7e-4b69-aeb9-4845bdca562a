"""
AI 响应处理器 - 处理 AI 生成的响应并发送到相应的平台
"""

import logging
import os
from typing import Dict, Optional, Union

from bot_agent.clients.gitlab_client import GitLabClient

logger = logging.getLogger(__name__)


class AIResponseHandler:
    """
    AI 响应处理器，负责处理 AI 生成的响应并发送到相应的平台
    """

    def __init__(self):
        """初始化 AI 响应处理器"""
        self.gitlab_client = GitLabClient()
        logger.info("AI response handler initialized")

    async def process_and_send_response(
        self,
        task: Dict,
        ai_response: str,
    ) -> Dict:
        """
        处理 AI 生成的响应并发送到相应的平台

        Args:
            task: 任务对象
            ai_response: AI 生成的响应

        Returns:
            Dict: 处理结果
        """
        source = task.get("source")

        if source == "gitlab":
            return await self._handle_gitlab_response(task, ai_response)
        else:
            logger.warning(f"Unsupported source: {source}")
            return {
                "status": "error",
                "message": f"Unsupported source: {source}",
            }

    async def _handle_gitlab_response(
        self,
        task: Dict,
        ai_response: str,
    ) -> Dict:
        """
        处理 GitLab 响应

        Args:
            task: 任务对象
            ai_response: AI 生成的响应

        Returns:
            Dict: 处理结果
        """
        source_id = task.get("source_id", "")
        metadata = task.get("metadata", {})
        event_type = metadata.get("event_type")

        try:
            # 解析 source_id 获取项目 ID 和 Issue/MR ID
            if ":" not in source_id:
                logger.error(f"Invalid source_id format: {source_id}")
                return {
                    "status": "error",
                    "message": f"Invalid source_id format: {source_id}",
                }

            parts = source_id.split(":")
            project_id = parts[0]

            if event_type == "Issue Hook":
                issue_iid = parts[1]
                return await self._send_issue_response(project_id, issue_iid, ai_response)
            elif event_type == "Note Hook":
                if len(parts) < 3:
                    logger.error(f"Invalid source_id format for Note Hook: {source_id}")
                    return {
                        "status": "error",
                        "message": f"Invalid source_id format for Note Hook: {source_id}",
                    }

                issue_iid = parts[1]
                note_type = metadata.get("note_type", "Issue")

                if note_type == "Issue":
                    return await self._send_issue_response(project_id, issue_iid, ai_response)
                elif note_type == "MergeRequest":
                    mr_iid = parts[1]
                    return await self._send_mr_response(project_id, mr_iid, ai_response)
            elif event_type == "Merge Request Hook":
                mr_iid = parts[1]
                return await self._send_mr_response(project_id, mr_iid, ai_response)
            else:
                logger.warning(f"Unsupported event type: {event_type}")
                return {
                    "status": "error",
                    "message": f"Unsupported event type: {event_type}",
                }
        except Exception as e:
            logger.error(f"Error handling GitLab response: {e}", exc_info=True)
            return {
                "status": "error",
                "message": f"Error handling GitLab response: {str(e)}",
            }

    async def _send_issue_response(
        self,
        project_id: Union[int, str],
        issue_iid: Union[int, str],
        ai_response: str,
    ) -> Dict:
        """
        发送 Issue 响应

        Args:
            project_id: 项目 ID
            issue_iid: Issue IID
            ai_response: AI 生成的响应

        Returns:
            Dict: 处理结果
        """
        try:
            # 检查响应是否已经包含AI标识，避免重复添加
            if ai_response.startswith("🤖") or "AI助手" in ai_response[:50]:
                # 响应已经包含AI标识，直接发送
                response_body = ai_response
            else:
                # 添加 AI 标识前缀
                response_body = f"🤖 **AI助手回复**：\n\n{ai_response}"

            # 发送评论
            result = self.gitlab_client.create_issue_comment(
                project_id=project_id,
                issue_iid=issue_iid,
                body=response_body,
            )

            logger.info(f"Sent response to GitLab Issue {project_id}:{issue_iid}")
            return {
                "status": "success",
                "message": f"Response sent to GitLab Issue {issue_iid}",
                "result": result,
            }
        except Exception as e:
            logger.error(f"Error sending Issue response: {e}", exc_info=True)
            return {
                "status": "error",
                "message": f"Error sending Issue response: {str(e)}",
            }

    async def _send_mr_response(
        self,
        project_id: Union[int, str],
        mr_iid: Union[int, str],
        ai_response: str,
    ) -> Dict:
        """
        发送合并请求响应

        Args:
            project_id: 项目 ID
            mr_iid: 合并请求 IID
            ai_response: AI 生成的响应

        Returns:
            Dict: 处理结果
        """
        try:
            # 检查响应是否已经包含AI标识，避免重复添加
            if ai_response.startswith("🤖") or "AI助手" in ai_response[:50]:
                # 响应已经包含AI标识，直接发送
                response_body = ai_response
            else:
                # 添加 AI 标识前缀
                response_body = f"🤖 **AI助手回复**：\n\n{ai_response}"

            # 发送评论
            result = self.gitlab_client.create_merge_request_comment(
                project_id=project_id,
                merge_request_iid=mr_iid,
                body=response_body,
            )

            logger.info(f"Sent response to GitLab MR {project_id}:{mr_iid}")
            return {
                "status": "success",
                "message": f"Response sent to GitLab MR {mr_iid}",
                "result": result,
            }
        except Exception as e:
            logger.error(f"Error sending MR response: {e}", exc_info=True)
            return {
                "status": "error",
                "message": f"Error sending MR response: {str(e)}",
            }
