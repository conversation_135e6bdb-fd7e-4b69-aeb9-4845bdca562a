"""
GitLab 分支管理模块 - 提供创建分支和克隆仓库的功能
"""

import logging
import os
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

from bot_agent.clients.gitlab_client import GitLabClient

logger = logging.getLogger(__name__)

# 导入git模块，如果可用
try:
    import git
    GIT_AVAILABLE = True
except ImportError:
    logger.warning("GitPython模块未安装，将使用subprocess执行git命令")
    git = None
    GIT_AVAILABLE = False

# 检测Git可执行文件路径
GIT_EXECUTABLE = "git"  # 默认命令
GIT_INSTALLED = False

def find_git_executable():
    """查找Git可执行文件路径"""
    global GIT_EXECUTABLE, GIT_INSTALLED

    # 检查环境变量中是否有Git路径
    git_path_env = os.environ.get("GIT_PATH")
    if git_path_env and os.path.exists(git_path_env):
        logger.info(f"使用环境变量中的Git路径: {git_path_env}")
        GIT_EXECUTABLE = git_path_env
        GIT_INSTALLED = True
        return git_path_env

    # 常见的Git安装路径
    common_git_paths = [
        "git",  # 如果在PATH中
        r"C:\Program Files\Git\bin\git.exe",
        r"C:\Program Files (x86)\Git\bin\git.exe",
        r"C:\Git\bin\git.exe",
        "/usr/bin/git",
        "/usr/local/bin/git",
        "/opt/local/bin/git",
    ]

    # 尝试每个路径
    for path in common_git_paths:
        try:
            # 尝试执行git --version
            result = subprocess.run(
                [path, "--version"],
                capture_output=True,
                text=True,
                check=False,  # 不抛出异常
            )
            if result.returncode == 0:
                logger.info(f"找到Git可执行文件: {path}")
                GIT_EXECUTABLE = path
                GIT_INSTALLED = True
                return path
        except Exception as e:
            logger.debug(f"尝试路径 {path} 失败: {e}")

    logger.warning("未找到Git可执行文件，Git操作可能会失败")
    return None

# 查找Git可执行文件
find_git_executable()


class GitLabBranchManager:
    """
    GitLab 分支管理器，提供创建分支和克隆仓库的功能
    """

    def __init__(
        self,
        gitlab_client: GitLabClient,
        work_dir: Optional[str] = None,
    ):
        """
        初始化 GitLab 分支管理器

        Args:
            gitlab_client: GitLab API 客户端
            work_dir: 工作目录，如果为 None，则使用环境变量中的项目下载目录或当前目录
        """
        self.gitlab_client = gitlab_client

        # 直接使用环境变量中的项目下载目录
        projects_dir = os.environ.get("PROJECTS_DIR")
        if projects_dir:
            logger.info(f"使用环境变量中的项目下载目录: {projects_dir}")
            self.work_dir = projects_dir
        elif work_dir:
            logger.info(f"使用指定的工作目录: {work_dir}")
            self.work_dir = work_dir
        else:
            # 如果没有设置环境变量或指定工作目录，使用当前目录的父目录下的git-repos目录
            parent_dir = os.path.dirname(os.getcwd())
            self.work_dir = os.path.join(parent_dir, "git-repos")
            logger.info(f"使用默认的Git仓库目录: {self.work_dir}")

        # 确保工作目录存在
        os.makedirs(self.work_dir, exist_ok=True)
        logger.info(f"Git仓库工作目录: {self.work_dir}")

        # 确保Git用户配置正确
        self.setup_git_config()

    def setup_git_config(self):
        """
        确保Git用户配置正确
        """
        logger.info("检查Git用户配置")

        try:
            user_name = None
            user_email = None

            # 优先使用环境变量中的Git配置
            git_user_name = os.environ.get("GIT_USER_NAME")
            git_user_email = os.environ.get("GIT_USER_EMAIL")

            if git_user_name:
                logger.info(f"使用环境变量中的Git用户名: {git_user_name}")
                user_name = git_user_name

            if git_user_email:
                logger.info(f"使用环境变量中的Git用户邮箱: {git_user_email}")
                user_email = git_user_email

            # 设置环境变量，确保子进程能够使用这些配置
            if user_name:
                os.environ["GIT_AUTHOR_NAME"] = user_name
                os.environ["GIT_COMMITTER_NAME"] = user_name
                logger.info(f"已设置Git环境变量: GIT_AUTHOR_NAME={user_name}, GIT_COMMITTER_NAME={user_name}")

            if user_email:
                os.environ["GIT_AUTHOR_EMAIL"] = user_email
                os.environ["GIT_COMMITTER_EMAIL"] = user_email
                logger.info(f"已设置Git环境变量: GIT_AUTHOR_EMAIL={user_email}, GIT_COMMITTER_EMAIL={user_email}")

            return True
        except Exception as e:
            logger.error(f"设置Git配置时出错: {e}", exc_info=True)
            return False

    def create_branch(
        self,
        project_id: Union[int, str],
        branch_name: str,
        ref: str = "main",
    ) -> Dict:
        """
        创建分支

        Args:
            project_id: 项目 ID
            branch_name: 分支名称
            ref: 基于哪个引用创建分支，默认为 main

        Returns:
            Dict: 创建的分支信息，如果失败则返回None
        """
        logger.info(f"创建分支: {branch_name} 基于 {ref} 在项目 {project_id}")

        try:
            # 使用GitLab客户端的create_branch方法
            result = self.gitlab_client.create_branch(
                project_id=project_id,
                branch_name=branch_name,
                ref=ref
            )

            if result:
                logger.info(f"分支创建成功: {branch_name}")
                return result
            else:
                logger.warning(f"分支创建失败，API返回None: {branch_name}")
                return None
        except Exception as e:
            logger.error(f"创建分支时出错: {e}", exc_info=True)
            return None

    def check_branch_exists(
        self,
        project_id: Union[int, str],
        branch_name: str,
    ) -> bool:
        """
        检查分支是否存在

        Args:
            project_id: 项目 ID
            branch_name: 分支名称

        Returns:
            bool: 分支是否存在
        """
        try:
            logger.info(f"检查分支是否存在: {branch_name} in project {project_id}")
            branch = self.gitlab_client.get_branch(project_id, branch_name)

            if branch:
                logger.info(f"分支存在: {branch_name}")
                return True
            else:
                logger.info(f"分支不存在: {branch_name}")
                return False
        except Exception as e:
            logger.error(f"检查分支是否存在时出错: {e}")
            return False

    def get_project_url(
        self,
        project_id: Union[int, str],
    ) -> str:
        """
        获取项目 URL

        Args:
            project_id: 项目 ID

        Returns:
            str: 项目 URL
        """
        project = self.gitlab_client.get_project(project_id)
        if not project:
            raise ValueError(f"无法获取项目信息: {project_id}")

        # 使用 HTTP URL，避免需要 SSH 密钥
        return project.get("http_url_to_repo")

    def clone_repository(
        self,
        project_id: Union[int, str],
        branch_name: Optional[str] = None,
        target_dir: Optional[str] = None,
    ) -> Tuple[bool, str]:
        """
        克隆仓库

        Args:
            project_id: 项目 ID
            branch_name: 分支名称，如果为 None，则克隆默认分支
            target_dir: 目标目录，如果为 None，则使用项目名称作为目录名

        Returns:
            Tuple[bool, str]: 是否成功，以及结果信息
        """
        try:
            # 获取项目信息
            project = self.gitlab_client.get_project(project_id)
            if not project:
                logger.error(f"无法获取项目信息: {project_id}")

                # 尝试使用硬编码的URL
                logger.info(f"尝试使用硬编码的URL克隆项目: {project_id}")
                hardcoded_url = f"http://192.168.123.103/Longer/aider-plus.git"
                logger.info(f"硬编码URL: {hardcoded_url}")

                # 使用硬编码的项目名称
                project_name = "aider-plus"
                logger.info(f"使用硬编码的项目名称: {project_name}")

                # 设置项目URL
                project_url = hardcoded_url
            else:
                # 获取项目URL
                try:
                    project_url = self.get_project_url(project_id)
                    logger.info(f"获取到项目URL: {project_url}")

                    project_name = project.get("name")
                    logger.info(f"项目名称: {project_name}")
                except Exception as e:
                    logger.error(f"获取项目URL时出错: {e}")

                    # 尝试使用硬编码的URL
                    logger.info(f"尝试使用硬编码的URL克隆项目: {project_id}")
                    hardcoded_url = f"http://192.168.123.103/Longer/aider-plus.git"
                    logger.info(f"硬编码URL: {hardcoded_url}")

                    # 使用硬编码的项目名称
                    project_name = "aider-plus"
                    logger.info(f"使用硬编码的项目名称: {project_name}")

                    # 设置项目URL
                    project_url = hardcoded_url

            # 确定目标目录
            if not target_dir:
                target_dir = os.path.join(self.work_dir, project_name)

            logger.info(f"目标目录: {target_dir}")

            # 如果目录已存在，先检查是否是git仓库
            if os.path.exists(target_dir):
                git_dir = os.path.join(target_dir, ".git")
                if os.path.exists(git_dir):
                    logger.info(f"目录 {target_dir} 已经是一个git仓库，尝试更新")

                    # 切换到目录
                    original_dir = os.getcwd()
                    os.chdir(target_dir)

                    try:
                        # 获取当前分支
                        current_branch = subprocess.run(
                            [GIT_EXECUTABLE, "rev-parse", "--abbrev-ref", "HEAD"],
                            capture_output=True,
                            text=True,
                            check=True,
                            env=os.environ.copy(),  # 使用当前环境变量
                        ).stdout.strip()

                        logger.info(f"当前分支: {current_branch}")

                        # 如果指定了分支且不是当前分支，则切换分支
                        if branch_name and current_branch != branch_name:
                            # 检查分支是否存在
                            branches = subprocess.run(
                                [GIT_EXECUTABLE, "branch"],
                                capture_output=True,
                                text=True,
                                check=True,
                                env=os.environ.copy(),  # 使用当前环境变量
                            ).stdout

                            if f"  {branch_name}" in branches or f"* {branch_name}" in branches:
                                # 分支存在，切换到该分支
                                logger.info(f"切换到分支: {branch_name}")
                                subprocess.run(
                                    [GIT_EXECUTABLE, "checkout", branch_name],
                                    capture_output=True,
                                    text=True,
                                    check=True,
                                    env=os.environ.copy(),  # 使用当前环境变量
                                )
                            else:
                                # 分支不存在，创建并切换
                                logger.info(f"创建并切换到分支: {branch_name}")
                                subprocess.run(
                                    [GIT_EXECUTABLE, "checkout", "-b", branch_name],
                                    capture_output=True,
                                    text=True,
                                    check=True,
                                    env=os.environ.copy(),  # 使用当前环境变量
                                )

                        # 拉取最新代码
                        logger.info("拉取最新代码")
                        subprocess.run(
                            [GIT_EXECUTABLE, "pull"],
                            capture_output=True,
                            text=True,
                            check=True,
                            env=os.environ.copy(),  # 使用当前环境变量
                        )

                        # 恢复原始目录
                        os.chdir(original_dir)

                        return True, f"仓库更新成功: {target_dir}"
                    except subprocess.CalledProcessError as e:
                        # 恢复原始目录
                        os.chdir(original_dir)
                        logger.error(f"更新仓库失败: {e.stderr}")
                        return False, f"更新仓库失败: {e.stderr}"
                    except Exception as e:
                        # 恢复原始目录
                        os.chdir(original_dir)
                        logger.error(f"更新仓库失败: {e}")
                        return False, f"更新仓库失败: {str(e)}"
                else:
                    logger.warning(f"目录 {target_dir} 已存在但不是git仓库，将被删除")
                    # 删除目录
                    import shutil
                    shutil.rmtree(target_dir)

            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            # 确保Git配置正确
            self.setup_git_config()

            # 尝试使用GitPython克隆
            if GIT_AVAILABLE:
                logger.info(f"使用GitPython克隆仓库: {project_url} -> {target_dir}")
                try:
                    # 设置克隆选项
                    clone_options = {}
                    if branch_name:
                        clone_options["branch"] = branch_name

                    # 使用GitPython克隆
                    git.Repo.clone_from(project_url, target_dir, **clone_options)
                    logger.info("使用GitPython克隆成功")
                    return True, f"仓库克隆成功: {target_dir}"
                except Exception as git_error:
                    logger.error(f"使用GitPython克隆失败: {git_error}")
                    # 如果GitPython失败，尝试使用subprocess
                    logger.info("尝试使用subprocess克隆")

            # 如果GitPython不可用或失败，使用subprocess
            # 构建克隆命令
            cmd = [GIT_EXECUTABLE, "clone"]

            # 如果指定了分支，则只克隆该分支
            if branch_name:
                cmd.extend(["--branch", branch_name, "--single-branch"])

            # 添加 URL 和目标目录
            cmd.extend([project_url, target_dir])

            # 执行克隆命令
            logger.info(f"克隆仓库: {' '.join(cmd)}")

            try:
                # 尝试使用subprocess执行git clone
                clone_process = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    check=True,
                    env=os.environ.copy(),  # 使用当前环境变量，包括我们设置的Git配置
                )

                # 记录输出
                if clone_process.stdout:
                    logger.info(f"克隆输出: {clone_process.stdout}")
                if clone_process.stderr:
                    logger.warning(f"克隆错误: {clone_process.stderr}")

                return True, f"仓库克隆成功: {target_dir}"
            except subprocess.CalledProcessError as e:
                logger.error(f"克隆失败，返回码: {e.returncode}")
                logger.error(f"错误输出: {e.stderr}")
                raise e
        except subprocess.CalledProcessError as e:
            logger.error(f"克隆仓库失败: {e.stderr}")
            return False, f"克隆仓库失败: {e.stderr}"
        except Exception as e:
            logger.error(f"克隆仓库失败: {e}")
            return False, f"克隆仓库失败: {str(e)}"

    def check_repository_exists(
        self,
        project_name: str,
        target_dir: Optional[str] = None,
    ) -> bool:
        """
        检查本地是否已有仓库

        Args:
            project_name: 项目名称
            target_dir: 目标目录，如果为 None，则使用项目名称作为目录名

        Returns:
            bool: 仓库是否存在
        """
        # 确定目标目录
        if not target_dir:
            target_dir = os.path.join(self.work_dir, project_name)

        # 检查目录是否存在
        if not os.path.exists(target_dir):
            return False

        # 检查是否是 Git 仓库
        git_dir = os.path.join(target_dir, ".git")
        return os.path.exists(git_dir)

    def setup_repository(
        self,
        project_id: Union[int, str],
        branch_name: str = "aider-plus-dev",
        ref: str = "main",
        target_dir: Optional[str] = None,
    ) -> Tuple[bool, str, str]:
        """
        设置仓库，如果本地不存在则克隆，如果分支不存在则创建

        Args:
            project_id: 项目 ID
            branch_name: 分支名称，默认为 aider-plus-dev
            ref: 基于哪个引用创建分支，默认为 main
            target_dir: 目标目录，如果为 None，则使用项目名称作为目录名

        Returns:
            Tuple[bool, str, str]: 是否成功，结果信息，以及仓库路径
        """
        try:
            # 获取项目信息
            logger.info(f"获取项目信息: {project_id}")
            project = self.gitlab_client.get_project(project_id)

            if not project:
                logger.error(f"无法获取项目信息: {project_id}")
                return False, f"无法获取项目信息: {project_id}", ""

            logger.info(f"获取到项目信息: {project.get('name')} (ID: {project.get('id')})")
            logger.info(f"项目URL: {project.get('web_url')}")

            project_name = project.get("name")

            # 确定目标目录
            if not target_dir:
                target_dir = os.path.join(self.work_dir, project_name)

            logger.info(f"目标目录: {target_dir}")

            # 检查本地是否已有仓库
            if self.check_repository_exists(project_name, target_dir):
                logger.info(f"本地已有仓库: {target_dir}")

                # 检查分支是否存在
                logger.info(f"检查分支是否存在: {branch_name}")
                branch_exists = self.check_branch_exists(project_id, branch_name)

                if not branch_exists:
                    logger.info(f"分支不存在，创建分支: {branch_name}")
                    # 获取默认分支
                    default_branch = project.get("default_branch", "main")
                    logger.info(f"项目默认分支: {default_branch}")

                    # 尝试使用默认分支作为参考
                    branch_result = self.create_branch(project_id, branch_name, default_branch)

                    if branch_result:
                        logger.info(f"分支创建成功: {branch_name}")
                        # 再次检查分支是否存在
                        branch_exists = self.check_branch_exists(project_id, branch_name)
                        if branch_exists:
                            logger.info(f"确认分支已创建: {branch_name}")
                        else:
                            logger.warning(f"分支创建成功但无法确认: {branch_name}")
                    else:
                        logger.warning(f"分支创建失败: {branch_name}")
                        # 尝试使用main作为参考
                        if default_branch != "main":
                            logger.info(f"尝试使用main作为参考创建分支: {branch_name}")
                            branch_result = self.create_branch(project_id, branch_name, "main")
                            if branch_result:
                                logger.info(f"使用main作为参考创建分支成功: {branch_name}")
                            else:
                                logger.warning(f"使用main作为参考创建分支失败: {branch_name}")

                        # 尝试使用master作为参考
                        if not branch_result and default_branch != "master":
                            logger.info(f"尝试使用master作为参考创建分支: {branch_name}")
                            branch_result = self.create_branch(project_id, branch_name, "master")
                            if branch_result:
                                logger.info(f"使用master作为参考创建分支成功: {branch_name}")
                            else:
                                logger.warning(f"使用master作为参考创建分支失败: {branch_name}")
                else:
                    logger.info(f"分支已存在: {branch_name}")

                return True, f"本地已有仓库: {target_dir}", target_dir

            # 检查分支是否存在
            logger.info(f"检查分支是否存在: {branch_name}")
            branch_exists = self.check_branch_exists(project_id, branch_name)

            if not branch_exists:
                logger.info(f"分支不存在，创建分支: {branch_name}")
                # 获取默认分支
                default_branch = project.get("default_branch", "main")
                logger.info(f"项目默认分支: {default_branch}")

                # 尝试使用默认分支作为参考
                branch_result = self.create_branch(project_id, branch_name, default_branch)

                if branch_result:
                    logger.info(f"分支创建成功: {branch_name}")
                    # 再次检查分支是否存在
                    branch_exists = self.check_branch_exists(project_id, branch_name)
                    if branch_exists:
                        logger.info(f"确认分支已创建: {branch_name}")
                    else:
                        logger.warning(f"分支创建成功但无法确认: {branch_name}")
                else:
                    logger.warning(f"分支创建失败: {branch_name}")
                    # 尝试使用main作为参考
                    if default_branch != "main":
                        logger.info(f"尝试使用main作为参考创建分支: {branch_name}")
                        branch_result = self.create_branch(project_id, branch_name, "main")
                        if branch_result:
                            logger.info(f"使用main作为参考创建分支成功: {branch_name}")
                        else:
                            logger.warning(f"使用main作为参考创建分支失败: {branch_name}")

                    # 尝试使用master作为参考
                    if not branch_result and default_branch != "master":
                        logger.info(f"尝试使用master作为参考创建分支: {branch_name}")
                        branch_result = self.create_branch(project_id, branch_name, "master")
                        if branch_result:
                            logger.info(f"使用master作为参考创建分支成功: {branch_name}")
                        else:
                            logger.warning(f"使用master作为参考创建分支失败: {branch_name}")
            else:
                logger.info(f"分支已存在: {branch_name}")

            # 克隆仓库
            logger.info(f"克隆仓库: {project_name}")
            success, message = self.clone_repository(project_id, branch_name, target_dir)
            if not success:
                logger.error(f"克隆仓库失败: {message}")
                return False, message, ""

            logger.info(f"仓库克隆成功: {target_dir}")
            return True, f"仓库设置成功: {target_dir}", target_dir
        except Exception as e:
            logger.error(f"设置仓库失败: {e}", exc_info=True)
            return False, f"设置仓库失败: {str(e)}", ""
