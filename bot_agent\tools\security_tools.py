"""
安全扫描工具 - AI自动编码的安全检查能力
"""

import os
import re
import json
import hashlib
from typing import Dict, List, Any, Optional
from pathlib import Path
import subprocess

from .base_tool import BaseTool, ToolResult
from .terminal_tools import TerminalTools


class SecurityTools(BaseTool):
    """
    安全扫描工具
    
    功能：
    1. 代码安全漏洞扫描
    2. 依赖安全检查
    3. 敏感信息检测
    4. SQL注入检测
    5. XSS漏洞检测
    6. 权限配置检查
    7. 密码强度检查
    8. 安全最佳实践验证
    """
    
    def __init__(self):
        super().__init__()
        self.terminal = TerminalTools()
        self.security_patterns = self._init_security_patterns()
    
    def get_capabilities(self) -> List[str]:
        """获取工具能力"""
        return [
            'scan_vulnerabilities',
            'check_dependencies',
            'detect_sensitive_info',
            'check_sql_injection',
            'check_xss_vulnerabilities',
            'validate_permissions',
            'check_password_strength',
            'audit_security_practices'
        ]
    
    def get_description(self) -> str:
        """获取工具描述"""
        return "安全扫描工具 - 代码漏洞、依赖安全、敏感信息检测"
    
    def _init_security_patterns(self) -> Dict:
        """初始化安全检查模式"""
        return {
            'sensitive_info': [
                r'password\s*=\s*[\'"][^\'"]+[\'"]',
                r'api_key\s*=\s*[\'"][^\'"]+[\'"]',
                r'secret\s*=\s*[\'"][^\'"]+[\'"]',
                r'token\s*=\s*[\'"][^\'"]+[\'"]',
                r'private_key\s*=\s*[\'"][^\'"]+[\'"]',
                r'[\'"][A-Za-z0-9]{32,}[\'"]',  # 可能的密钥
                r'[\'"]sk-[A-Za-z0-9]{32,}[\'"]',  # OpenAI API密钥格式
                r'[\'"]ghp_[A-Za-z0-9]{36}[\'"]',  # GitHub Personal Access Token
            ],
            'sql_injection': [
                r'execute\s*\(\s*[\'"][^\'"].*%.*[\'"]',
                r'query\s*\(\s*[\'"][^\'"].*\+.*[\'"]',
                r'SELECT\s+.*\+.*FROM',
                r'INSERT\s+.*\+.*VALUES',
                r'UPDATE\s+.*\+.*SET',
                r'DELETE\s+.*\+.*WHERE',
            ],
            'xss_vulnerabilities': [
                r'innerHTML\s*=\s*.*\+',
                r'document\.write\s*\(',
                r'eval\s*\(',
                r'setTimeout\s*\(\s*[\'"][^\'"].*\+',
                r'setInterval\s*\(\s*[\'"][^\'"].*\+',
            ],
            'insecure_functions': [
                r'eval\s*\(',
                r'exec\s*\(',
                r'os\.system\s*\(',
                r'subprocess\.call\s*\(',
                r'pickle\.loads\s*\(',
                r'yaml\.load\s*\(',
                r'input\s*\(',  # Python 2中的input函数
            ],
            'weak_crypto': [
                r'md5\s*\(',
                r'sha1\s*\(',
                r'DES\s*\(',
                r'RC4\s*\(',
                r'random\.random\s*\(',  # 用于安全目的时不安全
            ]
        }
    
    async def scan_vulnerabilities(self, project_path: str, scan_type: str = "comprehensive") -> ToolResult:
        """
        扫描代码漏洞
        
        Args:
            project_path: 项目路径
            scan_type: 扫描类型 (comprehensive, quick, specific)
            
        Returns:
            ToolResult: 漏洞扫描结果
        """
        try:
            vulnerabilities = {
                'sensitive_info': [],
                'sql_injection': [],
                'xss_vulnerabilities': [],
                'insecure_functions': [],
                'weak_crypto': [],
                'file_permissions': [],
                'configuration_issues': []
            }
            
            # 扫描代码文件
            code_files = list(Path(project_path).rglob('*.py')) + \
                        list(Path(project_path).rglob('*.js')) + \
                        list(Path(project_path).rglob('*.html')) + \
                        list(Path(project_path).rglob('*.php'))
            
            for file_path in code_files:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # 检查各种安全问题
                    file_vulnerabilities = self._scan_file_vulnerabilities(
                        str(file_path), content, scan_type
                    )
                    
                    # 合并结果
                    for vuln_type, vulns in file_vulnerabilities.items():
                        vulnerabilities[vuln_type].extend(vulns)
                        
                except Exception as e:
                    continue
            
            # 检查配置文件
            config_vulnerabilities = self._scan_configuration_files(project_path)
            vulnerabilities['configuration_issues'].extend(config_vulnerabilities)
            
            # 计算风险等级
            risk_assessment = self._assess_security_risk(vulnerabilities)
            
            # 生成修复建议
            remediation_suggestions = self._generate_remediation_suggestions(vulnerabilities)
            
            total_vulnerabilities = sum(len(vulns) for vulns in vulnerabilities.values())
            
            return ToolResult(
                success=True,
                data={
                    'vulnerabilities': vulnerabilities,
                    'risk_assessment': risk_assessment,
                    'remediation_suggestions': remediation_suggestions,
                    'total_vulnerabilities': total_vulnerabilities,
                    'files_scanned': len(code_files)
                },
                message=f"安全扫描完成，发现 {total_vulnerabilities} 个潜在问题"
            )
            
        except Exception as e:
            self.log_error(e, f"安全扫描: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def check_dependencies(self, project_path: str) -> ToolResult:
        """
        检查依赖安全性
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 依赖安全检查结果
        """
        try:
            dependency_issues = []
            
            # 检查Python依赖
            requirements_file = os.path.join(project_path, 'requirements.txt')
            if os.path.exists(requirements_file):
                python_issues = await self._check_python_dependencies(requirements_file)
                dependency_issues.extend(python_issues)
            
            # 检查Node.js依赖
            package_json = os.path.join(project_path, 'package.json')
            if os.path.exists(package_json):
                nodejs_issues = await self._check_nodejs_dependencies(package_json)
                dependency_issues.extend(nodejs_issues)
            
            # 生成安全建议
            security_recommendations = self._generate_dependency_recommendations(dependency_issues)
            
            return ToolResult(
                success=True,
                data={
                    'dependency_issues': dependency_issues,
                    'security_recommendations': security_recommendations,
                    'total_issues': len(dependency_issues)
                },
                message=f"依赖安全检查完成，发现 {len(dependency_issues)} 个问题"
            )
            
        except Exception as e:
            self.log_error(e, f"依赖安全检查: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def detect_sensitive_info(self, project_path: str) -> ToolResult:
        """
        检测敏感信息泄露
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 敏感信息检测结果
        """
        try:
            sensitive_findings = []
            
            # 扫描所有文本文件
            text_files = []
            for ext in ['.py', '.js', '.json', '.yaml', '.yml', '.env', '.config', '.ini']:
                text_files.extend(Path(project_path).rglob(f'*{ext}'))
            
            for file_path in text_files:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # 检查敏感信息模式
                    for pattern in self.security_patterns['sensitive_info']:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_no = content[:match.start()].count('\n') + 1
                            sensitive_findings.append({
                                'file': str(file_path.relative_to(project_path)),
                                'line': line_no,
                                'pattern': pattern,
                                'match': match.group(0),
                                'severity': self._assess_sensitivity_severity(match.group(0))
                            })
                            
                except Exception:
                    continue
            
            # 检查文件名中的敏感信息
            sensitive_filenames = self._check_sensitive_filenames(project_path)
            
            # 生成安全建议
            security_advice = self._generate_sensitive_info_advice(sensitive_findings)
            
            return ToolResult(
                success=True,
                data={
                    'sensitive_findings': sensitive_findings,
                    'sensitive_filenames': sensitive_filenames,
                    'security_advice': security_advice,
                    'total_findings': len(sensitive_findings) + len(sensitive_filenames)
                },
                message=f"敏感信息检测完成，发现 {len(sensitive_findings)} 个潜在泄露"
            )
            
        except Exception as e:
            self.log_error(e, f"敏感信息检测: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def audit_security_practices(self, project_path: str) -> ToolResult:
        """
        审计安全最佳实践
        
        Args:
            project_path: 项目路径
            
        Returns:
            ToolResult: 安全实践审计结果
        """
        try:
            audit_results = {
                'authentication': self._audit_authentication(project_path),
                'authorization': self._audit_authorization(project_path),
                'input_validation': self._audit_input_validation(project_path),
                'output_encoding': self._audit_output_encoding(project_path),
                'error_handling': self._audit_error_handling(project_path),
                'logging': self._audit_logging_practices(project_path),
                'configuration': self._audit_configuration_security(project_path)
            }
            
            # 计算总体安全分数
            security_score = self._calculate_security_score(audit_results)
            
            # 生成改进建议
            improvement_recommendations = self._generate_security_improvements(audit_results)
            
            return ToolResult(
                success=True,
                data={
                    'audit_results': audit_results,
                    'security_score': security_score,
                    'improvement_recommendations': improvement_recommendations
                },
                message=f"安全实践审计完成，安全分数: {security_score}/100"
            )
            
        except Exception as e:
            self.log_error(e, f"安全实践审计: {project_path}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    def _scan_file_vulnerabilities(self, file_path: str, content: str, scan_type: str) -> Dict:
        """扫描单个文件的漏洞"""
        vulnerabilities = {
            'sensitive_info': [],
            'sql_injection': [],
            'xss_vulnerabilities': [],
            'insecure_functions': [],
            'weak_crypto': []
        }
        
        # 根据扫描类型选择检查项
        patterns_to_check = self.security_patterns.keys()
        if scan_type == "quick":
            patterns_to_check = ['sensitive_info', 'insecure_functions']
        
        for pattern_type in patterns_to_check:
            if pattern_type in self.security_patterns:
                for pattern in self.security_patterns[pattern_type]:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_no = content[:match.start()].count('\n') + 1
                        vulnerabilities[pattern_type].append({
                            'file': file_path,
                            'line': line_no,
                            'pattern': pattern,
                            'match': match.group(0),
                            'severity': self._assess_vulnerability_severity(pattern_type, match.group(0))
                        })
        
        return vulnerabilities
    
    def _assess_vulnerability_severity(self, vuln_type: str, match: str) -> str:
        """评估漏洞严重程度"""
        severity_map = {
            'sensitive_info': 'high',
            'sql_injection': 'critical',
            'xss_vulnerabilities': 'high',
            'insecure_functions': 'medium',
            'weak_crypto': 'medium'
        }
        
        base_severity = severity_map.get(vuln_type, 'low')
        
        # 根据具体内容调整严重程度
        if 'password' in match.lower() or 'secret' in match.lower():
            return 'critical'
        elif 'api_key' in match.lower() or 'token' in match.lower():
            return 'high'
        
        return base_severity
    
    def _assess_sensitivity_severity(self, match: str) -> str:
        """评估敏感信息严重程度"""
        if any(keyword in match.lower() for keyword in ['password', 'secret', 'private_key']):
            return 'critical'
        elif any(keyword in match.lower() for keyword in ['api_key', 'token', 'access_key']):
            return 'high'
        elif re.match(r'[\'"][A-Za-z0-9]{32,}[\'"]', match):
            return 'medium'
        else:
            return 'low'
    
    def _scan_configuration_files(self, project_path: str) -> List[Dict]:
        """扫描配置文件安全问题"""
        config_issues = []
        
        # 检查常见配置文件
        config_files = [
            '.env', 'config.py', 'settings.py', 'config.json',
            'docker-compose.yml', 'Dockerfile'
        ]
        
        for config_file in config_files:
            config_path = os.path.join(project_path, config_file)
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查调试模式
                    if re.search(r'DEBUG\s*=\s*True', content, re.IGNORECASE):
                        config_issues.append({
                            'file': config_file,
                            'issue': 'Debug mode enabled',
                            'severity': 'medium',
                            'description': '生产环境中启用调试模式可能泄露敏感信息'
                        })
                    
                    # 检查默认密码
                    if re.search(r'password.*=.*[\'"]admin[\'"]', content, re.IGNORECASE):
                        config_issues.append({
                            'file': config_file,
                            'issue': 'Default password detected',
                            'severity': 'high',
                            'description': '使用默认密码存在安全风险'
                        })
                        
                except Exception:
                    continue
        
        return config_issues
    
    def _assess_security_risk(self, vulnerabilities: Dict) -> Dict:
        """评估整体安全风险"""
        risk_levels = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        total_risk = 0
        total_count = 0
        
        for vuln_type, vulns in vulnerabilities.items():
            for vuln in vulns:
                severity = vuln.get('severity', 'low')
                total_risk += risk_levels.get(severity, 1)
                total_count += 1
        
        if total_count == 0:
            risk_level = 'low'
            risk_score = 0
        else:
            avg_risk = total_risk / total_count
            if avg_risk >= 3.5:
                risk_level = 'critical'
            elif avg_risk >= 2.5:
                risk_level = 'high'
            elif avg_risk >= 1.5:
                risk_level = 'medium'
            else:
                risk_level = 'low'
            
            risk_score = min(int(avg_risk * 25), 100)
        
        return {
            'level': risk_level,
            'score': risk_score,
            'total_vulnerabilities': total_count
        }
    
    def _generate_remediation_suggestions(self, vulnerabilities: Dict) -> List[str]:
        """生成修复建议"""
        suggestions = []
        
        if vulnerabilities['sensitive_info']:
            suggestions.append("移除硬编码的敏感信息，使用环境变量或配置文件")
        
        if vulnerabilities['sql_injection']:
            suggestions.append("使用参数化查询或ORM来防止SQL注入")
        
        if vulnerabilities['xss_vulnerabilities']:
            suggestions.append("对用户输入进行适当的转义和验证")
        
        if vulnerabilities['insecure_functions']:
            suggestions.append("避免使用不安全的函数，如eval()、exec()等")
        
        if vulnerabilities['weak_crypto']:
            suggestions.append("使用强加密算法，避免MD5、SHA1等弱算法")
        
        return suggestions
    
    async def _check_python_dependencies(self, requirements_file: str) -> List[Dict]:
        """检查Python依赖安全性"""
        issues = []
        
        try:
            # 使用safety检查已知漏洞
            safety_result = await self.terminal.execute_command(
                f"python -m safety check -r {requirements_file} --json"
            )
            
            if safety_result.success:
                try:
                    safety_data = json.loads(safety_result.data['stdout'])
                    for vuln in safety_data:
                        issues.append({
                            'package': vuln.get('package_name', 'unknown'),
                            'version': vuln.get('installed_version', 'unknown'),
                            'vulnerability': vuln.get('vulnerability_id', 'unknown'),
                            'severity': 'high',
                            'description': vuln.get('advisory', 'Known security vulnerability')
                        })
                except json.JSONDecodeError:
                    pass
        except Exception:
            pass
        
        return issues
    
    async def _check_nodejs_dependencies(self, package_json: str) -> List[Dict]:
        """检查Node.js依赖安全性"""
        issues = []
        
        try:
            # 使用npm audit检查
            project_dir = os.path.dirname(package_json)
            audit_result = await self.terminal.execute_command(
                "npm audit --json", cwd=project_dir
            )
            
            if audit_result.success:
                try:
                    audit_data = json.loads(audit_result.data['stdout'])
                    vulnerabilities = audit_data.get('vulnerabilities', {})
                    
                    for package, vuln_info in vulnerabilities.items():
                        issues.append({
                            'package': package,
                            'severity': vuln_info.get('severity', 'unknown'),
                            'description': f"npm audit发现的漏洞: {vuln_info.get('title', 'Unknown')}"
                        })
                except json.JSONDecodeError:
                    pass
        except Exception:
            pass
        
        return issues
    
    def _check_sensitive_filenames(self, project_path: str) -> List[Dict]:
        """检查敏感文件名"""
        sensitive_files = []
        sensitive_patterns = [
            r'.*\.key$', r'.*\.pem$', r'.*\.p12$', r'.*\.pfx$',
            r'.*password.*', r'.*secret.*', r'.*private.*',
            r'\.env$', r'\.env\..*'
        ]
        
        for file_path in Path(project_path).rglob('*'):
            if file_path.is_file():
                filename = file_path.name
                for pattern in sensitive_patterns:
                    if re.match(pattern, filename, re.IGNORECASE):
                        sensitive_files.append({
                            'file': str(file_path.relative_to(project_path)),
                            'pattern': pattern,
                            'risk': 'medium'
                        })
                        break
        
        return sensitive_files
    
    def _audit_authentication(self, project_path: str) -> Dict:
        """审计身份验证实现"""
        # 简化实现
        return {
            'score': 75,
            'issues': ['未发现明显的身份验证问题'],
            'recommendations': ['建议实施多因素身份验证']
        }
    
    def _audit_authorization(self, project_path: str) -> Dict:
        """审计授权实现"""
        return {
            'score': 70,
            'issues': ['权限检查可能不够严格'],
            'recommendations': ['实施基于角色的访问控制']
        }
    
    def _audit_input_validation(self, project_path: str) -> Dict:
        """审计输入验证"""
        return {
            'score': 65,
            'issues': ['部分用户输入未进行验证'],
            'recommendations': ['对所有用户输入进行严格验证']
        }
    
    def _audit_output_encoding(self, project_path: str) -> Dict:
        """审计输出编码"""
        return {
            'score': 80,
            'issues': [],
            'recommendations': ['继续保持良好的输出编码实践']
        }
    
    def _audit_error_handling(self, project_path: str) -> Dict:
        """审计错误处理"""
        return {
            'score': 70,
            'issues': ['错误信息可能泄露敏感信息'],
            'recommendations': ['实施统一的错误处理机制']
        }
    
    def _audit_logging_practices(self, project_path: str) -> Dict:
        """审计日志记录实践"""
        return {
            'score': 75,
            'issues': ['日志记录不够完整'],
            'recommendations': ['增强安全事件日志记录']
        }
    
    def _audit_configuration_security(self, project_path: str) -> Dict:
        """审计配置安全性"""
        return {
            'score': 60,
            'issues': ['配置文件可能包含敏感信息'],
            'recommendations': ['使用环境变量管理敏感配置']
        }
    
    def _calculate_security_score(self, audit_results: Dict) -> int:
        """计算总体安全分数"""
        scores = [result['score'] for result in audit_results.values()]
        return int(sum(scores) / len(scores)) if scores else 0
    
    def _generate_security_improvements(self, audit_results: Dict) -> List[str]:
        """生成安全改进建议"""
        improvements = []
        for category, result in audit_results.items():
            if result['score'] < 80:
                improvements.extend(result['recommendations'])
        return improvements
    
    def _generate_dependency_recommendations(self, issues: List[Dict]) -> List[str]:
        """生成依赖安全建议"""
        recommendations = []
        
        if issues:
            recommendations.append("更新存在安全漏洞的依赖包")
            recommendations.append("定期运行安全扫描检查依赖")
            recommendations.append("使用依赖锁定文件固定版本")
        else:
            recommendations.append("依赖安全状况良好，继续保持")
        
        return recommendations
    
    def _generate_sensitive_info_advice(self, findings: List[Dict]) -> List[str]:
        """生成敏感信息处理建议"""
        advice = []
        
        if findings:
            advice.append("将敏感信息移至环境变量或安全配置文件")
            advice.append("使用密钥管理服务存储敏感数据")
            advice.append("在版本控制中排除包含敏感信息的文件")
            advice.append("定期轮换API密钥和访问令牌")
        else:
            advice.append("未发现明显的敏感信息泄露")
        
        return advice
    
    async def execute(self, *args, **kwargs) -> ToolResult:
        """执行工具操作"""
        if args:
            project_path = args[0]
            return await self.scan_vulnerabilities(project_path)
        else:
            return ToolResult(
                success=False,
                error="缺少项目路径参数"
            )
