/**
 * 分析链路可视化组件
 * 提供结构化的问题分析展示
 */

class AnalysisChain {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentSession = null;
        this.analysisData = null;
    }

    /**
     * 渲染分析链路
     * @param {Object} sessionData - 会话数据
     */
    renderAnalysisChain(sessionData) {
        this.currentSession = sessionData;
        this.analysisData = this.extractAnalysisData(sessionData);

        const chainHTML = this.buildChainHTML();
        this.container.innerHTML = chainHTML;

        // 添加交互事件
        this.attachEventListeners();
    }

    /**
     * 从会话数据中提取分析链路
     */
    extractAnalysisData(sessionData) {
        const rounds = sessionData.rounds || [];

        return {
            problemIdentification: this.extractProblemIdentification(sessionData, rounds),
            solutionDesign: this.extractSolutionDesign(rounds),
            executionProcess: this.extractExecutionProcess(rounds),
            resultEvaluation: this.extractResultEvaluation(sessionData, rounds)
        };
    }

    /**
     * 提取问题识别阶段
     */
    extractProblemIdentification(sessionData, rounds) {
        const firstRound = rounds[0];
        const taskAnalysis = sessionData.metadata?.task_analysis || {};

        return {
            originalProblem: sessionData.task_title || '未知问题',
            problemType: taskAnalysis.task_type || '未分类',
            complexity: taskAnalysis.estimated_time || '未评估',
            keywords: taskAnalysis.keywords || [],
            risks: taskAnalysis.risks || [],
            userInput: firstRound?.prompt || '',
            aiUnderstanding: this.extractAIUnderstanding(firstRound?.response || ''),
            confidence: taskAnalysis.confidence || 0
        };
    }

    /**
     * 提取解决方案设计阶段
     */
    extractSolutionDesign(rounds) {
        const designRounds = rounds.filter(r =>
            r.round_name?.includes('分析') ||
            r.round_name?.includes('设计') ||
            r.round_name?.includes('规划')
        );

        return {
            approaches: this.extractApproaches(designRounds),
            tools: this.extractToolsUsed(rounds),
            strategy: this.extractStrategy(designRounds),
            alternatives: this.extractAlternatives(designRounds)
        };
    }

    /**
     * 提取执行过程阶段
     */
    extractExecutionProcess(rounds) {
        const executionRounds = rounds.filter(r =>
            r.round_name?.includes('执行') ||
            r.round_name?.includes('实现') ||
            r.round_name?.includes('修改')
        );

        return {
            steps: this.extractExecutionSteps(executionRounds),
            challenges: this.extractChallenges(rounds),
            adaptations: this.extractAdaptations(rounds),
            timeline: this.buildTimeline(rounds)
        };
    }

    /**
     * 提取结果评估阶段
     */
    extractResultEvaluation(sessionData, rounds) {
        const lastRound = rounds[rounds.length - 1];
        const successfulRounds = rounds.filter(r => r.status === 'success').length;
        const totalRounds = rounds.length;

        return {
            finalStatus: sessionData.status || 'unknown',
            successRate: totalRounds > 0 ? (successfulRounds / totalRounds * 100).toFixed(1) : 0,
            totalTime: sessionData.total_duration || 0,
            achievements: this.extractAchievements(rounds),
            lessons: this.extractLessons(rounds),
            improvements: this.extractImprovements(rounds),
            finalResult: sessionData.final_result || lastRound?.response || ''
        };
    }

    /**
     * 构建分析链路HTML
     */
    buildChainHTML() {
        const { problemIdentification, solutionDesign, executionProcess, resultEvaluation } = this.analysisData;

        return `
            <div class="analysis-chain">
                <div class="chain-header">
                    <h3><i class="fas fa-link"></i> 问题分析链路</h3>
                    <div class="chain-summary">
                        <span class="badge bg-primary">${problemIdentification.problemType}</span>
                        <span class="badge bg-info">置信度: ${problemIdentification.confidence}%</span>
                        <span class="badge bg-success">成功率: ${resultEvaluation.successRate}%</span>
                    </div>
                </div>

                <div class="chain-steps">
                    ${this.buildStepHTML('1', '问题识别', 'fa-search', problemIdentification, 'problem')}
                    ${this.buildStepHTML('2', '解决方案', 'fa-lightbulb', solutionDesign, 'solution')}
                    ${this.buildStepHTML('3', '执行过程', 'fa-cogs', executionProcess, 'execution')}
                    ${this.buildStepHTML('4', '结果评估', 'fa-chart-line', resultEvaluation, 'result')}
                </div>

                <div class="chain-insights">
                    ${this.buildInsightsHTML()}
                </div>
            </div>
        `;
    }

    /**
     * 构建单个步骤HTML
     */
    buildStepHTML(number, title, icon, data, type) {
        return `
            <div class="chain-step" data-step="${type}">
                <div class="step-header" onclick="toggleStep('${type}')">
                    <div class="step-number">${number}</div>
                    <div class="step-title">
                        <i class="fas ${icon}"></i>
                        ${title}
                    </div>
                    <div class="step-toggle">
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
                <div class="step-content" id="step-${type}">
                    ${this.buildStepContent(type, data)}
                </div>
            </div>
        `;
    }

    /**
     * 构建步骤内容
     */
    buildStepContent(type, data) {
        switch (type) {
            case 'problem':
                return this.buildProblemContent(data);
            case 'solution':
                return this.buildSolutionContent(data);
            case 'execution':
                return this.buildExecutionContent(data);
            case 'result':
                return this.buildResultContent(data);
            default:
                return '<p>暂无数据</p>';
        }
    }

    /**
     * 构建问题识别内容
     */
    buildProblemContent(data) {
        return `
            <div class="problem-analysis">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-exclamation-circle"></i> 原始问题</h6>
                        <div class="content-box">
                            <p>${data.originalProblem}</p>
                            <div class="meta-info">
                                <span class="badge bg-secondary">${data.problemType}</span>
                                <span class="badge bg-warning">复杂度: ${data.complexity}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-brain"></i> AI理解</h6>
                        <div class="content-box">
                            <p>${data.aiUnderstanding}</p>
                            <div class="confidence-bar">
                                <div class="progress">
                                    <div class="progress-bar" style="width: ${data.confidence}%">
                                        ${data.confidence}% 置信度
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                ${data.keywords.length > 0 ? `
                    <div class="keywords-section">
                        <h6><i class="fas fa-tags"></i> 关键词</h6>
                        <div class="keywords">
                            ${data.keywords.map(k => `<span class="keyword-tag">${k}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}
                ${data.risks.length > 0 ? `
                    <div class="risks-section">
                        <h6><i class="fas fa-exclamation-triangle"></i> 风险识别</h6>
                        <ul class="risk-list">
                            ${data.risks.map(r => `<li>${r}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 构建解决方案内容
     */
    buildSolutionContent(data) {
        return `
            <div class="solution-design">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-route"></i> 解决策略</h6>
                        <div class="content-box">
                            <p>${data.strategy || '未明确指定策略'}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-tools"></i> 使用工具</h6>
                        <div class="content-box">
                            ${data.tools.length > 0 ?
                                data.tools.map(tool => `<span class="tool-tag">${tool}</span>`).join('') :
                                '<p>未识别到特定工具</p>'
                            }
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-sitemap"></i> 方法选择</h6>
                        <div class="content-box">
                            ${data.approaches.length > 0 ?
                                `<ul>${data.approaches.map(a => `<li>${a}</li>`).join('')}</ul>` :
                                '<p>未明确指定方法</p>'
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 构建执行过程内容
     */
    buildExecutionContent(data) {
        console.log('构建执行内容，时间线数据:', data.timeline); // 调试日志

        return `
            <div class="execution-process">
                <div class="timeline-section">
                    <h6><i class="fas fa-clock"></i> 执行时间线</h6>
                    <div class="timeline">
                        ${data.timeline && data.timeline.length > 0 ?
                            data.timeline.map(item => `
                                <div class="timeline-item ${item.status || 'unknown'}">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">${item.title || '未知步骤'}</div>
                                        <div class="timeline-time">${item.time || '未知时间'}</div>
                                        <div class="timeline-duration">${(item.duration || 0).toFixed(1)}s</div>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="timeline-empty"><p>暂无执行步骤记录</p></div>'
                        }
                    </div>
                </div>

                <div class="execution-summary">
                    <h6><i class="fas fa-list-check"></i> 执行步骤概览</h6>
                    <div class="steps-overview">
                        ${data.steps && data.steps.length > 0 ?
                            data.steps.map(step => `
                                <div class="step-item ${step.status || 'unknown'}">
                                    <span class="step-number">${step.step}</span>
                                    <span class="step-name">${step.name || '未命名步骤'}</span>
                                    <span class="step-duration">${(step.duration || 0).toFixed(1)}s</span>
                                    <span class="step-status-badge badge bg-${step.status === 'success' ? 'success' : step.status === 'failed' ? 'danger' : 'secondary'}">
                                        ${step.status || 'unknown'}
                                    </span>
                                </div>
                            `).join('') :
                            '<p>暂无步骤记录</p>'
                        }
                    </div>
                </div>

                ${data.challenges && data.challenges.length > 0 ? `
                    <div class="challenges-section">
                        <h6><i class="fas fa-mountain"></i> 遇到的挑战</h6>
                        <div class="challenges-list">
                            ${data.challenges.map(c => `
                                <div class="challenge-item">
                                    <div class="challenge-problem">${c.problem || '未知问题'}</div>
                                    <div class="challenge-solution">${c.solution || '暂无解决方案'}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 构建结果评估内容
     */
    buildResultContent(data) {
        return `
            <div class="result-evaluation">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-trophy"></i> 执行结果</h6>
                        <div class="result-summary">
                            <div class="result-status ${data.finalStatus}">
                                <i class="fas ${data.finalStatus === 'success' ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                ${data.finalStatus === 'success' ? '成功完成' : '执行失败'}
                            </div>
                            <div class="result-metrics">
                                <div class="metric">
                                    <span class="metric-label">成功率:</span>
                                    <span class="metric-value">${data.successRate}%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">总耗时:</span>
                                    <span class="metric-value">${data.totalTime.toFixed(1)}s</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-lightbulb"></i> 经验总结</h6>
                        <div class="lessons-learned">
                            ${data.lessons.length > 0 ?
                                data.lessons.map(lesson => `<div class="lesson-item">${lesson}</div>`).join('') :
                                '<p>暂无经验总结</p>'
                            }
                        </div>
                    </div>
                </div>

                ${data.achievements.length > 0 ? `
                    <div class="achievements-section">
                        <h6><i class="fas fa-star"></i> 主要成果</h6>
                        <ul class="achievements-list">
                            ${data.achievements.map(a => `<li>${a}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}

                ${data.improvements.length > 0 ? `
                    <div class="improvements-section">
                        <h6><i class="fas fa-arrow-up"></i> 改进建议</h6>
                        <ul class="improvements-list">
                            ${data.improvements.map(i => `<li>${i}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // 辅助方法
    extractAIUnderstanding(response) {
        // 从AI响应中提取理解内容
        const lines = response.split('\n').slice(0, 3);
        return lines.join(' ').substring(0, 200) + '...';
    }

    extractApproaches(rounds) {
        // 从轮次中提取解决方法
        return rounds.map(r => r.round_name).filter(Boolean);
    }

    extractToolsUsed(rounds) {
        // 从轮次中提取使用的工具
        const tools = new Set();
        rounds.forEach(r => {
            if (r.response?.includes('git ')) tools.add('Git');
            if (r.response?.includes('python ')) tools.add('Python');
            if (r.response?.includes('npm ')) tools.add('NPM');
            if (r.response?.includes('docker ')) tools.add('Docker');
        });
        return Array.from(tools);
    }

    extractStrategy(rounds) {
        // 提取整体策略
        const firstRound = rounds[0];
        if (firstRound?.response) {
            const lines = firstRound.response.split('\n');
            const strategyLine = lines.find(line =>
                line.includes('策略') || line.includes('方法') || line.includes('approach')
            );
            return strategyLine || '渐进式问题解决';
        }
        return '渐进式问题解决';
    }

    extractAlternatives(rounds) {
        // 提取备选方案
        return [];
    }

    extractExecutionSteps(rounds) {
        return rounds.map((r, i) => ({
            step: i + 1,
            name: r.round_name,
            status: r.status,
            duration: r.duration
        }));
    }

    extractChallenges(rounds) {
        return rounds
            .filter(r => r.status === 'failed' || r.error_message)
            .map(r => ({
                problem: r.error_message || '执行失败',
                solution: '重试或调整策略'
            }));
    }

    extractAdaptations(rounds) {
        return [];
    }

    buildTimeline(rounds) {
        return rounds.map((r, index) => {
            let timeStr = '未知时间';
            try {
                if (r.timestamp) {
                    timeStr = new Date(r.timestamp).toLocaleTimeString('zh-CN');
                }
            } catch (e) {
                timeStr = `第${index + 1}步`;
            }

            return {
                title: r.round_name || `第${r.round_number || index + 1}轮`,
                time: timeStr,
                duration: r.duration || 0,
                status: r.status || 'unknown'
            };
        });
    }

    extractAchievements(rounds) {
        const successfulRounds = rounds.filter(r => r.status === 'success');
        return successfulRounds.map(r => `完成${r.round_name || '任务步骤'}`);
    }

    extractLessons(rounds) {
        const lessons = [];
        const failedRounds = rounds.filter(r => r.status === 'failed');
        if (failedRounds.length > 0) {
            lessons.push(`遇到${failedRounds.length}次失败，需要改进错误处理`);
        }
        if (rounds.length > 5) {
            lessons.push('任务复杂度较高，可考虑分解为更小的子任务');
        }
        return lessons;
    }

    extractImprovements(rounds) {
        const improvements = [];
        const avgDuration = rounds.reduce((sum, r) => sum + r.duration, 0) / rounds.length;
        if (avgDuration > 30) {
            improvements.push('优化响应时间，考虑使用更快的模型');
        }
        return improvements;
    }

    buildInsightsHTML() {
        return `
            <div class="insights-panel">
                <h6><i class="fas fa-chart-pie"></i> 分析洞察</h6>
                <div class="insights-grid">
                    <div class="insight-card">
                        <div class="insight-title">效率分析</div>
                        <div class="insight-content">
                            平均每轮耗时 ${(this.analysisData.resultEvaluation.totalTime / this.currentSession.rounds.length).toFixed(1)}s
                        </div>
                    </div>
                    <div class="insight-card">
                        <div class="insight-title">成功模式</div>
                        <div class="insight-content">
                            ${this.analysisData.resultEvaluation.successRate}% 的轮次成功完成
                        </div>
                    </div>
                    <div class="insight-card">
                        <div class="insight-title">改进空间</div>
                        <div class="insight-content">
                            ${this.analysisData.resultEvaluation.improvements.length > 0 ?
                                this.analysisData.resultEvaluation.improvements[0] :
                                '表现良好，继续保持'
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    attachEventListeners() {
        // 添加步骤展开/收起功能
        window.toggleStep = (stepType) => {
            const content = document.getElementById(`step-${stepType}`);
            const toggle = document.querySelector(`[data-step="${stepType}"] .step-toggle i`);

            if (content.style.display === 'none' || !content.style.display) {
                content.style.display = 'block';
                toggle.className = 'fas fa-chevron-up';
            } else {
                content.style.display = 'none';
                toggle.className = 'fas fa-chevron-down';
            }
        };
    }
}

// 导出类
window.AnalysisChain = AnalysisChain;
