"""
任务模型 - 定义任务相关的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union

from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskCreate(BaseModel):
    """任务创建请求模型"""
    title: str = Field(..., description="任务标题")
    description: str = Field(..., description="任务描述")
    source: str = Field(..., description="任务来源 (gitlab, dify, etc.)")
    source_id: str = Field(..., description="来源系统中的任务 ID")
    labels: Optional[List[str]] = Field(None, description="任务标签")
    metadata: Optional[Dict[str, Union[str, int, float, bool, List, Dict]]] = Field(
        None, description="额外的元数据"
    )


class TaskResponse(BaseModel):
    """任务响应模型"""
    id: str = Field(..., description="任务 ID")
    title: str = Field(..., description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    status: TaskStatus = Field(..., description="任务状态")
    source: str = Field(..., description="任务来源")
    source_id: str = Field(..., description="来源系统中的任务 ID")
    labels: Optional[List[str]] = Field(None, description="任务标签")
    target_component: Optional[str] = Field(None, description="目标组件")
    created_at: str = Field(..., description="创建时间 (ISO 格式)")
    updated_at: str = Field(..., description="更新时间 (ISO 格式)")
    completed_at: Optional[str] = Field(None, description="完成时间 (ISO 格式)")
    error: Optional[str] = Field(None, description="错误信息")
    result: Optional[Dict[str, Union[str, int, float, bool, List, Dict]]] = Field(
        None, description="任务结果"
    )


class TaskUpdate(BaseModel):
    """任务更新请求模型"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    result: Optional[Dict[str, Union[str, int, float, bool, List, Dict]]] = Field(
        None, description="任务结果"
    )
    error: Optional[str] = Field(None, description="错误信息")


class TaskFilter(BaseModel):
    """任务过滤器模型"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    source: Optional[str] = Field(None, description="任务来源")
    target_component: Optional[str] = Field(None, description="目标组件")
    labels: Optional[List[str]] = Field(None, description="任务标签")
    created_after: Optional[datetime] = Field(None, description="创建时间下限")
    created_before: Optional[datetime] = Field(None, description="创建时间上限")


class TaskEvent(BaseModel):
    """任务事件模型"""
    task_id: str = Field(..., description="任务 ID")
    event_type: str = Field(..., description="事件类型")
    timestamp: str = Field(..., description="事件时间 (ISO 格式)")
    data: Dict[str, Union[str, int, float, bool, List, Dict]] = Field(
        ..., description="事件数据"
    )
